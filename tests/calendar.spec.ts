import { test, expect } from '@playwright/test';
import { login, takeScreenshot } from './utils';

test.describe('Calendar Page', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await page.goto('/calendar', { waitUntil: 'networkidle' });
    await takeScreenshot(page, 'calendar', '00-calendar-page-loaded');
  });

  test('should display static elements and initial calendar view', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'Calendar' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Create' })).toBeVisible(); // 'Create' is a link wrapping a button

    // Common calendar elements (these are typical for libraries like FullCalendar/React Big Calendar)
    // Adjust selectors if the actual implementation differs.
    await expect(page.getByRole('button', { name: /today/i })).toBeVisible({ timeout: 10000 });
    await expect(page.getByRole('button', { name: /previous/i })).toBeVisible(); // Often an arrow button
    await expect(page.getByRole('button', { name: /next/i })).toBeVisible();   // Often an arrow button
    
    // Check for view-switching buttons
    await expect(page.getByRole('button', { name: /month/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /week/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /day/i })).toBeVisible();
    
    // Check for a displayed date range/title (common pattern for calendar headers)
    // This selector is a guess and might need to be very specific.
    // e.g., page.locator('.rbc-toolbar-label') or page.locator('.fc-toolbar-title')
    await expect(page.locator('h2, .rbc-toolbar-label, .fc-toolbar-title').first()).toBeVisible(); 
    await takeScreenshot(page, 'calendar', '01-initial-calendar-view');
  });

  test('should navigate calendar view using Next, Previous, and Today buttons', async ({ page }) => {
    // Get initial date range text
    const dateDisplayElement = page.locator('h2, .rbc-toolbar-label, .fc-toolbar-title').first();
    const initialDateText = await dateDisplayElement.textContent();

    // Click Next
    await page.getByRole('button', { name: /next/i }).click();
    await page.waitForTimeout(500); // Allow time for view to update
    const nextDateText = await dateDisplayElement.textContent();
    expect(nextDateText).not.toEqual(initialDateText);
    await takeScreenshot(page, 'calendar', '02-calendar-next-view');

    // Click Previous twice to go past the initial view
    await page.getByRole('button', { name: /previous/i }).click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: /previous/i }).click();
    await page.waitForTimeout(500);
    const previousDateText = await dateDisplayElement.textContent();
    expect(previousDateText).not.toEqual(nextDateText);
    await takeScreenshot(page, 'calendar', '03-calendar-previous-view');
    
    // Click Today
    await page.getByRole('button', { name: /today/i }).click();
    await page.waitForTimeout(500);
    const todayDateText = await dateDisplayElement.textContent();
    // Today's date text might be the same as initial if the test starts on the current month/week/day
    // So, a direct non-equality might not always hold. Just ensure it runs.
    expect(dateDisplayElement).toBeVisible(); 
    await takeScreenshot(page, 'calendar', '04-calendar-today-view');
  });

  test('should switch between Month, Week, and Day views', async ({ page }) => {
    // Assuming default view is Month or Week. Click Day view.
    await page.getByRole('button', { name: /day/i }).click();
    await page.waitForTimeout(500); // Allow view to render
    // Add an assertion specific to day view if possible, e.g., presence of time slots column
    await expect(page.locator('.rbc-time-header-gutter, .fc-timegrid-slot-label').first()).toBeVisible({timeout: 10000});
    await takeScreenshot(page, 'calendar', '05-calendar-day-view');

    await page.getByRole('button', { name: /week/i }).click();
    await page.waitForTimeout(500);
    // Add an assertion specific to week view, e.g., 7 day columns or similar
    await expect(page.locator('.rbc-time-header-content, .fc-col-header-cell').locator(':scope > a').first()).toBeVisible({timeout: 10000}); // Check for day headers in week view
    await takeScreenshot(page, 'calendar', '06-calendar-week-view');

    await page.getByRole('button', { name: /month/i }).click();
    await page.waitForTimeout(500);
    // Add an assertion specific to month view, e.g., a grid of days
    await expect(page.locator('.rbc-month-view, .fc-daygrid-day').first()).toBeVisible({timeout: 10000});
    await takeScreenshot(page, 'calendar', '07-calendar-month-view');
  });
});
