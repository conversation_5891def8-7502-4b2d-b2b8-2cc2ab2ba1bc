name: Deploy Frontend to S3

on:
  push:
    branches:
      - staging # Set this to the branch you want to deploy from

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18" # Specify your Node.js version here

      - name: Install Dependencies
        run: |
          git config --global url."https://${{ secrets.PAT }}@github.com/".insteadOf ssh://**************/
          git config --global url."https://${{ secrets.PAT }}@github.com/".insteadOf https://github.com/
          npm ci

      - name: Build
        run: |
          node -v
          npm -v
          npm run build

      - name: Upload to S3
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --acl public-read --follow-symlinks --delete --exclude 'strapi/*' --exclude 'sampleimage/*' --exclude 'generated-reports/*'
        env:
          AWS_S3_BUCKET: ${{ secrets.STAGING_S3_BUCKET_NAME }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          SOURCE_DIR: "dist" # The directory Vite outputs the build to

      - name: Invalidate CloudFront
        uses: chetan/invalidate-cloudfront-action@v2
        env:
          DISTRIBUTION: ${{ secrets.STAGING_DISTRIBUTION }}
          PATHS: "/index.html"
          AWS_REGION: "ap-southeast-1"
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}