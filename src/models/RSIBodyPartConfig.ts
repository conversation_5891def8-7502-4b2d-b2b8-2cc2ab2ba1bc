import QuestionConfig from "./QuestionConfig";
import RSIBodyPart from "./RSIBodyPart";

export default interface RsiBodyPartConfig {
  id: number;
  bodyPart: string;
  bodyPartText: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  topOffset: number;
  leftOffset: number;
  question_configs: QuestionConfig[];
  mirror: boolean;
  isFront: boolean;
  rsiBodyPart?: RSIBodyPart;
}
