import { ElCourseProgress } from "../pages/Dashboard/learningCenter/Course/CourseType";
import Appointment from "./Appointment";
import Employee from "./Employee";
import { Organization } from "./Organization";
import WorkingHour from "./WorkingHours";



// Role interface based on the data structure
export interface Role {
  id: number;
  name: string;
  description: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}


// Main User interface with all properties from the data
export default interface User {
  id: number;
  username: string;
  email: string;
  provider: string;
  confirmed: boolean;
  blocked: boolean;
  createdAt: string;
  updatedAt: string;
  phone?: string | null;
  timezone: string | null;
  role: Role;
  organization: Organization;
  working_hours?: WorkingHour[];
  appointments?: Appointment[];
  managees?: User[];
  manager?: User | null;
  employee: Employee | null;
  vendor: boolean | null;
  ElCourseProgress?: ElCourseProgress[];
  vendorOrganisation: VendorOrganisation | null;
  vendorAssignment: VendorAssignment | null;
}

interface VendorOrganisation {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

interface VendorAssignment {
  id: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}