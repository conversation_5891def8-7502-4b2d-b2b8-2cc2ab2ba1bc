import MLTypography from "../ui/MLTypography/MLTypography";
import { ImageToggleContext } from "./ImageToggleButtonGroup";
import { Stack, useTheme, useMediaQuery, Box, Skeleton } from "@mui/material";
import { PropsWithChildren, useContext, useState } from "react";
import { SxProps, Theme } from '@mui/material/styles';

interface ResponsiveWidth {
  xs?: string;
  sm?: string;
  md?: string;
  lg?: string;
  xl?: string;
}

interface ImageToggleButtonProps {
  value: string;
  label: string;
  imageSrc?: string;
  width?: string | ResponsiveWidth;
  height?: string;
}

const ImageToggleButton = (
  props: PropsWithChildren<ImageToggleButtonProps>,
) => {
  const { value, label, imageSrc, width = "18rem", height = "auto" } = props; // Default width and height added
  const context = useContext(ImageToggleContext);
  const active = value === context?.value;
  const theme = useTheme();
  const [isLoaded, setIsLoaded] = useState(false);

  const handleImageLoad = () => {
    setIsLoaded(true);
  };

  const isXs = useMediaQuery(theme.breakpoints.only("xs"));
  const isSm = useMediaQuery(theme.breakpoints.only("sm"));
  const isMd = useMediaQuery(theme.breakpoints.only("md"));
  const isLg = useMediaQuery(theme.breakpoints.only("lg"));
  const isXl = useMediaQuery(theme.breakpoints.only("xl"));

  let computedWidth = typeof width === "string" ? width : "18rem";
  if (typeof width === "object") {
    if (isXs) {
      computedWidth = width.xs ?? computedWidth;
    } else if (isSm) {
      computedWidth = width.sm ?? computedWidth;
    } else if (isMd) {
      computedWidth = width.md ?? computedWidth;
    } else if (isLg) {
      computedWidth = width.lg ?? computedWidth;
    } else if (isXl) {
      computedWidth = width.xl ?? computedWidth;
    }
  }

  const buttonStyle = {
    display: "flex",
    flexDirection: "column" as const,
    backgroundColor: "transparent",
    padding: 0,
    border: !imageSrc
      ? `0.5px solid #C1C1C1`
      : `2px solid ${active ? theme.palette.primary.main : "transparent"}`,
    borderRadius: "0.5rem",
    cursor: "pointer",
    width: computedWidth, // Use width prop here
    height: height, // Use height prop here
  };

  const imageStyle = {
    width: "100%",
    height: "auto",
    border: !active ? `0.5px solid #C1C1C1` : undefined,
    borderRadius: !imageSrc ? "0.5rem" : active ? "0.5rem 0.5rem 0 0" : "0.5rem",
    display: isLoaded ? 'block' : 'none',
  };

  return (
    <button onClick={() => context?.onChange(value)} style={buttonStyle}>
      {/* {imageSrc && <img src={imageSrc} style={imageStyle} alt={label} />} */}
      <Box
        component="img"
        onLoad={handleImageLoad}
        sx={imageStyle}
        src={imageSrc}
      />
      {!isLoaded && (
        <Skeleton
          animation="wave"
          variant="rounded"
          sx={{
            display: isLoaded ? 'none' : 'block',
            width: width as string,
            height: {
              xs: "130px",
              sm: "240px",
              md: "250px",
            },
          }}
        />
      )}
      <Stack
        direction={"row"}
        width={"100%"}
        justifyContent={"center"}
        alignItems={"center"}
        sx={{
          backgroundColor: active ? theme.palette.primary.main : undefined,
          paddingX: "0.5rem",
          minHeight: "3.125rem",
          flex: 1,
          borderRadius: !imageSrc ? "0.5rem" : undefined,
        }}
      >
        <MLTypography
          color={active ? "white" : undefined}
          fontWeight={600}
          sx={{
            lineHeight: "1",
          }}
        >
          {label}
        </MLTypography>
      </Stack>
    </button>
  );
};

export default ImageToggleButton;
