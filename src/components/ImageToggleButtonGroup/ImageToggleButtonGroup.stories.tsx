import ImageToggleButton from "./ImageToggleButton";
import ImageToggleButtonGroup from "./ImageToggleButtonGroup";
import { Box, SxProps, Theme } from "@mui/material";
import { resourceUsage } from "process";
import { PropsWithChildren } from "react";

export default {
  component: ImageToggleButtonGroup,
};

const sx: SxProps<Theme> = {
  width: "100%",
  padding: 1,
  borderRadius: "inherit",
  borderTopLeftRadius: 0,
  borderTopRightRadius: 0,
  flexGrow: 0,
  height: "3rem",
  display: "flex",
  fontWeight: "600",
  justifyContent: "center",
  alignItems: "center",
};

/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */
export const Primary = {
  render: () => (
    <ImageToggleButtonGroup
      value={"one"}
      onChange={() => {
        return;
      }}
    >
      <ImageToggleButton value={"one"} label="One"></ImageToggleButton>
      <ImageToggleButton
        value={"two"}
        label="Sitting in the middle of primary and secondary screens"
      ></ImageToggleButton>
      <ImageToggleButton
        value={"three"}
        label="Three"
        imageSrc="http://localhost:1337/uploads/Ext_Reach_Fwd_Image_7ff49ba6c0.png"
      ></ImageToggleButton>
    </ImageToggleButtonGroup>
  ),
};
