import { Stack } from "@mui/material";
import { PropsWithChildren, createContext, useState } from "react";

interface ImageToggleButtonGroupProps {
  value: string;
  onChange: (value: string) => void;
}

type ToggleControlValues = ImageToggleButtonGroupProps;

export const ImageToggleContext = createContext<ToggleControlValues | null>(
  null,
);

const ImageToggleButtonGroup = (
  props: PropsWithChildren<ImageToggleButtonGroupProps>,
) => {
  const { value, onChange, children } = props;

  return (
    <ImageToggleContext.Provider
      value={{
        value,
        onChange,
      }}
    >
      <Stack direction={"row"} gap={4} flexWrap="wrap">
        {children}
      </Stack>
    </ImageToggleContext.Provider>
  );
};

export default ImageToggleButtonGroup;
