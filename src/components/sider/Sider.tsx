import { <PERSON>r<PERSON><PERSON><PERSON><PERSON>, ErLogout } from "@mindlens/ergo-icons";
import { ChevronRight, Menu } from "@mui/icons-material";
import ChevronLeft from "@mui/icons-material/ChevronLeft";
import Dashboard from "@mui/icons-material/Dashboard";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import ListOutlined from "@mui/icons-material/ListOutlined";
import Logout from "@mui/icons-material/Logout";
import PersonIcon from "@mui/icons-material/Person";
import { Stack } from "@mui/material";
import Box from "@mui/material/Box";
import Collapse from "@mui/material/Collapse";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import List from "@mui/material/List";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Paper from "@mui/material/Paper";
import Tooltip from "@mui/material/Tooltip";
import {
  CanAccess,
  ITreeMenu,
  useIsExistAuthentication,
  useLogout,
  useTitle,
  useTranslate,
  useRouterType,
  useLink,
  useMenu,
  useRefineContext,
  useActiveAuthProvider,
  pickNotDeprecated,
  useWarnAboutChange,
  useGo,
} from "@refinedev/core";
import {
  RefineThemedLayoutV2SiderProps,
  ThemedTitleV2,
  useThemedLayoutContext,
} from "@refinedev/mui";
import React, { CSSProperties, useContext, useState } from "react";
import { Link, LinkProps, useLocation } from "react-router-dom";
import refreshIcon from '../../assets/icons/button-refresh-one.svg'
import { ViewModeContext } from "../../contexts/ViewModeContext/ViewModeContext";

export const CustomSider: React.FC<RefineThemedLayoutV2SiderProps> = ({
  Title: TitleFromProps,
  render,
  meta,
  activeItemDisabled = false,
}) => {

  const { isEmployeeView, toggleViewMode } = useContext(ViewModeContext);

  const handleViewModeChange = () => {
    toggleViewMode();
  };


  const {
    siderCollapsed,
    setSiderCollapsed,
    mobileSiderOpen,
    setMobileSiderOpen,
  } = useThemedLayoutContext();

  const drawerWidth = () => {
    if (siderCollapsed) return 60;
    return 220;
  };

  const t = useTranslate();
  const routerType = useRouterType();
  // const Link = useLink();
  // const { Link: LegacyLink } = useRouterContext();
  // const ActiveLink = routerType === "legacy" ? LegacyLink : Link;

  const ActiveLink = React.forwardRef<HTMLAnchorElement, LinkProps>((props, ref) => {
    return <Link ref={ref} {...props} />;
  });

  const { hasDashboard } = useRefineContext();
  const translate = useTranslate();
  const go = useGo();

  const { menuItems, selectedKey, defaultOpenKeys } = useMenu({ meta });
  const isExistAuthentication = useIsExistAuthentication();
  const TitleFromContext = useTitle();
  const authProvider = useActiveAuthProvider();
  const { warnWhen, setWarnWhen } = useWarnAboutChange();
  const { mutate: mutateLogout } = useLogout({
    v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
  });

  const [open, setOpen] = useState<{ [k: string]: any }>({});

  const enableHoverExpand = true;

  React.useEffect(() => {
    setOpen((previous) => {
      const previousKeys: string[] = Object.keys(previous);
      const previousOpenKeys = previousKeys.filter((key) => previous[key]);

      const uniqueKeys = new Set([...previousOpenKeys, ...defaultOpenKeys]);
      const uniqueKeysRecord = Object.fromEntries(
        Array.from(uniqueKeys.values()).map((key) => [key, true]),
      );
      return uniqueKeysRecord;
    });
  }, [defaultOpenKeys]);

  const RenderToTitle = TitleFromProps ?? TitleFromContext ?? ThemedTitleV2;

  const handleClick = (key: string) => {
    setOpen({ ...open, [key]: !open[key] });
  };

  const renderTreeView = (tree: ITreeMenu[], selectedKey?: string) => {
    return tree.map((item: ITreeMenu) => {
      const { icon, label, route, name, children, parentName, meta, options } =
        item;
      const isOpen = open[item.key || ""] || false;

      const isSelected = item.key === selectedKey;
      const isNested = !(
        pickNotDeprecated(meta?.parent, options?.parent, parentName) ===
        undefined
      );

      // might want to look if this code is still needed
      if (children.length > 0) {
        return (
          <CanAccess
            key={item.key}
            resource={name.toLowerCase()}
            action="list"
            params={{
              resource: item,
            }}
          >
            <div key={item.key}>
              <Tooltip
                title={label ?? name}
                placement="right"
                disableHoverListener={enableHoverExpand || !siderCollapsed}
                arrow
              >
                <ListItemButton
                  onClick={() => {
                    if (siderCollapsed) {
                      setSiderCollapsed(false);
                      if (!isOpen) {
                        handleClick(item.key || "");
                      }
                    } else {
                      handleClick(item.key || "");
                    }
                  }}
                  sx={{
                    pl: isNested ? 4 : 2,
                    justifyContent: "center",
                    marginTop: "8px",
                  }}
                >
                  <ListItemIcon
                    sx={{
                      justifyContent: "center",
                      minWidth: "24px",
                      transition: "margin-right 0.3s",
                      marginRight: siderCollapsed ? "0px" : "12px",
                      color: "currentColor",
                    }}
                  >
                    {icon ?? <ListOutlined />}
                  </ListItemIcon>
                  <ListItemText
                    primary={label}
                    primaryTypographyProps={{
                      noWrap: true,
                      fontSize: "16px",
                    }}
                  />
                  {isOpen ? (
                    <ExpandLess
                      sx={{
                        color: "text.icon",
                      }}
                    />
                  ) : (
                    <ExpandMore
                      sx={{
                        color: "text.icon",
                      }}
                    />
                  )}
                </ListItemButton>
              </Tooltip>
              {!siderCollapsed && (
                <Collapse
                  in={open[item.key || ""]}
                  timeout="auto"
                  unmountOnExit
                >
                  <List component="div" disablePadding>
                    {renderTreeView(children, selectedKey)}
                  </List>
                </Collapse>
              )}
            </div>
          </CanAccess>
        );
      }
      // might want to look if this code is still needed

      const linkStyle: CSSProperties =
        activeItemDisabled && isSelected ? { pointerEvents: "none" } : {};

      return (
        <CanAccess
          key={item.key}
          resource={name.toLowerCase()}
          action="list"
          params={{ resource: item }}
        >
          <Tooltip
            title={label ?? name}
            placement="right"
            disableHoverListener={enableHoverExpand || !siderCollapsed}
            arrow
          >
            <ListItemButton
              selected={isSelected}
              style={linkStyle}
              onClick={() => {
                go({ to: route });
                setMobileSiderOpen(false);
              }}
              sx={{
                pl: isNested ? 4 : 2,
                py: isNested ? 1.25 : 1,
                justifyContent: "center",
                color: "text.primary",
                flex: 0,
                "&.MuiListItemButton-root.Mui-selected": {
                  backgroundColor: "#ffe2e2",
                  borderRadius: "8px",
                },
                gap: { xs: 1, md: 0 },
              }}
            >
              <ListItemIcon
                sx={{
                  justifyContent: "center",
                  transition: "margin-right 0.3s",
                  marginRight: siderCollapsed ? "0px" : "12px",
                  minWidth: "24px",
                  color: "currentColor",
                }}
              >
                {icon ?? <ListOutlined />}
              </ListItemIcon>
              <ListItemText
                primary={label}
                primaryTypographyProps={{
                  noWrap: true,
                  fontSize: "16px",
                  fontWeight: isSelected ? 600 : 400,
                }}
              />
            </ListItemButton>

          </Tooltip>
        </CanAccess>
      );
    });
  };

  const dashboard = hasDashboard ? (
    <CanAccess resource="dashboard" action="list">
      <Tooltip
        title={translate("dashboard.title", "Dashboard")}
        placement="right"
        disableHoverListener={enableHoverExpand || !siderCollapsed}
        arrow
      >
        <ListItemButton
          component={ActiveLink}
          to="/"
          selected={selectedKey === "/"}
          onClick={() => {
            setMobileSiderOpen(false);
          }}
          sx={{
            pl: 2,
            py: 1,
            justifyContent: "center",
            color: selectedKey === "/" ? "primary.main" : "text.primary",
          }}
        >
          <ListItemIcon
            sx={{
              justifyContent: "center",
              minWidth: "24px",
              transition: "margin-right 0.3s",
              marginRight: siderCollapsed ? "0px" : "12px",
              color: "currentColor",
              fontSize: "16px",
            }}
          >
            <Dashboard />
          </ListItemIcon>
          <ListItemText
            primary={translate("dashboard.title", "Dashboard")}
            primaryTypographyProps={{
              noWrap: true,
              fontSize: "16px",
            }}
          />
        </ListItemButton>
      </Tooltip>
    </CanAccess>
  ) : null;

  const handleLogout = () => {
    if (warnWhen) {
      const confirm = window.confirm(
        t(
          "warnWhenUnsavedChanges",
          "Are you sure you want to leave? You have unsaved changes.",
        ),
      );

      if (confirm) {
        setWarnWhen(false);
        mutateLogout();
      }
    } else {
      localStorage.removeItem('userDetails')
      // localStorage.removeItem('currentOrganization')
      mutateLogout();
    }
  };

  const profile = isExistAuthentication && (
    <Tooltip
      title={t("buttons.profile", "Profile")}
      placement="right"
      disableHoverListener={enableHoverExpand || !siderCollapsed}
      arrow
    >
      <ListItemButton
        key="profile"
        onClick={() => {
          go({ to: "/profile" })
        }}
        sx={{
          justifyContent: "center",
          flex: 0,
          gap: { xs: 1, md: 0 },
        }}
      >
        <ListItemIcon
          sx={{
            justifyContent: "center",
            minWidth: "24px",
            transition: "margin-right 0.3s",
            marginRight: siderCollapsed ? "0px" : "12px",
            color: "currentColor",
          }}
        >
          {/* <PersonIcon /> */}
          <ErProfile fontSize={32} />
        </ListItemIcon>
        <ListItemText
          primary={t("buttons.profile", "Profile")}
          primaryTypographyProps={{
            noWrap: true,
            fontSize: "16px",
          }}
        />
      </ListItemButton>
    </Tooltip>
  );

  const logout = isExistAuthentication && (
    <Tooltip
      title={t("buttons.logout", "Logout")}
      placement="right"
      disableHoverListener={enableHoverExpand || !siderCollapsed}
      arrow
    >
      <ListItemButton
        key="logout"
        onClick={() => handleLogout()}
        sx={{
          justifyContent: "center",
          flex: 0,
          gap: { xs: 1, md: 0 },
        }}
      >
        <ListItemIcon
          sx={{
            justifyContent: "center",
            minWidth: "24px",
            transition: "margin-right 0.3s",
            marginRight: siderCollapsed ? "0px" : "12px",
            color: "currentColor",
          }}
        >
          <ErLogout fontSize={32} />
        </ListItemIcon>
        <ListItemText
          primary={t("buttons.logout", "Logout")}
          primaryTypographyProps={{
            noWrap: true,
            fontSize: "16px",
          }}
        />
      </ListItemButton>
    </Tooltip>
  );

  const EmployeePortalButton = isExistAuthentication && (
    <Tooltip
      title="Employee portal"
      placement="right"
      disableHoverListener={enableHoverExpand || !siderCollapsed}
      arrow
    >
      <ListItemButton
        key="employee-portal"
        onClick={handleViewModeChange}
        sx={{
          justifyContent: "center",
          flex: 0,
          gap: { xs: 1, md: 0 },
          backgroundColor: '#FFE8E8',
          ':hover': {
            backgroundColor: '#FFE8E8',
          }
        }}
      >
        <ListItemIcon
          sx={{
            justifyContent: "center",
            minWidth: "24px",
            transition: "margin-right 0.3s",
            marginRight: siderCollapsed ? "0px" : "12px",
            color: "currentColor",
          }}
        >
          <img src={refreshIcon} width={24} height={26} alt="Employee Portal" />
        </ListItemIcon>
        <ListItemText
          primary="Employee portal"
          primaryTypographyProps={{
            noWrap: true,
            fontSize: "16px",
          }}
        />
      </ListItemButton>
    </Tooltip>
  );


  const items = renderTreeView(menuItems, selectedKey);

  const renderSider = () => {
    if (render) {
      return render({
        dashboard,
        logout,
        items,
        collapsed: siderCollapsed,
      });
    }
    return (
      <Stack
        direction="column"
        // divider={<Divider orientation="horizontal" />}
        height={"100%"}
      >
        {dashboard}
        <Stack direction="column" flex={1} justifyContent={"flex-start"}>
          {items}
        </Stack>
        <Stack direction="column" flex={1} justifyContent={"flex-end"}>
          {profile}
          {logout}
          {EmployeePortalButton}
        </Stack>
      </Stack>
    );
  };

  const drawer = (
    <List
      disablePadding
      sx={{
        flexGrow: 1,
        paddingTop: "1rem",
        height: "100%",
        paddingX: "6px",
      }}
    >
      {renderSider()}
    </List>
  );

  const location = useLocation();
  const shouldRenderMenu = location.pathname.split('/').length <= 2 ? true : false;

  return (
    <>
      <Box
        sx={{
          width: 60,
          display: { xs: "none", md: "block" },
          transition: "width 0.3s ease",
        }}
      />
      <Box
        sx={{
          position: "absolute",
          display: { xs: shouldRenderMenu ? "block" : "none", md: "none" },
          alignItems: "center",
          padding: "24px",
        }}
      >
        <IconButton
          sx={{
            marginTop: "6px"
          }}
          onClick={() => setMobileSiderOpen(true)}
        >
          <Menu />
        </IconButton>
      </Box>
      <Box component="nav">
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          sx={{
            display: { xs: "block", md: "none" },
            "& .MuiDrawer-paper": {
              width: 180,
              overflow: "hidden",
              transition: "width 200ms cubic-bezier(0.4, 0, 0.6, 1) 0ms",
              boxShadow: "0px 4px 34px 0px rgba(0, 0, 0, 0.10)",
              borderRight: 0
            },
          }}
          open={mobileSiderOpen}
          onClose={() => setMobileSiderOpen(false)}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile
          }}
        >
          <Paper
            elevation={0}
            sx={{
              fontSize: "16px",
              width: "100%",
              display: "flex",
              flexShrink: 0,
              alignItems: "center",
              justifyContent: "center",
              paddingLeft: 0,
              paddingRight: 0,
              variant: "outlined",
              borderRadius: 0,
              position: "static",
              overflow: "auto",
            }}
          >
            {TitleFromProps && <TitleFromProps collapsed={false} />}
          </Paper>
          {drawer}
        </Drawer>

        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: "none", md: "block" },
            "& .MuiDrawer-paper": {
              width: drawerWidth(),
              overflow: "hidden",
              transition: "width 200ms cubic-bezier(0.4, 0, 0.6, 1) 0ms",
              boxShadow: "0px 4px 34px 0px rgba(0, 0, 0, 0.10)",
              borderRight: 0
            },
          }}
          open
          onMouseEnter={() => {
            if (enableHoverExpand) setSiderCollapsed(false);
          }}
          onMouseLeave={() => {
            if (enableHoverExpand) setSiderCollapsed(true);
          }}
        >
          <Paper
            elevation={0}
            sx={{
              fontSize: "16px",
              width: "100%",
              display: "flex",
              flexShrink: 0,
              alignItems: "center",
              justifyContent: "center",
              paddingLeft: 0,
              paddingRight: 0,
              variant: "outlined",
              borderRadius: 0,
              position: "static",
              overflow: "auto",
            }}
          >
            <RenderToTitle collapsed={siderCollapsed} />
            {!enableHoverExpand ? (
              <IconButton
                size="small"
                onClick={() => setSiderCollapsed(!siderCollapsed)}
                sx={{
                  position: "absolute",
                  right: "-17px",
                }}
              >
                {!siderCollapsed ? <ChevronLeft /> : <ChevronRight />}
              </IconButton>
            ) : null}
          </Paper>
          <Box
            sx={{
              flexGrow: 1,
              overflowX: "hidden",
              overflowY: "auto",
            }}
          >
            {drawer}
          </Box>
        </Drawer>
      </Box>
    </>
  );
};
