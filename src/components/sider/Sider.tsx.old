// import { Avatar, Stack, Typography } from "@mui/material";
// import { useGetIdentity } from "@refinedev/core";
// import { HamburgerMenu, ThemedSiderV2 } from "@refinedev/mui";

// type IUser = {
//   id: number;
//   name: string;
//   avatar: string;
// };

// export const CustomSider = () => {
//   const { data: user } = useGetIdentity<IUser>();
//   return (
//     <ThemedSiderV2
//       Title={() => (
//         <Stack paddingLeft={4} justifyContent={"center"}>
//           <HamburgerMenu />
//         </Stack>
//       )}
//       render={({ items, logout, collapsed }) => {
//         return (
//           <Stack
//             direction={"column"}
//             height={"100%"}
//             gap={2}
//             justifyContent={"space-between"}
//           >
//             <div>{items}</div>
//             <div>
//               {(user?.avatar || user?.name) && (
//                 <Stack
//                   direction="row"
//                   gap="16px"
//                   alignItems="center"
//                   justifyContent="flex-start"
//                   paddingX={2}
//                   paddingY={1}
//                 >
//                   <Avatar
//                     src={user?.avatar}
//                     alt={user?.name}
//                     style={{
//                       width: "1.5rem",
//                       height: "1.5rem",
//                     }}
//                   />
//                   {user?.name && !collapsed ? (
//                     <MLTypography
//                       sx={{
//                         display: {
//                           xs: "none",
//                           sm: "inline-block",
//                         },
//                       }}
//                       variant="subtitle2"
//                     >
//                       {user?.name}
//                     </MLTypography>
//                   ) : null}
//                 </Stack>
//               )}
//               {logout}
//             </div>
//           </Stack>
//         );
//       }}
//     />
//   );
// };
