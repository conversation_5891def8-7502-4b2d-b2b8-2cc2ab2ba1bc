import styled from "@emotion/styled";
import { WeekDays } from "../../../pages/AuthScreens/components/WorkingHoursBox/types";
import MLTextField from "../../ui/MLTextField/MLTextField";
import {
  Autocomplete,
  AutocompleteRenderInputParams,
  Checkbox,
  Chip,
  IconButton,
  SxProps,
} from "@mui/material";
import React, { ReactNode, useState } from "react";
import DeleteCrossIcon from "../../../assets/icons/DeleteCrossIcon";
import UncheckedIcon from "../../../assets/icons/UncheckedIcon";
import CheckedIcon from "../../../assets/icons/CheckedIcon";


const StyledAutocomplete = styled(Autocomplete)(({ theme }) => ({
  '.MuiOutlinedInput-root': {
    borderRadius: "10px",
    padding: "5px"
  },
  '& .MuiAutocomplete-paper': {
    backgroundColor: '#F4F0FF',
  },
  '& .MuiAutocomplete-option': {
    '&[data-focus="true"]': {
      backgroundColor: '#E8E0FF',
    },
    '&[aria-selected="true"]': {
      backgroundColor: '#FFFFFF',
      '&:hover': {
        backgroundColor: '#E8E0FF',
      },
    },
  },
  '& .MuiAutocomplete-tag': {
    backgroundColor: '#FFFFFF',
    border: '1px solid #D0D0D0',
    borderRadius: '4px',
    color: 'black',
    '& .MuiChip-deleteIcon': {
      color: '#666666',
      '&:hover': {
        color: '#000000',
      },
    },
  },
}));

export default function DaysAutocomplete(props: {
  days: string[];
  selectedDays: string[];
  setSelectedDays: React.Dispatch<string[]>;
  sx?: SxProps;
}) {
  const { days, selectedDays, setSelectedDays, sx } = props;
  return (
    <StyledAutocomplete
      renderInput={function (params: AutocompleteRenderInputParams): ReactNode {
        return <MLTextField {...params}></MLTextField>;
      }}
      multiple
      size="small"
      options={days}
      defaultValue={[]}
      value={selectedDays}
      onChange={(e, value: any) => setSelectedDays(value)}
      renderTags={(value: any, getTagProps) =>
        value.map((option: any, index: any) => (
          <Chip
            label={option}
            {...getTagProps({ index })}
            deleteIcon={
              <IconButton

              >
                <DeleteCrossIcon />
              </IconButton>
            }
          />
        ))
      }
      renderOption={(props, option: any, { selected }) => (
        <li
          {...props}
          className={selected ? 'option-selected' : "option"}
          style={{
            backgroundColor: selected ? '#FFFFFF' : undefined,
            color: 'black',
          }}
        >
          <Checkbox
            icon={<UncheckedIcon />}
            checkedIcon={<CheckedIcon />}
            style={{ marginRight: 8 }}
            checked={selected}
          />
          {option}
        </li>
      )}
      sx={{
        ...sx,
      }}
    ></StyledAutocomplete>
  );
}
