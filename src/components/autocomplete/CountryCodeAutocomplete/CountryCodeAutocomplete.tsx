import { InputLabel, Stack, Typography, styled } from "@mui/material";
import { countries } from "./countries";
import Autocomplete, {
  AutocompleteChangeDetails,
  AutocompleteChangeReason,
} from "@mui/material/Autocomplete";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import * as React from "react";

export interface CountryType {
  code: string;
  label: string;
  phone: string;
  suggested?: boolean;
}

interface CountryCodeSelectProps {
  value?: CountryType;
  disabled?: boolean;
  onChange:
  | ((
    event: React.SyntheticEvent<Element, Event>,
    value: CountryType | null,
    reason: AutocompleteChangeReason,
    details?: AutocompleteChangeDetails<any> | undefined,
  ) => void)
  | undefined;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  disabledTextColor?: string; // Add new prop for disabled text color
}

const StyledInputLabel = styled(InputLabel)(({ theme }) => ({
  position: 'static',
  transform: 'none',
  textAlign: 'left',
  fontSize: '1rem',
  color: theme.palette.text.primary,
}));

// Custom styled TextField to handle disabled text styling
const StyledTextField = styled(TextField, {
  shouldForwardProp: (prop) => prop !== 'disabledTextColor',
})<{ disabledTextColor?: string }>(({ theme, disabledTextColor }) => ({
  '& .MuiOutlinedInput-root.Mui-disabled .MuiInputBase-input': {
    color: disabledTextColor || '#6B7280',
    WebkitTextFillColor: disabledTextColor || '#6B7280',
  },
}));

export default function CountryCodeAutocomplete({
  value,
  onChange,
  disabled,
  onBlur,
  disabledTextColor = '#6B7280', // Default to specified color
}: CountryCodeSelectProps) {
  return (
    <Stack gap="8px" >
      <StyledInputLabel
        sx={{
          fontSize: '16px',
          fontStyle: 'normal',
          fontWeight: 500,
          lineHeight: '120%',
        }}
      >
        Country code
      </StyledInputLabel>
      <Autocomplete
        id="country-code-select"
        value={value}
        disabled={disabled}
        size="small"
        onChange={onChange}
        onBlur={onBlur}
        options={countries}
        autoHighlight
        getOptionLabel={(option) => `+${option.phone} (${option.code})`}
        filterOptions={(options, state) =>
          options.filter(
            (option) =>
              option.code
                .toLocaleLowerCase()
                .includes(state.inputValue.toLocaleLowerCase()) ||
              option.label
                .toLocaleLowerCase()
                .includes(state.inputValue.toLocaleLowerCase()) ||
              "+"
                .concat(option.phone)
                .toLocaleLowerCase()
                .includes(state.inputValue.toLocaleLowerCase()),
          )
        }
        sx={{
          width: 200,
          '& .MuiOutlinedInput-root': {
            borderColor: '#e0e0e0',
            borderRadius: '8px',
            padding: "8px",
            '& fieldset': {
              borderColor: '#e0e0e0',
            },
            '&:hover fieldset': {
              borderColor: '#b0b0b0',
            },
            // Add styling for disabled state in Autocomplete
            '&.Mui-disabled .MuiInputBase-input': {
              color: disabledTextColor,
              WebkitTextFillColor: disabledTextColor,
              opacity: 1,
            },
          },
        }}
        renderOption={(props, option) => (
          <Box
            component="li"
            sx={{ "& > img": { mr: 2, flexShrink: 0 } }}
            {...props}
          >
            <img
              loading="lazy"
              width="20"
              srcSet={`https://flagcdn.com/w40/${option.code.toLowerCase()}.png 2x`}
              src={`https://flagcdn.com/w20/${option.code.toLowerCase()}.png`}
              alt=""
            />
            {option.label} ({option.code}) +{option.phone}
          </Box>
        )}
        renderInput={(params) => (
          <StyledTextField
            {...params}
            placeholder="Select Country Code"
            disabledTextColor={disabledTextColor}
            inputProps={{
              ...params.inputProps,
              autoComplete: "new-password", // disable autocomplete and autofill
            }}
          />
        )}
      />
    </Stack>
  );
}