import React, { useState, useRef } from 'react';
import { Box, CardContent, Grid, Stack } from '@mui/material';
import CustomRightArrow from '../../../src/assets/icons/CustomRightArrow';

import MLTypography from '../ui/MLTypography/MLTypography';
import MLButton from '../ui/MLButton/MLButton';
import { ErgoVideo, ErgoPoster, Topic } from '.';
import { useNavigate } from 'react-router-dom';
import { useList } from '@refinedev/core';
import ErgoVideoCard from './ErgoVideoCard';
import PosterCard from './PosterCard';
import ResourceVideoPopup from './ResourceVideoPopup';
import TopicBadge from './TopicBadge';
import ReadMoreRightArrow from '../../assets/icons/ReadMoreRightArrow';
import PDFPreviewModal from './PDFPreviewModal';

interface Article {
  id: number;
  title: string;
  description: string;
  fileUrl: string;
  author: string;
  thumbnail: [{
    id: number;
    name: string;
    url: string;
  }];
  topics: Topic[];
  displayOrder?: number;
  publishedDate?: string;
}

const TodaysTip = () => {
  const navigate = useNavigate();
  const [videoOpen, setVideoOpen] = useState(false);
  const [currentVideo, setCurrentVideo] = useState();
  const [isPdfOpen, setIsPdfOpen] = useState(false);
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);

  // Carousel state
  const [activeSlide, setActiveSlide] = useState(0);
  const carouselRef = useRef(null);

  // Fetch latest video, article, and poster
  const { data: videoData } = useList<ErgoVideo>({
    resource: 'ep-videos',
    pagination: { current: 1, pageSize: 1 },
    meta: {
      populate: {
        video: { fields: ['url'] },
        thumbnail: { fields: ['url'] },
        topics: {
          fields: ['id', 'title'],
          populate: { icon: { fields: ['url'] } }
        }
      },
      fields: ['id', 'title', 'description']
    },
  });

  const { data: posterData } = useList<ErgoPoster>({
    resource: 'ep-posters',
    pagination: { current: 1, pageSize: 1 },
    meta: {
      populate: {
        thumbnail: { fields: ['url'] },
        topics: {
          fields: ['id', 'title'],
          populate: { icon: { fields: ['url'] } }
        }
      },
      fields: ['id', 'title', 'description']
    },
  });

  const { data: articleData } = useList<Article>({
    resource: 'ep-white-papers',
    pagination: { current: 1, pageSize: 1 },
    meta: {
      populate: {
        thumbnail: { fields: ['url'] },
        topics: {
          fields: ['id', 'title'],
          populate: { icon: { fields: ['url'] } }
        }
      },
      fields: ['id', 'title', 'description', 'fileUrl']
    },
  });

  // Handle video popup
  const handleClickOpenVideoPopup = (video: any) => {
    setCurrentVideo(video);
    setVideoOpen(true);
  };

  const handleCloseVideoPopup = () => {
    setVideoOpen(false);
  };

  // Handle PDF preview
  const handleOpenPdf = (article: Article) => {
    setSelectedArticle(article);
    setIsPdfOpen(true);
  };

  const handleClosePdf = () => {
    setIsPdfOpen(false);
    setSelectedArticle(null);
  };

  // Navigate to respective tabs
  const handleViewAll = () => navigate('/resources');

  // Extract the first item from fetched data
  const video = videoData?.data?.[0];
  const poster = posterData?.data?.[0];
  const article = articleData?.data?.[0];

  // Carousel navigation
  const handleNextSlide = () => {
    setActiveSlide((prev) => (prev === 2 ? 0 : prev + 1));
  };

  const handlePrevSlide = () => {
    setActiveSlide((prev) => (prev === 0 ? 2 : prev - 1));
  };

  // Prepare carousel items
  const carouselItems = [
    video && (
      <Stack  key="video-slide">
        <ErgoVideoCard
          key={video.id}
          videoData={video}
          handleClickOpenVideoPopup={handleClickOpenVideoPopup}
        />
      </Stack>
    ),
    article && (
      <Stack key="article-slide" gap="10px">
        <Box
          component="img"
          sx={{
            height: "auto",
            width: "100%",
            bgcolor: '#f5f5f5',
            borderRadius: "10px",
            objectFit: "fill",
            border: "0.5px solid #E0E0E0",
            cursor: "pointer"
          }}
          src={article?.thumbnail?.[0]?.url || ""}
          alt={article?.title}
          onClick={() => handleOpenPdf(article)}
        />

        <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: "10px", px: 0 }}>
          <Stack direction="row" flexWrap="wrap" gap={0.5}>
            {article?.topics?.map((topic) => (
              <TopicBadge key={topic.id} imageUrl={topic?.icon?.[0]?.url} label={topic.title} />
            ))}
          </Stack>

          <Stack gap="5px">
            <MLTypography
              sx={{
                fontSize: '14px',
                fontWeight: 600,
                display: '-webkit-box',
                WebkitLineClamp: 1,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                cursor: "pointer"
              }}
              onClick={() => handleOpenPdf(article)}
            >
              {article?.title}
            </MLTypography>

            <MLTypography
              sx={{
                fontSize: '14px',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                height: '3rem',
              }}
            >
              {article?.description}
            </MLTypography>
          </Stack>

          <MLButton
            endIcon={<ReadMoreRightArrow />}
            variant="outlined"
            onClick={() => handleOpenPdf(article)}
          >
            Read Article
          </MLButton>
        </CardContent>
      </Stack>
    ),
    poster && (
      <Box key="poster-slide">
        <PosterCard key={poster.id} posterData={poster} />
      </Box>
    )
  ].filter(Boolean);

  // SVG for navigation arrows
  const LeftArrow = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
      <mask id="path-1-inside-1_17021_71794" fill="white">
        <path d="M0 8.8C0 8.05712 0 7.68568 0.0246613 7.37233C0.333409 3.44932 3.44932 0.333409 7.37233 0.0246613C7.68568 0 8.05712 0 8.8 0H23.2C23.9429 0 24.3143 0 24.6277 0.0246613C28.5507 0.333409 31.6666 3.44932 31.9753 7.37233C32 7.68568 32 8.05712 32 8.8V23.2C32 23.9429 32 24.3143 31.9753 24.6277C31.6666 28.5507 28.5507 31.6666 24.6277 31.9753C24.3143 32 23.9429 32 23.2 32H8.8C8.05712 32 7.68568 32 7.37233 31.9753C3.44932 31.6666 0.333409 28.5507 0.0246613 24.6277C0 24.3143 0 23.9429 0 23.2V8.8Z" />
      </mask>
      <path d="M0 8.8C0 8.05712 0 7.68568 0.0246613 7.37233C0.333409 3.44932 3.44932 0.333409 7.37233 0.0246613C7.68568 0 8.05712 0 8.8 0H23.2C23.9429 0 24.3143 0 24.6277 0.0246613C28.5507 0.333409 31.6666 3.44932 31.9753 7.37233C32 7.68568 32 8.05712 32 8.8V23.2C32 23.9429 32 24.3143 31.9753 24.6277C31.6666 28.5507 28.5507 31.6666 24.6277 31.9753C24.3143 32 23.9429 32 23.2 32H8.8C8.05712 32 7.68568 32 7.37233 31.9753C3.44932 31.6666 0.333409 28.5507 0.0246613 24.6277C0 24.3143 0 23.9429 0 23.2V8.8Z" fill="white" />
      <path d="M7.37233 31.9753L7.3331 32.4738L7.37233 31.9753ZM0.0246613 24.6277L0.52312 24.5884L0.0246613 24.6277ZM31.9753 24.6277L31.4769 24.5884L31.9753 24.6277ZM24.6277 31.9753L24.5884 31.4769L24.6277 31.9753ZM24.6277 0.0246613L24.5884 0.52312L24.6277 0.0246613ZM31.9753 7.37233L32.4738 7.3331L31.9753 7.37233ZM7.37233 0.0246613L7.3331 -0.473797L7.37233 0.0246613ZM8.8 0V0.5H23.2V0V-0.5H8.8V0ZM32 8.8H31.5V23.2H32H32.5V8.8H32ZM23.2 32V31.5H8.8V32V32.5H23.2V32ZM0 23.2H0.5V8.8H0H-0.5V23.2H0ZM8.8 32V31.5C8.04761 31.5 7.70039 31.4996 7.41156 31.4769L7.37233 31.9753L7.3331 32.4738C7.67097 32.5004 8.06663 32.5 8.8 32.5V32ZM0 23.2H-0.5C-0.5 23.9334 -0.500389 24.329 -0.473797 24.6669L0.0246613 24.6277L0.52312 24.5884C0.500389 24.2996 0.5 23.9524 0.5 23.2H0ZM7.37233 31.9753L7.41156 31.4769C3.73373 31.1874 0.812571 28.2663 0.52312 24.5884L0.0246613 24.6277L-0.473797 24.6669C-0.145753 28.8351 3.1649 32.1458 7.3331 32.4738L7.37233 31.9753ZM32 23.2H31.5C31.5 23.9524 31.4996 24.2996 31.4769 24.5884L31.9753 24.6277L32.4738 24.6669C32.5004 24.329 32.5 23.9334 32.5 23.2H32ZM23.2 32V32.5C23.9334 32.5 24.329 32.5004 24.6669 32.4738L24.6277 31.9753L24.5884 31.4769C24.2996 31.4996 23.9524 31.5 23.2 31.5V32ZM31.9753 24.6277L31.4769 24.5884C31.1874 28.2663 28.2663 31.1874 24.5884 31.4769L24.6277 31.9753L24.6669 32.4738C28.8351 32.1458 32.1458 28.8351 32.4738 24.6669L31.9753 24.6277ZM23.2 0V0.5C23.9524 0.5 24.2996 0.500389 24.5884 0.52312L24.6277 0.0246613L24.6669 -0.473797C24.329 -0.500389 23.9334 -0.5 23.2 -0.5V0ZM32 8.8H32.5C32.5 8.06663 32.5004 7.67097 32.4738 7.3331L31.9753 7.37233L31.4769 7.41156C31.4996 7.70039 31.5 8.04761 31.5 8.8H32ZM24.6277 0.0246613L24.5884 0.52312C28.2663 0.812571 31.1874 3.73373 31.4769 7.41156L31.9753 7.37233L32.4738 7.3331C32.1458 3.1649 28.8351 -0.145753 24.6669 -0.473797L24.6277 0.0246613ZM8.8 0V-0.5C8.06663 -0.5 7.67097 -0.500389 7.3331 -0.473797L7.37233 0.0246613L7.41156 0.52312C7.70039 0.500389 8.04761 0.5 8.8 0.5V0ZM0 8.8H0.5C0.5 8.04761 0.500389 7.70039 0.52312 7.41156L0.0246613 7.37233L-0.473797 7.3331C-0.500389 7.67097 -0.5 8.06663 -0.5 8.8H0ZM7.37233 0.0246613L7.3331 -0.473797C3.1649 -0.145753 -0.145753 3.1649 -0.473797 7.3331L0.0246613 7.37233L0.52312 7.41156C0.812571 3.73373 3.73373 0.812571 7.41156 0.52312L7.37233 0.0246613Z" fill="#7856FF" mask="url(#path-1-inside-1_17021_71794)" />
      <path d="M18 12L14 16L18 20" stroke="#7856FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );

  const RightArrow = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
      <mask id="path-1-inside-1_17489_8525" fill="white">
        <path d="M0 8.8C0 8.05712 0 7.68568 0.0246613 7.37233C0.333409 3.44932 3.44932 0.333409 7.37233 0.0246613C7.68568 0 8.05712 0 8.8 0H23.2C23.9429 0 24.3143 0 24.6277 0.0246613C28.5507 0.333409 31.6666 3.44932 31.9753 7.37233C32 7.68568 32 8.05712 32 8.8V23.2C32 23.9429 32 24.3143 31.9753 24.6277C31.6666 28.5507 28.5507 31.6666 24.6277 31.9753C24.3143 32 23.9429 32 23.2 32H8.8C8.05712 32 7.68568 32 7.37233 31.9753C3.44932 31.6666 0.333409 28.5507 0.0246613 24.6277C0 24.3143 0 23.9429 0 23.2V8.8Z" />
      </mask>
      <path d="M0 8.8C0 8.05712 0 7.68568 0.0246613 7.37233C0.333409 3.44932 3.44932 0.333409 7.37233 0.0246613C7.68568 0 8.05712 0 8.8 0H23.2C23.9429 0 24.3143 0 24.6277 0.0246613C28.5507 0.333409 31.6666 3.44932 31.9753 7.37233C32 7.68568 32 8.05712 32 8.8V23.2C32 23.9429 32 24.3143 31.9753 24.6277C31.6666 28.5507 28.5507 31.6666 24.6277 31.9753C24.3143 32 23.9429 32 23.2 32H8.8C8.05712 32 7.68568 32 7.37233 31.9753C3.44932 31.6666 0.333409 28.5507 0.0246613 24.6277C0 24.3143 0 23.9429 0 23.2V8.8Z" fill="white" />
      <path d="M7.37233 31.9753L7.3331 32.4738L7.37233 31.9753ZM0.0246613 24.6277L0.52312 24.5884L0.0246613 24.6277ZM31.9753 24.6277L31.4769 24.5884L31.9753 24.6277ZM24.6277 31.9753L24.5884 31.4769L24.6277 31.9753ZM24.6277 0.0246613L24.5884 0.52312L24.6277 0.0246613ZM31.9753 7.37233L32.4738 7.3331L31.9753 7.37233ZM7.37233 0.0246613L7.3331 -0.473797L7.37233 0.0246613ZM8.8 0V0.5H23.2V0V-0.5H8.8V0ZM32 8.8H31.5V23.2H32H32.5V8.8H32ZM23.2 32V31.5H8.8V32V32.5H23.2V32ZM0 23.2H0.5V8.8H0H-0.5V23.2H0ZM8.8 32V31.5C8.04761 31.5 7.70039 31.4996 7.41156 31.4769L7.37233 31.9753L7.3331 32.4738C7.67097 32.5004 8.06663 32.5 8.8 32.5V32ZM0 23.2H-0.5C-0.5 23.9334 -0.500389 24.329 -0.473797 24.6669L0.0246613 24.6277L0.52312 24.5884C0.500389 24.2996 0.5 23.9524 0.5 23.2H0ZM7.37233 31.9753L7.41156 31.4769C3.73373 31.1874 0.812571 28.2663 0.52312 24.5884L0.0246613 24.6277L-0.473797 24.6669C-0.145753 28.8351 3.1649 32.1458 7.3331 32.4738L7.37233 31.9753ZM32 23.2H31.5C31.5 23.9524 31.4996 24.2996 31.4769 24.5884L31.9753 24.6277L32.4738 24.6669C32.5004 24.329 32.5 23.9334 32.5 23.2H32ZM23.2 32V32.5C23.9334 32.5 24.329 32.5004 24.6669 32.4738L24.6277 31.9753L24.5884 31.4769C24.2996 31.4996 23.9524 31.5 23.2 31.5V32ZM31.9753 24.6277L31.4769 24.5884C31.1874 28.2663 28.2663 31.1874 24.5884 31.4769L24.6277 31.9753L24.6669 32.4738C28.8351 32.1458 32.1458 28.8351 32.4738 24.6669L31.9753 24.6277ZM23.2 0V0.5C23.9524 0.5 24.2996 0.500389 24.5884 0.52312L24.6277 0.0246613L24.6669 -0.473797C24.329 -0.500389 23.9334 -0.5 23.2 -0.5V0ZM32 8.8H32.5C32.5 8.06663 32.5004 7.67097 32.4738 7.3331L31.9753 7.37233L31.4769 7.41156C31.4996 7.70039 31.5 8.04761 31.5 8.8H32ZM24.6277 0.0246613L24.5884 0.52312C28.2663 0.812571 31.1874 3.73373 31.4769 7.41156L31.9753 7.37233L32.4738 7.3331C32.1458 3.1649 28.8351 -0.145753 24.6669 -0.473797L24.6277 0.0246613ZM8.8 0V-0.5C8.06663 -0.5 7.67097 -0.500389 7.3331 -0.473797L7.37233 0.0246613L7.41156 0.52312C7.70039 0.500389 8.04761 0.5 8.8 0.5V0ZM0 8.8H0.5C0.5 8.04761 0.500389 7.70039 0.52312 7.41156L0.0246613 7.37233L-0.473797 7.3331C-0.500389 7.67097 -0.5 8.06663 -0.5 8.8H0ZM7.37233 0.0246613L7.3331 -0.473797C3.1649 -0.145753 -0.145753 3.1649 -0.473797 7.3331L0.0246613 7.37233L0.52312 7.41156C0.812571 3.73373 3.73373 0.812571 7.41156 0.52312L7.37233 0.0246613Z" fill="#7856FF" mask="url(#path-1-inside-1_17489_8525)" />
      <path d="M14 20L18 16L14 12" stroke="#7856FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );

  return (
    <Stack gap="15px">
      <Stack
        display="flex"
        justifyContent="space-between"
        direction={{ xs: "row", sm: "row" }}
        alignItems={{ xs: "flex-start", md: "center" }}
      >
        <MLTypography variant='h2' sx={{ fontSize: "30px", fontWeight: 600 }}>
          Today's ergo tips
        </MLTypography>
        <MLButton
          color="primary"
          sx={{
            textTransform: 'none',
            '&:hover': { backgroundColor: 'transparent' },
            marginLeft: { xs: "-8px", sm: "0px" },
          }}
          onClick={handleViewAll}
        >
          View all
          <CustomRightArrow sx={{ fontSize: "20px", ml: 1 }} />
        </MLButton>
      </Stack>

      {/* Mobile Carousel (only shown on xs screens) */}
      <Box
        // maxWidth="430px"
        sx={{
          display: { xs: 'flex', sm: 'none' },
          position: 'relative',
          flexDirection: "column",
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {/* Left navigation arrow - positioned absolutely */}
        <Box
          onClick={handlePrevSlide}
          sx={{
            position: 'absolute',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: 32,
            height: 32,
            zIndex: 2,
            cursor: 'pointer',
            left: "0%",
            top: '25%',
            transform: 'translateY(-50%)'
          }}
        >
          <LeftArrow />
        </Box>

        {/* Right navigation arrow - positioned absolutely */}
        <Box
          onClick={handleNextSlide}
          sx={{
            position: 'absolute',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: 32,
            height: 32,
            zIndex: 2,
            cursor: 'pointer',
            right: "0%",
            top: '25%',
            transform: 'translateY(-50%)'
          }}
        >
          <RightArrow />
        </Box>

        <Box
          ref={carouselRef}
          sx={{
            maxWidth: '309px',
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              transform: `translateX(-${activeSlide * 100}%)`,
              transition: 'transform 0.3s ease-in-out',
            }}
          >
            {carouselItems.map((item, index) => (
              <Box
                key={index}
                sx={{
                  minWidth: '100%',
                  // flexShrink: 0,
                  px: 1
                }}
              >
                {item}
              </Box>
            ))}
          </Box>
        </Box>

        {/* Indicator dots */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            mt: 2
          }}
        >
          <Box sx={{ display: 'flex', gap: 1 }}>
            {[0, 1, 2].map(index => (
              <Box
                key={index}
                onClick={() => setActiveSlide(index)}
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: activeSlide === index ? '#7856FF' : '#E0E0E0',
                  cursor: 'pointer'
                }}
              />
            ))}
          </Box>
        </Box>
      </Box>

      {/* Desktop/Tablet Grid (hidden on xs screens) */}
      <Grid container spacing={4} sx={{ display: { xs: 'none', sm: 'flex' } }}>
        {/* Video Section */}
        <Grid item xs={12} sm={6} md={6} lg={4}>
          {video && (
            <Stack gap={'10px'}>
              <ErgoVideoCard
                key={video.id}
                videoData={video}
                handleClickOpenVideoPopup={handleClickOpenVideoPopup}
              />
            </Stack>
          )}
        </Grid>

        {/* Article Section */}
        <Grid item xs={12} sm={6} md={6} lg={4}>
          {article && (
            <Stack
              key={article.id}
              gap="10px"
            >
              <Box
                component="img"
                sx={{
                  height: "auto",
                  width: "100%",
                  bgcolor: '#f5f5f5',
                  borderRadius: "10px",
                  objectFit: "fill",
                  border: "0.5px solid #E0E0E0",
                  cursor: "pointer"
                }}
                src={article?.thumbnail?.[0]?.url || ""}
                alt={article?.title}
                onClick={() => handleOpenPdf(article)}
              />

              <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: "10px" }}>
                <Stack direction="row" flexWrap="wrap" gap={0.5} sx={{ maxWidth: { sm: '90%' } }}>
                  {article?.topics?.map((topic) => (
                    <TopicBadge key={topic.id} imageUrl={topic?.icon?.[0]?.url} label={topic.title} />
                  ))}
                </Stack>

                <Stack gap="5px">
                  <MLTypography
                    sx={{
                      fontSize: { sm: '16px', md: '20px' },
                      fontWeight: 600,
                      display: '-webkit-box',
                      WebkitLineClamp: 1,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      cursor: "pointer"
                    }}
                    onClick={() => handleOpenPdf(article)}
                  >
                    {article?.title}
                  </MLTypography>

                  <MLTypography
                    sx={{
                      fontSize: { sm: '16px' },
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      height: '3rem',
                    }}
                  >
                    {article?.description}
                  </MLTypography>
                </Stack>

                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <MLButton
                    endIcon={<ReadMoreRightArrow />}
                    variant="outlined"
                    onClick={() => handleOpenPdf(article)}
                  >
                    Read Article
                  </MLButton>
                </Stack>
              </CardContent>
            </Stack>
          )}
        </Grid>

        {/* Poster Section */}
        <Grid item xs={12} sm={6} md={6} lg={4}>
          {poster && <PosterCard key={poster.id} posterData={poster} />}
        </Grid>
      </Grid>

      {/* Video Popup */}
      <ResourceVideoPopup
        open={videoOpen}
        onClose={handleCloseVideoPopup}
        videoData={currentVideo}
      />

      {/* PDF Preview Modal */}
      <PDFPreviewModal
        open={isPdfOpen}
        onClose={handleClosePdf}
        pdfUrl={selectedArticle?.fileUrl ?? ''}
        fileName={selectedArticle?.title ?? 'document.pdf'}
      />
    </Stack>
  );
};

export default TodaysTip;