import React, { useEffect, useRef, useState } from 'react';
import {
    Box,
    styled,
    Stack,
} from '@mui/material';
import TopicBadge from './TopicBadge';
import PlayIcon from '../../assets/icons/PlayIcon';
import { ErgoReel } from '.';
import MLTypography from '../ui/MLTypography/MLTypography';
import PauseIcon from '../../assets/icons/PauseIcon';

interface ErgoReelCardProps {
    reelData: ErgoReel;
    isPlaying: boolean;
    onPlay: (videoId: number | null) => void;
    isBadgeVisible?: false
}

const ControlButton = styled(Box)({
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    cursor: 'pointer',
    zIndex: 1,
    opacity: 0,
    transition: 'opacity 0.2s ease-in-out',
});

const VideoContainer = styled(Box)({
    position: 'relative',
    width: '100%',
    // height: '550px',
    paddingTop: '56.25%', // 16:9 Aspect Ratio
    overflow: 'hidden',
    borderRadius: "10px",
    border: "0.5px solid #E0E0E0",
    '&:hover .control-button': {
        opacity: 1,
    },
});

const StyledVideo = styled('video')({
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    cursor: 'pointer',
});

const ErgoReelCard: React.FC<ErgoReelCardProps> = ({ reelData, isPlaying, onPlay, isBadgeVisible = true }) => {
    const videoRef = useRef<HTMLVideoElement | null>(null);
    const [duration, setDuration] = useState('0:00');
    const [isHovered, setIsHovered] = useState(false);
    const [hasStartedPlaying, setHasStartedPlaying] = useState(false);
    const [isEnded, setIsEnded] = useState(false);

    const formatTime = (timeInSeconds: number) => {
        const minutes = Math.floor(timeInSeconds / 60);
        const seconds = Math.floor(timeInSeconds % 60);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };

    const handleLoadedMetadata = () => {
        if (videoRef.current) {
            setDuration(formatTime(videoRef.current.duration));
        }
    };

    const handleTimeUpdate = () => {
        if (videoRef.current) {
            const timeLeft = videoRef.current.duration - videoRef.current.currentTime;
            setDuration(formatTime(timeLeft));
        }
    };

    const handleVideoEnded = () => {
        setIsEnded(true);
        onPlay(null); // Reset playing state in parent
    };

    const handleVideoClick = () => {
        if (!videoRef.current) return;

        if (!isPlaying) {
            setHasStartedPlaying(true);
            setIsEnded(false);
            onPlay(reelData.id);
            videoRef.current.play();
        } else {
            videoRef.current.pause();
            onPlay(null);
        }
    };

    useEffect(() => {
        if (!isPlaying && videoRef.current) {
            videoRef.current.pause();
        }
    }, [isPlaying]);

    // Determine if we should show the play button
    const shouldShowPlayButton = !isPlaying || isHovered || isEnded;

    return (
        <Stack gap="10px" >
            <VideoContainer
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
            >
                <StyledVideo
                    ref={videoRef}
                    src={reelData?.video[0].url}
                    onLoadedMetadata={handleLoadedMetadata}
                    onTimeUpdate={handleTimeUpdate}
                    onClick={handleVideoClick}
                    onEnded={handleVideoEnded}
                    playsInline
                    style={{
                        visibility: (!hasStartedPlaying && reelData?.thumbnail?.[0]?.url) ? 'hidden' : 'visible'
                    }}
                />

                {!hasStartedPlaying && reelData?.thumbnail?.[0]?.url && (
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            zIndex: 1,
                            backgroundColor: '#000',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                        }}
                        onClick={handleVideoClick}
                    >
                        <Box
                            component="img"
                            src={reelData.thumbnail[0].url}
                            sx={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'fill',
                            }}
                        />
                    </Box>
                )}

                <MLTypography
                    sx={{
                        position: "absolute",
                        top: "8px",
                        right: "8px",
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        color: "#fff",
                        py: "2px",
                        px: "8px",
                        borderRadius: "4px",
                        zIndex: 2
                    }}
                >
                    {duration || ""}
                </MLTypography>
                <ControlButton
                    className="control-button"
                    onClick={handleVideoClick}
                    sx={{
                        opacity: shouldShowPlayButton ? 1 : 0,
                        zIndex: 2
                    }}
                >
                    {!isPlaying ? <PlayIcon /> : <PauseIcon />}
                </ControlButton>
            </VideoContainer>
            <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
            >
                <Stack
                    direction="row"
                    flexWrap="wrap"
                    gap={0.5}
                    sx={{
                        minHeight: { xs: 0, sm: '32px' }, // No space on mobile, fixed height on larger screens
                    }}
                >
                    {
                        isBadgeVisible && reelData?.topics.map((topic: any) =>
                            <TopicBadge
                                key={topic.id}
                                imageUrl={topic?.icon[0]?.url}
                                label={topic.title}
                            />)
                    }
                </Stack>
            </Stack>
            <Stack spacing={0.5}>
                <MLTypography
                    sx={{
                        fontSize: { xs: '14px', sm: '16px', md: '20px' },
                        fontWeight: 600,
                        display: '-webkit-box',
                        WebkitLineClamp: 1,
                        WebkitBoxOrient: { xs: '', sm: 'vertical' },
                        overflow: { xs: 'visible', sm: 'hidden' },
                    }}
                >
                    {reelData?.title}
                </MLTypography>
                <MLTypography
                    sx={{
                        fontSize: { xs: '14px', sm: '16px' },
                        fontWeight: 400,
                        display: '-webkit-box',
                        WebkitLineClamp: 2,  // Limit to 2 lines
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxHeight: '3rem', // Limit the height of description (2 lines of text)
                    }}
                >
                    {reelData?.description}
                </MLTypography>
            </Stack>
        </Stack>
    );
};

export default ErgoReelCard;