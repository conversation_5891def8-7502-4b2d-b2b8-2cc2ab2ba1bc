import { Box, Stack, } from '@mui/material'
import React, { useState, useEffect } from 'react'
import PlayIcon from '../../assets/icons/PlayBlueIcon';
import TopicBadge from './TopicBadge';
import { ErgoVideo } from '.';
import ViewsCountEyeIcon from '../../assets/icons/ViewsCountEyeIcon';
import MLTypography from '../ui/MLTypography/MLTypography';
import MLButton from '../ui/MLButton/MLButton';
import ReadMoreRightArrow from '../../assets/icons/ReadMoreRightArrow';

interface ErgoVideoCardProps {
    videoData: ErgoVideo;
    handleClickOpenVideoPopup: (video: ErgoVideo) => void
    hideViewButton?: boolean; // New prop to control button visibility
}

const ErgoVideoCard: React.FC<ErgoVideoCardProps> = ({ videoData, handleClickOpenVideoPopup,
    hideViewButton = false // Default to showing the button
}) => {
    const [duration, setDuration] = useState<string | null>(null);

    // Load the video duration when component mounts
    useEffect(() => {
        if (videoData?.video?.[0]?.url) {
            const video = document.createElement('video');
            video.src = videoData.video[0].url;

            // Add event listener for metadata loading
            video.addEventListener('loadedmetadata', () => {
                const minutes = Math.floor(video.duration / 60);
                const seconds = Math.floor(video.duration % 60);
                setDuration(`${minutes}:${seconds < 10 ? '0' : ''}${seconds}`);
            });

            // Handle errors
            video.addEventListener('error', () => {
                console.error('Error loading video duration');
                // Set a fallback duration if needed
                setDuration('0:00');
            });

            // Load the video to trigger metadata loading
            video.load();

            // Clean up
            return () => {
                video.removeEventListener('loadedmetadata', () => { });
                video.removeEventListener('error', () => { });
                video.src = '';
            };
        }
    }, [videoData?.video]);

    const handleLoadedMetadata = (event: React.SyntheticEvent<HTMLVideoElement, Event>) => {
        const videoElement = event.target as HTMLVideoElement;
        const minutes = Math.floor(videoElement.duration / 60);
        const seconds = Math.floor(videoElement.duration % 60);
        setDuration(`${minutes}:${seconds < 10 ? '0' : ''}${seconds}`);
    };

    return (
        <div>
            <Stack
                key={videoData.id}
                direction="column"
                gap="10px"
                width="100%"
                textAlign="left"
                mb={'18px'}
            >
                <Stack
                    // width="auto"
                    // height="222px"
                    sx={{
                        cursor: "pointer",
                        position: "relative",
                        overflow: "hidden",
                        height: "auto",
                        width: "100%",
                    }}
                    onClick={() => handleClickOpenVideoPopup(videoData)}
                >
                    {duration && (
                        <MLTypography
                            sx={{
                                position: "absolute",
                                top: "8px",
                                right: "8px",
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                color: "#fff",
                                py: "2px",
                                px: "8px",
                                borderRadius: "4px",
                                zIndex: 2
                            }}
                        >
                            {duration}
                        </MLTypography>
                    )}
                    <Box
                        position="absolute"
                        top="50%"
                        left="50%"
                        sx={{
                            transform: 'translate(-50%, -50%)',
                            cursor: "pointer",
                            zIndex: 2
                        }}
                    >
                        <PlayIcon />
                    </Box>

                    <Box
                        component="img"
                        sx={{
                            height: "auto",
                            width: "100%",
                            // width: "fit-content",
                            bgcolor: '#f5f5f5',
                            borderRadius: "10px",
                            objectFit: "fill",
                            border: "0.5px solid #E0E0E0",
                            cursor: "pointer"
                        }}
                        src={videoData?.thumbnail[0]?.url || ""}
                        alt={videoData?.thumbnail[0]?.name || ""}
                    />
                    {/* <Box
                        sx={{
                            height: { xl: "233px", lg: "188px", md: "220px", sm: "138px", xs: "170px" },
                            width: { xl: "431px", lg: "307px", md: "375px", sm: "254px", xs: "284px" },
                            position: "relative"
                        }}
                    >
                        <video
                            src={videoData?.video[0].url}
                            poster={videoData?.thumbnail[0]?.url || ""}
                            onLoadedMetadata={handleLoadedMetadata}
                            style={{
                                width: "auto",
                                height: "100%",
                                borderRadius: "3px",
                            }}
                            disablePictureInPicture
                        />
                    </Box> */}
                </Stack>


                {/* Show topic badges above title only in TodaysTip (when hideViewButton is false) */}
                {!hideViewButton && (
                    <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="center"
                    >
                        <Stack
                            direction="row"
                            flexWrap="wrap"
                            gap={'16px'}
                            sx={{
                                minHeight: { xs: 0, sm: '32px' },
                            }}
                        >
                            {videoData?.topics?.length > 0 ?
                                videoData.topics.map((topic: any) =>
                                    <TopicBadge
                                        key={topic.id}
                                        imageUrl={topic?.icon?.[0]?.url}
                                        label={topic.title}
                                    />
                                )
                                :
                                <Box sx={{
                                    display: { xs: 'none', sm: 'block' },
                                    height: '32px',
                                    width: 0
                                }} />
                            }
                        </Stack>
                    </Stack>
                )}

                <Stack gap="5px">
                    <MLTypography
                        sx={{
                            fontSize: { xs: '14px', sm: '16px', md: '20px' },
                            fontWeight: 600,
                            display: '-webkit-box',
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: { xs: '', sm: 'vertical' },
                            overflow: { xs: 'visible', sm: 'hidden' },
                            cursor: "pointer",
                        }}
                        onClick={() => handleClickOpenVideoPopup(videoData)}
                    >
                        {videoData.title}
                    </MLTypography>
                    <MLTypography
                        sx={{
                            fontSize: { xs: '14px', sm: '16px' },
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            height: '3rem',
                            mb: hideViewButton ? '10px' : ''
                        }}
                    >
                        {videoData.description}
                    </MLTypography>
                </Stack>

                {/* Show topic badges below description only in ErgoVideosTab (when hideViewButton is true) */}
                {hideViewButton && videoData?.topics?.length > 0 && (
                    <Stack
                        direction="row"
                        flexWrap="wrap"
                        gap={'16px'}
                        sx={{
                            mt: 0.5,
                            minHeight: { xs: 0, sm: '32px' }, // No space on mobile, fixed height on larger screens
                        }}
                    >
                        {videoData.topics.map((topic: any) => (
                            <TopicBadge
                                key={topic.id}
                                imageUrl={topic?.icon?.[0]?.url}
                                label={topic.title}
                            />
                        ))}
                    </Stack>
                )}


                {/* Conditionally render desktop/tablet button based on hideViewButton prop */}
                {!hideViewButton && (
                    <Stack display={{ xs: 'none', sm: 'flex' }} direction="row" justifyContent="space-between" alignItems="center">
                        <MLButton
                            endIcon={<ReadMoreRightArrow />}
                            variant="outlined"
                            onClick={() => handleClickOpenVideoPopup(videoData)}
                        >
                            VIEW VIDEO
                        </MLButton>
                    </Stack>
                )}

                {/* Conditionally render mobile button based on hideViewButton prop */}
                {!hideViewButton && (
                    <MLButton
                        endIcon={<ReadMoreRightArrow />}
                        variant="outlined"
                        onClick={() => handleClickOpenVideoPopup(videoData)}
                        sx={{ display: { xs: 'flex', sm: 'none' } }}
                    >
                        VIEW VIDEO
                    </MLButton>
                )}

            </Stack>
        </div>
    )
}

export default ErgoVideoCard