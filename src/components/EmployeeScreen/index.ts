// types/index.ts
export interface LearningModule {
  id: number,
  title: string;
  progress: number;
  estimatedDuration: number;
  lastLesson: string;
  status: string;
  thumbnail: Thumbnail[];
  ELCourseProgress: [
    { percentageComplete: number, status: string }
  ]
}

export interface VideoContent {
  id: number,
  duration: string;
  title: string;
  description: string;
  thumbnail?: string;
}

export interface Video {
  id: number;
  name: string;
  url: string;
}

export interface Topic {
  id: number;
  title: string;
  icon: [{
    url: string
  }]
}

export interface ErgoReel {
  id: number;
  title: string;
  description: string;
  videoUrl: string;
  displayOrder: number;
  thumbnail: [{
    url: string
  }] | null;
  topics: Topic[];
  video: Video[];
  views: string;
}


interface Thumbnail {
  id: number;
  name: string;
  url: string;
}

export interface ImageNode {
  type: 'image';
  image: ImageData;
  children: TextNode[];
}

interface ImageData {
  url: string;
  name: string;
  alternativeText: string;
  caption: string
}

export interface ListItemNode {
  type: 'list-item';
  children: TextNode[];
}

export interface TextNode {
  text: string;
  type?: string;
}

export interface ContentNode {
  type: string;
  children: (TextNode | ListItemNode)[];
  image?: ImageData;
  level?: number;
  format?: 'unordered' | 'ordered';
}

export interface Article {
  id: number;
  title: string;
  description: string;
  content: ContentNode[];
  displayOrder: number;
  thumbnail: Thumbnail[];
  topics: Topic[];
  publishedAt: string;
  writtenBy: string;
  writerProfile: string;
  publishedDate: string;
  readingTime: number;
}

export interface ErgoVideo {
  id: number;
  title: string;
  description: string;
  videoUrl: string;
  displayOrder: number;
  thumbnail: Thumbnail[];
  topics: Topic[];
  video: Video[];
  views: string;
}

export interface ErgoPoster {
  id: number;
  title: string;
  description: string;
  displayOrder: number;
  thumbnail: Thumbnail[];
  topics: Topic[];
  posterUrl?: string;
}
export interface ErgoBite {
  id: number;
  title: string;
  description: string;
  displayOrder: number;
  media: Thumbnail[];
  topics: Topic[];
}

