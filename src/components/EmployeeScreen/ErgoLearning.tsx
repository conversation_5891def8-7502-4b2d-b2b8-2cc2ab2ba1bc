import React from 'react';
import { CardMedia, LinearProgress, Box, Stack } from '@mui/material';
import { styled } from '@mui/material/styles';
import { LearningModule } from '.';
import MLButton from '../ui/MLButton/MLButton';
import CustomRightArrow from '../../assets/icons/CustomRightArrow';
import MLTypography from '../ui/MLTypography/MLTypography';
import Slider from './Slider';

const StyledLinearProgress = styled(LinearProgress)(({ theme }) => ({
    height: 10,
    borderRadius: 4,
    backgroundColor: '#E0E0E0',
    '& .MuiLinearProgress-bar': {
        backgroundColor: '#7856FF',
        borderRadius: 4,
    }
}));

interface ErgoLearningProps {
    learningData: LearningModule[] | undefined;
}

const ErgoLearning: React.FC<ErgoLearningProps> = ({ learningData }) => {

    if (!learningData) return null;

    return (
        <Stack gap="15px" width={'105%'} >
            <MLTypography
                variant='h2'
                sx={{
                    fontSize: '30px',
                    fontWeight: 600,
                }}
            >
                Ergo Learning
            </MLTypography>
            <Slider>
                {learningData.map((module) =>
                    <Stack
                        key={module.id}
                        sx={{
                            borderRadius: "10px",
                            background: "#F2F2F2",
                        }}
                        direction={{ xs: 'column', sm: 'row' }}
                        gap="25px"
                        padding="20px"
                    >
                        <Stack flex={1}>
                            {module?.thumbnail &&
                                <CardMedia
                                    component="img"
                                    image={module?.thumbnail[0]?.url}
                                    alt={module.title}
                                    sx={{
                                        width: { md: '90%', lg: "80%" },
                                        height: { xs: '230px', sm: '240px', lg: "250px" },
                                        minHeight: { lg: '310px' },
                                        objectFit: 'fill',
                                        borderRadius: '8px',
                                    }}
                                />
                            }

                        </Stack>
                        <Stack flex={1} gap="15px">
                            <Stack gap="3px">
                                <MLTypography sx={{
                                    fontSize: "16px",
                                    fontWeight: 600,
                                }}
                                >
                                    {module.title}
                                </MLTypography>
                                <MLTypography
                                    color="text.secondary"
                                    sx={{ mb: 2 }}
                                >
                                    {module?.ELCourseProgress[0]?.status || "Not Started"}
                                </MLTypography>

                            </Stack>
                            <Stack gap="7px">
                                <StyledLinearProgress
                                    variant="determinate"
                                    value={module?.ELCourseProgress[0]?.percentageComplete || 0}
                                />

                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <MLTypography sx={{
                                        fontSize: "14px",
                                        fontWeight: 600,
                                    }}>
                                        {`${module?.ELCourseProgress[0]?.percentageComplete ?? 0}% completed`}
                                    </MLTypography>
                                    <MLTypography variant="body2">{`${module.estimatedDuration} mins left`}</MLTypography>
                                </Box>
                            </Stack>

                            <MLTypography
                                variant="body2"
                                color="text.secondary"
                                sx={{ mb: 2 }}
                            >
                                Last: {module.lastLesson ?? "NA"}
                            </MLTypography>

                            <Box >
                                <MLButton
                                    variant="outlined"
                                    color="primary"
                                    sx={{
                                        paddingX: 6
                                    }}
                                >
                                    CONTINUE
                                    <CustomRightArrow sx={{
                                        fontSize: "18px",
                                        ml: 1
                                    }} />
                                </MLButton>
                            </Box>
                        </Stack>
                    </Stack>
                )}
            </Slider>
        </Stack>
    );
};

export default ErgoLearning