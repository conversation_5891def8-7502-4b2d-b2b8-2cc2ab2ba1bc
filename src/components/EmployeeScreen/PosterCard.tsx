import React, { useState } from 'react';
import { Box, Dialog, Stack } from '@mui/material';
import MLButton from '../ui/MLButton/MLButton';
import { ErgoPoster } from '.';
import MLTypography from '../ui/MLTypography/MLTypography';
import ReadMoreRightArrow from '../../assets/icons/ReadMoreRightArrow';
import AttachmentPreviewModal from '../../pages/MyErgoQueris/components/AttachmentPreviewModal';
import TopicBadge from './TopicBadge';
import PDFPreviewModal from './PDFPreviewModal';

interface PosterCardProps {
    posterData: ErgoPoster;
}

const PosterCard: React.FC<PosterCardProps> = ({ posterData }) => {
    const [isDownloading, setIsDownloading] = useState(false);
    const [previewOpen, setPreviewOpen] = useState(false);
    const [isPdfOpen, setIsPdfOpen] = useState(false);
    const [currentAttachments, setCurrentAttachments] = useState<Array<{ url: string; name: string; mime?: string }>>([]);
    const [initialPreviewIndex, setInitialPreviewIndex] = useState(0);

    const handleOpenPreview = () => {
        // If we have a PDF URL, open the PDF preview
        if (posterData.posterUrl) {
            setIsPdfOpen(true);
        } else {
            // Otherwise fall back to the image preview
            setCurrentAttachments([posterData?.thumbnail[0]]);
            setInitialPreviewIndex(0);
            setPreviewOpen(true);
        }
    };

    const handleClosePdf = () => {
        setIsPdfOpen(false);
    };

    const handleDownload = async () => {
        if (isDownloading) return;
        setIsDownloading(true);

        // Get the PDF URL from posterUrl field
        const pdfUrl = posterData.posterUrl;
        if (!pdfUrl) {
            alert('No downloadable PDF available');
            setIsDownloading(false);
            return;
        }

        try {
            // Simple direct download
            const response = await fetch(pdfUrl);
            if (!response.ok) {
                throw new Error('Download failed');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `${posterData.title}.pdf`;
            document.body.appendChild(link);
            link.click();

            // Cleanup
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading file:', error);
            alert('Failed to download the file. Please try again.');
        } finally {
            setIsDownloading(false);
        }
    };

    return (
        <Stack gap="10px">
            <Box
                component="img"
                sx={{
                    height: "auto",
                    width: "100%",
                    bgcolor: '#f5f5f5',
                    borderRadius: "10px",
                    objectFit: "fill",
                    border: "0.5px solid #E0E0E0",
                    cursor: "pointer"
                }}
                src={posterData?.thumbnail[0]?.url}
                alt={posterData?.title}
                onClick={handleOpenPreview}
            />

            {/* Topic badges section with consistent height */}
            <Stack
                direction="row"
                flexWrap="wrap"
                gap={'16px'}
                sx={{
                    minHeight: { xs: "0px", sm: '32px' }, // No space on mobile, fixed height on larger screens
                }}
            >
                {posterData?.topics?.length > 0 &&
                    posterData.topics.map((topic: any) => (
                        <TopicBadge
                            key={topic.id}
                            imageUrl={topic?.icon?.[0]?.url}
                            label={topic.title}
                        />
                    ))
                }
            </Stack>

            {/* Title and Description */}
            <Stack gap="5px">
                <MLTypography
                    sx={{
                        fontSize: { xs: '14px', sm: '16px', md: '20px' },
                        fontWeight: 600,
                        display: '-webkit-box',
                        WebkitLineClamp: 1,
                        WebkitBoxOrient: { xs: '', sm: 'vertical' },
                        overflow: { xs: 'visible', sm: 'hidden' },
                        cursor: "pointer"
                    }}
                    onClick={handleOpenPreview}
                >
                    {posterData?.title}
                </MLTypography>
                <MLTypography
                    sx={{
                        fontSize: { xs: '14px', sm: '16px' },
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        height: '3rem',
                    }}>
                    {posterData?.description}
                </MLTypography>
            </Stack>
            <Stack display={{ xs: 'none', sm: 'flex' }} direction="row" justifyContent="space-between" alignItems="center">
                <MLButton
                    endIcon={<ReadMoreRightArrow />}
                    variant="outlined"
                    onClick={handleDownload}
                    disabled={isDownloading}
                >
                    DOWNLOAD POSTER
                </MLButton>
            </Stack>
            <MLButton
                endIcon={<ReadMoreRightArrow />}
                variant="outlined"
                onClick={handleDownload}
                disabled={isDownloading}
                sx={{ display: { xs: 'flex', sm: 'none' } }}
            >
                DOWNLOAD POSTER
            </MLButton>

            {/* PDF Preview Modal */}
            {posterData.posterUrl && (
                <PDFPreviewModal
                    open={isPdfOpen}
                    onClose={handleClosePdf}
                    pdfUrl={posterData.posterUrl}
                    fileName={`${posterData.title}.pdf`}
                />
            )}

            {/* Image Preview Modal (fallback) */}
            <Dialog open={previewOpen}>
                <AttachmentPreviewModal
                    open={previewOpen}
                    onClose={() => setPreviewOpen(false)}
                    attachments={currentAttachments}
                    initialIndex={initialPreviewIndex}
                />
            </Dialog>
        </Stack>
    );
};

export default PosterCard;


// import React, { useState } from 'react';
// import { Box, Dialog, Stack } from '@mui/material';
// import MLButton from '../ui/MLButton/MLButton';
// import { ErgoPoster } from '.';
// import MLTypography from '../ui/MLTypography/MLTypography';
// import ReadMoreRightArrow from '../../assets/icons/ReadMoreRightArrow';
// import AttachmentPreviewModal from '../../pages/MyErgoQueris/components/AttachmentPreviewModal';
// import TopicBadge from './TopicBadge';
// // Import jsPDF library - you need to add this to your dependencies
// // npm install jspdf
// import { jsPDF } from "jspdf";

// interface PosterCardProps {
//     posterData: ErgoPoster;
// }

// const PosterCard: React.FC<PosterCardProps> = ({ posterData }) => {
//     const [isDownloading, setIsDownloading] = useState(false);
//     const [previewOpen, setPreviewOpen] = useState(false);
//     const [currentAttachments, setCurrentAttachments] = useState<Array<{ url: string; name: string; mime?: string }>>([]);
//     const [initialPreviewIndex, setInitialPreviewIndex] = useState(0);

//     const handleOpenPreview = (attachments: any[], startIndex: number) => {
//         setCurrentAttachments(attachments);
//         setInitialPreviewIndex(startIndex);
//         setPreviewOpen(true);
//     };

//     const handleDownload = async (): Promise<void> => {
//         if (isDownloading) return;
//         setIsDownloading(true);

//         const imageUrl = posterData.thumbnail[0]?.url;
//         if (!imageUrl) {
//             alert('No downloadable URL available');
//             setIsDownloading(false);
//             return;
//         }

//         // Clean filename for PDF
//         const fileName = `${posterData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`;
//         let downloadSuccess = false;

//         try {
//             // Create a new Image object
//             const img = new Image();
//             img.crossOrigin = 'anonymous';

//             // Create a promise for image loading
//             await new Promise<void>((resolve, reject) => {
//                 const timeout = setTimeout(() => reject(new Error("Image loading timed out")), 10000);

//                 img.onload = () => {
//                     clearTimeout(timeout);
//                     resolve();
//                 };

//                 img.onerror = () => {
//                     clearTimeout(timeout);
//                     reject(new Error("Image failed to load"));
//                 };

//                 // Add cache-busting parameter to avoid cached non-CORS-enabled responses
//                 img.src = `${imageUrl}?${new Date().getTime()}`;
//             });

//             // Initialize PDF with proper orientation and size
//             // Calculate the PDF dimensions based on the image aspect ratio
//             const imgWidth = img.width;
//             const imgHeight = img.height;
//             const aspectRatio = imgWidth / imgHeight;

//             // Initialize PDF - use A4 as a base but adjust if needed
//             let pdf;
//             if (aspectRatio > 1) {
//                 // Landscape orientation for wide images
//                 pdf = new jsPDF('landscape', 'mm', 'a4');
//             } else {
//                 // Portrait orientation for tall images
//                 pdf = new jsPDF('portrait', 'mm', 'a4');
//             }

//             // Get PDF dimensions
//             const pdfWidth = pdf.internal.pageSize.getWidth();
//             const pdfHeight = pdf.internal.pageSize.getHeight();

//             // Calculate image dimensions to fit within PDF while maintaining aspect ratio
//             let finalWidth, finalHeight;
//             if (aspectRatio > 1) {
//                 finalWidth = pdfWidth - 20; // Margin of 10mm on each side
//                 finalHeight = finalWidth / aspectRatio;
//             } else {
//                 finalHeight = pdfHeight - 60; // Allow space for metadata
//                 finalWidth = finalHeight * aspectRatio;
//             }

//             // Canvas approach to handle CORS issues
//             const canvas = document.createElement('canvas');
//             canvas.width = imgWidth;
//             canvas.height = imgHeight;

//             const ctx = canvas.getContext('2d');
//             if (!ctx) {
//                 throw new Error("Could not get canvas context");
//             }
//             ctx.drawImage(img, 0, 0, imgWidth, imgHeight);

//             // Convert canvas to data URL
//             try {
//                 const imgData = canvas.toDataURL('image/jpeg', 0.95);

//                 // Center image on page
//                 const xPos = (pdfWidth - finalWidth) / 2;
//                 const yPos = 20; // Position from top

//                 // Add the image to PDF
//                 pdf.addImage(imgData, 'JPEG', xPos, yPos, finalWidth, finalHeight);

//                 // Add poster metadata below the image
//                 const textY = yPos + finalHeight + 10;
//                 pdf.setFontSize(16);
//                 pdf.text(posterData.title, pdfWidth / 2, textY, { align: 'center' });

//                 // Add description if available
//                 if (posterData.description) {
//                     pdf.setFontSize(10);
//                     const descriptionLines = pdf.splitTextToSize(posterData.description, pdfWidth - 40);
//                     pdf.text(descriptionLines, pdfWidth / 2, textY + 10, { align: 'center' });
//                 }

//                 // Add topics if available
//                 if (posterData.topics?.length > 0) {
//                     pdf.setFontSize(10);
//                     const topics = posterData.topics.map((topic: any) => topic.title).join(', ');
//                     pdf.text(`Topics: ${topics}`, pdfWidth / 2, textY + 25, { align: 'center' });
//                 }

//                 // Save the PDF
//                 pdf.save(fileName);
//                 downloadSuccess = true;

//             } catch (canvasError) {
//                 console.log("Canvas tainted by CORS:", canvasError);
//                 throw canvasError;
//             }
//         } catch (error) {
//             console.error("PDF generation failed:", error);

//             // If PDF creation fails, try a fallback method using PDF.js
//             try {
//                 // Fallback approach - fetch the image and embed it directly
//                 const response = await fetch(imageUrl, {
//                     headers: {
//                         'Origin': window.location.origin
//                     }
//                 });

//                 if (response.ok) {
//                     const blob = await response.blob();

//                     // Create a new PDF
//                     const pdf = new jsPDF();

//                     // Create a temporary URL for the blob
//                     const blobUrl = URL.createObjectURL(blob);

//                     // Create a new Image to get dimensions
//                     const tmpImg = new Image();
//                     await new Promise<void>((resolve, reject) => {
//                         tmpImg.onload = () => resolve();
//                         tmpImg.onerror = () => reject(new Error("Fallback image failed to load"));
//                         tmpImg.src = blobUrl;
//                     });

//                     // Calculate dimensions for PDF
//                     const pdfWidth = pdf.internal.pageSize.getWidth();
//                     const pdfHeight = pdf.internal.pageSize.getHeight();

//                     // Calculate image dimensions
//                     const imgAspect = tmpImg.width / tmpImg.height;
//                     let renderWidth, renderHeight;

//                     if (imgAspect > 1) {
//                         renderWidth = pdfWidth - 20;
//                         renderHeight = renderWidth / imgAspect;
//                     } else {
//                         renderHeight = pdfHeight - 50;
//                         renderWidth = renderHeight * imgAspect;
//                     }

//                     // Convert Blob to base64
//                     const reader = new FileReader();
//                     const base64data = await new Promise<string>((resolve) => {
//                         reader.onloadend = () => resolve(reader.result as string);
//                         reader.readAsDataURL(blob);
//                     });

//                     // Add image to PDF
//                     pdf.addImage(
//                         base64data,
//                         'JPEG',
//                         (pdfWidth - renderWidth) / 2,
//                         10,
//                         renderWidth,
//                         renderHeight
//                     );

//                     // Add title and metadata
//                     pdf.setFontSize(14);
//                     pdf.text(posterData.title, pdfWidth / 2, renderHeight + 20, { align: 'center' });

//                     // Save PDF
//                     pdf.save(fileName);
//                     downloadSuccess = true;

//                     // Clean up
//                     URL.revokeObjectURL(blobUrl);
//                 }
//             } catch (fallbackError) {
//                 console.error("Fallback method failed:", fallbackError);
//             }
//         }

//         // If all methods failed, show error message
//         if (!downloadSuccess) {
//             alert("Unable to generate PDF. Please try again or contact support for assistance.");
//         }

//         setIsDownloading(false);
//     };

//     return (
//         <Stack gap="10px">
//             <Box
//                 component="img"
//                 sx={{
//                     height: "auto",
//                     width: "100%",
//                     bgcolor: '#f5f5f5',
//                     borderRadius: "10px",
//                     objectFit: "fill",
//                     border: "0.5px solid #E0E0E0",
//                     cursor: "pointer"
//                 }}
//                 src={posterData?.thumbnail[0]?.url}
//                 alt={posterData?.title}
//                 onClick={() => handleOpenPreview([posterData?.thumbnail[0]], 0)}
//             />

//             {/* Topic badges section with consistent height */}
//             <Stack
//                 direction="row"
//                 flexWrap="wrap"
//                 gap={'16px'}
//                 sx={{
//                     minHeight: { xs: "0px", sm: '32px' }, // No space on mobile, fixed height on larger screens
//                 }}
//             >
//                 {posterData?.topics?.length > 0 &&
//                     posterData.topics.map((topic: any) => (
//                         <TopicBadge
//                             key={topic.id}
//                             imageUrl={topic?.icon?.[0]?.url}
//                             label={topic.title}
//                         />
//                     ))
//                 }
//             </Stack>

//             {/* Title and Description */}
//             <Stack gap="5px">
//                 <MLTypography
//                     sx={{
//                         fontSize: { xs: '14px', sm: '16px', md: '20px' },
//                         fontWeight: 600,
//                         display: '-webkit-box',
//                         WebkitLineClamp: 1,
//                         WebkitBoxOrient: { xs: '', sm: 'vertical' },
//                         overflow: { xs: 'visible', sm: 'hidden' },
//                         cursor: "pointer"
//                     }}
//                     onClick={() => handleOpenPreview([posterData?.thumbnail[0]], 0)}
//                 >
//                     {posterData?.title}
//                 </MLTypography>
//                 <MLTypography
//                     sx={{
//                         fontSize: { xs: '14px', sm: '16px' },
//                         display: '-webkit-box',
//                         WebkitLineClamp: 2,
//                         WebkitBoxOrient: 'vertical',
//                         overflow: 'hidden',
//                         textOverflow: 'ellipsis',
//                         height: '3rem',
//                     }}>
//                     {posterData?.description}
//                 </MLTypography>
//             </Stack>
//             <Stack display={{ xs: 'none', sm: 'flex' }} direction="row" justifyContent="space-between" alignItems="center">
//                 <MLButton
//                     endIcon={<ReadMoreRightArrow />}
//                     variant="outlined"
//                     onClick={handleDownload}
//                     disabled={isDownloading}
//                 >
//                     DOWNLOAD POSTER
//                 </MLButton>
//             </Stack>
//             <MLButton
//                 endIcon={<ReadMoreRightArrow />}
//                 variant="outlined"
//                 onClick={handleDownload}
//                 disabled={isDownloading}
//                 sx={{ display: { xs: 'flex', sm: 'none' } }}
//             >
//                 DOWNLOAD POSTER
//             </MLButton>

//             {/* Poster Preview Modal */}
//             <Dialog open={previewOpen}>
//                 <AttachmentPreviewModal
//                     open={previewOpen}
//                     onClose={() => setPreviewOpen(false)}
//                     attachments={currentAttachments}
//                     initialIndex={initialPreviewIndex}
//                 />
//             </Dialog>
//         </Stack>
//     );
// };

// export default PosterCard;