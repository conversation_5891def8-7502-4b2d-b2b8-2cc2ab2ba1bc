import React from 'react';
import { CardContent, CardMedia, Stack } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import MLButton from '../ui/MLButton/MLButton';
import { Article } from '.';
import TopicBadge from './TopicBadge';
import MLTypography from '../ui/MLTypography/MLTypography';
import SmallClockIcon from '../../assets/icons/SmallClockIcon';
import ReadMoreRightArrow from '../../assets/icons/ReadMoreRightArrow';
import { Link } from 'react-router-dom';

interface ErgoArticleCardProps {
    articleData: Article,
}

const ErgoArticleCard: React.FC<ErgoArticleCardProps> = ({ articleData }) => {
    const formatDate = (dateInput: string) => {
        const date = new Date(dateInput);

        // Format the date to "Nov 26, 2024" using ES6 syntax
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',  // Abbreviated month name
            day: 'numeric',
        });
    };
    return (
        <Stack gap="10px" >
            <CardMedia
                component="img"
                height="300"
                image={articleData?.thumbnail && articleData?.thumbnail[0]?.url || ""}
                alt={articleData?.title}
                sx={{
                    bgcolor: '#f5f5f5',  // Light grey background for placeholder
                    borderRadius: "10px",
                    border: "0.5px solid #E0E0E0",
                    objectFit: "fill"
                }}
            />

            <CardContent>
                <Stack gap="15px">
                    {/* Category and Date */}
                    <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="flex-start"
                        flexWrap="wrap"
                        gap={1}
                    >
                        <Stack
                            direction="row"
                            flexWrap="wrap"
                            gap={0.5}
                            sx={{
                                minHeight: { xs: 0, sm: '32px' }, // No space on mobile, fixed height on larger screens
                            }}
                        >
                            {articleData?.topics.map((topic) => (
                                <TopicBadge
                                    key={topic.id}
                                    imageUrl={topic?.icon[0]?.url}
                                    label={topic.title}
                                />
                            ))}
                        </Stack>
                        <MLTypography
                            sx={{
                                color: "#9C9C9C",
                                fontSize: "12px",
                                fontWeight: 500,
                                flexShrink: 0
                            }}
                        >
                            {articleData.publishedDate && `Published on ${formatDate(articleData?.publishedDate)}`}
                        </MLTypography>
                    </Stack>
                    {/* Title and Description */}
                    <Stack gap="5px">
                        <MLTypography
                            sx={{
                                fontSize: { xs: '14px', sm: '16px', md: '20px' },
                                fontWeight: 600,
                                display: '-webkit-box',
                                WebkitLineClamp: 1,
                                WebkitBoxOrient: { xs: '', sm: 'vertical' },
                                overflow: { xs: 'visible', sm: 'hidden' },
                            }}
                        >
                            {articleData?.title}
                        </MLTypography>
                        <MLTypography
                            sx={{
                                fontSize: { xs: '14px', sm: '16px' },
                                display: '-webkit-box',
                                WebkitLineClamp: 2,  // Limit to 2 lines
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                height: '3rem', // Limit the height of description (2 lines of text)
                            }}
                        >
                            {articleData?.description}
                        </MLTypography>
                    </Stack>

                    {/* Read Time and Button */}
                    <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="center"
                    >
                        <Stack direction="row" spacing={1} alignItems="center" color="#9C9C9C">
                            {articleData?.readingTime &&
                                <>
                                    <SmallClockIcon />
                                    <MLTypography
                                        sx={{

                                            font: "12px",
                                            fontWeight: 500
                                        }} >
                                        {articleData?.readingTime}  min read
                                    </MLTypography>
                                </>
                            }
                        </Stack>
                        <MLButton
                            endIcon={<ReadMoreRightArrow />}
                            variant="outlined"
                            component={Link}
                            to={`/article/${articleData.id}`}
                        >
                            Read Article
                        </MLButton>
                    </Stack>
                </Stack>
            </CardContent>
        </Stack>
    );
};

export default ErgoArticleCard;