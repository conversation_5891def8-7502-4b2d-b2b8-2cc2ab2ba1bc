import React, { useState, useEffect, ReactNode } from 'react';
import {
    Box,
    Breakpoint,
    styled
} from '@mui/material';

interface SliderProps {
    children: ReactNode;
    autoPlayInterval?: number;
    showPlayPause?: boolean;
    showDots?: boolean;
    width?: number | string;
    itemsPerSlide?: number;
    autoPlay?: boolean;
    gap?: number | Partial<Record<Breakpoint, number>>;
}

const StyledDot = styled(Box, {
    shouldForwardProp: (prop) => prop !== 'active',
})<{ active?: boolean }>(({ theme, active }) => ({
    width: 12,
    height: 12,
    borderRadius: '50%',
    backgroundColor: active ? theme.palette.primary.main : theme.palette.grey[300],
    transition: theme.transitions.create(['background-color'], {
        duration: 300,
    }),
    cursor: 'pointer',
    '&:hover': {
        backgroundColor: active ? theme.palette.primary.dark : theme.palette.grey[400],
    }
}));

const CarouselContainer = styled(Box)({
    position: 'relative',
    overflow: 'hidden',
    height: 'auto'
});

const SlideWrapper = styled(Box)<{
    direction: 'left' | 'right';
    gap?: number | Partial<Record<Breakpoint, number>>;
}>(({ direction, gap = 0 }) => ({
    display: 'flex',
    position: 'relative',
    width: '96%',
    height: 'auto',
    transition: 'transform 0.5s ease-in-out',
    ...(direction === 'left' && {
        animation: 'slideOut 0.5s ease-in-out',
    }),
    ...(direction === 'right' && {
        animation: 'slideIn 0.5s ease-in-out',
    }),
    '@keyframes slideOut': {
        '0%': {
            transform: 'translateX(0)',
            opacity: 1,
        },
        '100%': {
            transform: 'translateX(-100%)',
            opacity: 0,
        },
    },
    '@keyframes slideIn': {
        '0%': {
            transform: 'translateX(100%)',
            opacity: 0,
        },
        '100%': {
            transform: 'translateX(0)',
            opacity: 1,
        },
    },
}));

const Slider = ({
    children,
    autoPlayInterval = 3000,
    showDots = true,
    width = '100%',
    itemsPerSlide = 1,
    autoPlay = false,
    gap = 0,  // Add gap prop with default value
}: SliderProps) => {
    const childrenArray = React.Children.toArray(children);
    const numberOfSlides = Math.ceil(childrenArray.length / itemsPerSlide);
    const [activeStep, setActiveStep] = useState(0);
    const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);
    const [slideDirection, setSlideDirection] = useState<'left' | 'right'>('right');
    const [key, setKey] = useState(0);

    const getCurrentSlideItems = () => {
        const startIndex = activeStep * itemsPerSlide;
        return childrenArray.slice(startIndex, startIndex + itemsPerSlide);
    };

    const handleSlideChange = (newIndex: number) => {
        if (newIndex !== activeStep) {
            setSlideDirection(newIndex > activeStep ? 'right' : 'left');
            setActiveStep(newIndex);
            setKey(prev => prev + 1);
        }
    };

    useEffect(() => {
        let interval: NodeJS.Timeout;

        if (isAutoPlaying && autoPlay) {
            interval = setInterval(() => {
                const newIndex = activeStep === numberOfSlides - 1 ? 0 : activeStep + 1;
                handleSlideChange(newIndex);
            }, autoPlayInterval);
        }

        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [isAutoPlaying, activeStep, numberOfSlides, autoPlayInterval, autoPlay]);

    const handleDotClick = (index: number) => {
        handleSlideChange(index);
        setIsAutoPlaying(false);
    };

    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
            <Box sx={{ position: 'relative', width }}>
                <CarouselContainer>
                    <SlideWrapper
                        key={key}
                        direction={slideDirection}
                        gap={gap}
                        sx={{
                            '& > *': {
                                flex: `0 0 ${100 / itemsPerSlide}%`,
                            }
                        }}
                    >
                        {getCurrentSlideItems()}
                    </SlideWrapper>
                </CarouselContainer>
            </Box>

            {showDots && (
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 1 }}>
                    {[...Array(numberOfSlides)].map((_, index) => (
                        <StyledDot
                            key={index}
                            active={index === activeStep}
                            onClick={() => handleDotClick(index)}
                        />
                    ))}
                </Box>
            )}
        </Box>
    );
};

export default Slider;