// Types for the article data structure
import { LogicalFilter, useList } from "@refinedev/core";
import {
    Box,
    Stack,
    CardContent,
} from "@mui/material";
import { CustomGridContainer } from "../../pages/EmployeeScreen/Resources";
import React, { useState, useEffect } from "react";
import MLButton from "../ui/MLButton/MLButton";
import ViewMoreDownIcon from "../../assets/icons/ViewMoreDownIcon";
import Loading from "../../pages/Loading/Loading";
import MLTypography from "../ui/MLTypography/MLTypography";
import TopicBadge from "./TopicBadge";
import ReadMoreRightArrow from "../../assets/icons/ReadMoreRightArrow";
import PDFPreviewModal from "./PDFPreviewModal";
import { StatusMessage } from "../../pages/MyAssessments/myAssessmentUtils";

interface Icon {
    id: number;
    url: string;
}

interface Topic {
    id: number;
    title: string;
    icon: Icon[];
}

interface Thumbnail {
    id: number;
    url: string;
}

interface Article {
    id: number;
    title: string;
    description: string;
    fileUrl: string;
    author: string;
    thumbnail: Thumbnail[];
    topics: Topic[];
    publishedDate?: string;
}

interface ErgoArticlesPDFTabProps {
    selectedTopics?: string[];
}

/**
 * ErgoArticlesPDFTab component
 * **Created**
 * @param param0 
 * @returns 
 */
const ErgoArticlesPDFTab: React.FC<ErgoArticlesPDFTabProps> = ({ selectedTopics = [] }) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);
    const [isPdfOpen, setIsPdfOpen] = useState(false);
    // Track if there are more articles to load
    const [hasMoreArticles, setHasMoreArticles] = useState(true);

    // Create filters based on selected topics
    const filters: LogicalFilter[] = React.useMemo(() => {
        if (selectedTopics.length === 0) return [];

        return [
            {
                field: "topics.id",
                operator: "in",
                value: selectedTopics
            }
        ];
    }, [selectedTopics]);

    // Reset pagination when filters change
    useEffect(() => {
        setCurrentPage(1);
        setHasMoreArticles(true);
    }, [selectedTopics]);

    const handleOpenPdf = (article: Article) => {
        setSelectedArticle(article);
        setIsPdfOpen(true);
    };

    const handleClosePdf = () => {
        setIsPdfOpen(false);
        setSelectedArticle(null);
    };

    const { data: EpArticles, isLoading, refetch, isError } = useList<Article>({
        resource: 'ep-white-papers',
        pagination: {
            current: 1,
            pageSize: currentPage * 6
        },
        filters: filters,
        sorters: [
            {
                field: 'createdAt',
                order: 'desc'  // Sort by newest first
            }
        ],
        meta: {
            populate: {
                thumbnail: {
                    fields: ['url']
                },
                topics: {
                    fields: ['id', 'title'],
                    populate: {
                        icon: {
                            fields: ['url']
                        }
                    }
                }
            },
            fields: [
                'title',
                'description',
                'fileUrl',
                'author',
            ]
        },
    });

    // Update hasMoreArticles when data changes
    useEffect(() => {
        if (EpArticles?.data) {
            // If we received fewer articles than requested, there are no more to load
            setHasMoreArticles(EpArticles.data.length < EpArticles.total);
        }
    }, [EpArticles?.data, EpArticles?.total]);

    const handleViewMore = async () => {
        setIsLoadingMore(true);
        setCurrentPage(prev => prev + 1);
        await refetch();
        setIsLoadingMore(false);
    };

    if (isLoading && !isLoadingMore) return (
        <Stack minHeight="70vh" direction="column" alignItems="center" justifyContent="center">
            <Loading />
        </Stack>
    );

    // Handle error state
    if (isError) {
        return (
            <Stack minHeight={"50vh"} direction="column" alignItems="center" justifyContent="center">
                <StatusMessage
                    title="Unable to load Ergo Articles"
                    message={"unable to load the Ergo Articles. Please try again later."}
                    type="error"
                />
                <Box mt={3}>
                    <MLButton variant="outlined" onClick={() => refetch()}>
                        Try Again
                    </MLButton>
                </Box>
            </Stack>
        );
    }

    return (
        <Stack>
            {EpArticles?.data && EpArticles.data.length > 0 ? (
                <Stack minHeight="70vh" direction="column" justifyContent="space-between" alignItems="space-between" gap="60px">
                    <CustomGridContainer>
                        {EpArticles?.data?.map((article) => (
                            <Stack key={article.id} gap="10px" >
                                <Box
                                    component="img"
                                    sx={{
                                        height: "auto",
                                        width: "100%",
                                        bgcolor: '#f5f5f5',
                                        borderRadius: "10px",
                                        objectFit: "fill",
                                        border: "0.5px solid #E0E0E0",
                                        cursor: "pointer"
                                    }}
                                    src={article?.thumbnail?.[0]?.url || ""}
                                    alt={article?.title}
                                    onClick={() => handleOpenPdf(article)}

                                />

                                <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: "10px" }} >
                                    <Stack
                                        direction="row"
                                        flexWrap="wrap"
                                        gap={'16px'}
                                        sx={{
                                            minHeight: { xs: 0, sm: '32px' },
                                            maxWidth: { xs: '100%', sm: '90%' }
                                        }}
                                    >
                                        {article?.topics?.map((topic) => (
                                            <TopicBadge
                                                key={topic.id}
                                                imageUrl={topic?.icon?.[0]?.url}
                                                label={topic.title}
                                            />
                                        ))}
                                    </Stack>

                                    <Stack gap="5px">
                                        <MLTypography
                                            sx={{
                                                fontSize: { xs: '14px', sm: '16px', md: '20px' },
                                                fontWeight: 600,
                                                display: '-webkit-box',
                                                WebkitLineClamp: 1,
                                                WebkitBoxOrient: { xs: '', sm: 'vertical' },
                                                overflow: { xs: 'visible', sm: 'hidden' },
                                                cursor: "pointer"
                                            }}
                                            onClick={() => handleOpenPdf(article)}
                                        >
                                            {article?.title}
                                        </MLTypography>

                                        <MLTypography
                                            sx={{
                                                fontSize: { xs: '14px', sm: '16px' },
                                                display: '-webkit-box',
                                                WebkitLineClamp: 2,  // Limit to 2 lines
                                                WebkitBoxOrient: 'vertical',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                height: '3rem', // Limit the height of description (2 lines of text)
                                            }}
                                        >
                                            {article?.description}
                                        </MLTypography>
                                    </Stack>

                                    <Stack
                                        direction="row"
                                        justifyContent="space-between"
                                        alignItems="center"
                                    >
                                        <MLButton
                                            endIcon={<ReadMoreRightArrow />}
                                            variant="outlined"
                                            onClick={() => handleOpenPdf(article)}
                                        >
                                            Read Article
                                        </MLButton>
                                    </Stack>
                                </CardContent>
                            </Stack>
                        ))}
                    </CustomGridContainer>

                    {hasMoreArticles && (
                        <Box sx={{ width: "100%", display: "flex", justifyContent: "center", p: "8px" }}>
                            {isLoadingMore ? (
                                <Loading />
                            ) : (
                                <MLButton
                                    variant="outlined"
                                    endIcon={<ViewMoreDownIcon />}
                                    onClick={handleViewMore}
                                >
                                    View More
                                </MLButton>
                            )}
                        </Box>
                    )}
                </Stack>
            ) : (
                // <Stack margin="auto">
                //     <MLTypography>
                //         {selectedTopics && selectedTopics.length > 0
                //             ? "No articles available for selected topics!"
                //             : "No articles available!"}
                //     </MLTypography>
                // </Stack>
                <Box py={{ sx: 0, md: 2 }} display="flex" justifyContent="flex-start">
                    <StatusMessage
                        title={`No articles found`}
                        message={selectedTopics && selectedTopics.length > 0
                            ? "Reset the filters to explore articles from other categories!"
                            : "No articles available!"}
                        type="info"
                    />
                </Box>
            )}

            <PDFPreviewModal
                open={isPdfOpen}
                onClose={handleClosePdf}
                pdfUrl={selectedArticle?.fileUrl ?? ''}
                fileName={selectedArticle?.title ?? 'document.pdf'}
            />
        </Stack>
    );
};

export default ErgoArticlesPDFTab;