import React, { useState, useRef } from 'react';
import { Dialog, DialogContent, <PERSON>ack, Box, Typography, IconButton } from '@mui/material';
import { ErgoVideo } from '.';
import CloseIcon from '../../assets/icons/CloseIcon';
import TopicBadge from './TopicBadge';
import ViewsCountEyeIcon from '../../assets/icons/ViewsCountEyeIcon';

interface ResourceVideoPopupProps {
    open: boolean;
    onClose: () => void;
    videoData: ErgoVideo | undefined;
}

const ResourceVideoPopup: React.FC<ResourceVideoPopupProps> = ({ open, onClose, videoData }) => {
    const [duration, setDuration] = useState<string>('');
    const videoRef = useRef<HTMLVideoElement>(null);

    const formatTime = (timeInSeconds: number) => {
        const minutes = Math.floor(timeInSeconds / 60);
        const seconds = Math.floor(timeInSeconds % 60);
        return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    };

    const handleLoadedMetadata = () => {
        if (videoRef.current) {
            setDuration(formatTime(videoRef.current.duration));
        }
    };

    const handleTimeUpdate = () => {
        if (videoRef.current) {
            const timeLeft = videoRef.current.duration - videoRef.current.currentTime;
            setDuration(formatTime(timeLeft));
        }
    };

    if (!videoData) return null;

    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="md"
            fullWidth
        >
            <DialogContent sx={{
                padding: { md: "30px 60px", sm: "20px 30px" }
            }} >
                <Stack direction="row" justifyContent="space-between" alignItems="start" mb={1}>
                    <Typography
                        sx={{
                            fontSize: { md: "32px", xs: "24px" },
                            fontWeight: 700,
                        }}
                    >
                        {videoData.title}
                    </Typography>
                    <IconButton onClick={onClose}>
                        <CloseIcon />
                    </IconButton>
                </Stack>
                <Stack gap="10px">
                    <Box
                        sx={{
                            position: "relative",
                            width: "100%",
                            paddingTop: "56.25%", // 16:9 aspect ratio
                            border: "0.5px solid #9C9C9C",
                            borderRadius: "10px",
                            overflow: "hidden",
                        }}
                    >

                        <video
                            ref={videoRef}
                            src={videoData.video[0]?.url}
                            poster={videoData.thumbnail[0]?.url}
                            onLoadedMetadata={handleLoadedMetadata}
                            onTimeUpdate={handleTimeUpdate}
                            controls
                            autoPlay
                            disablePictureInPicture
                            controlsList="nodownload"
                            style={{
                                position: "absolute",
                                top: 0,
                                left: 0,
                                width: "100%",
                                height: "100%",
                                objectFit: "cover", // ensures it fills the box
                                display: "block",
                            }}
                        />
                        {duration && (
                            <Typography
                                sx={{
                                    position: "absolute",
                                    top: "8px",
                                    right: "8px",
                                    backgroundColor: "rgba(0, 0, 0, 0.6)",
                                    color: "#fff",
                                    py: "2px",
                                    px: "8px",
                                    borderRadius: "4px",
                                }}
                            >
                                {duration}
                            </Typography>
                        )}
                    </Box>
                    <Stack direction="row" justifyContent="space-between">
                        <Typography
                            sx={{
                                fontSize: '16px',
                                fontWeight: 400,
                            }}
                        >
                            {videoData.description}
                        </Typography>
                        {/* <Box display="flex" gap={1} alignItems="center">
                            <ViewsCountEyeIcon />
                            <Typography variant="caption" color="text.disabled">
                                {videoData?.views || 0}
                            </Typography>
                        </Box> */}
                    </Stack>
                    <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="center"
                    >
                        <Stack
                            direction="row"
                            alignItems="center"
                            gap={'16px'}
                        >
                            {
                                videoData?.topics.map((topic: any) =>
                                    <TopicBadge
                                        key={topic.id}
                                        imageUrl={topic?.icon[0]?.url}
                                        label={topic.title}
                                    />)
                            }
                        </Stack>
                    </Stack>
                </Stack>
            </DialogContent>
        </Dialog>
    );
};

export default ResourceVideoPopup;