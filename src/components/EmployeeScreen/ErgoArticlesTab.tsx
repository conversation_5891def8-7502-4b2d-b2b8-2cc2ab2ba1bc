import { useList } from "@refinedev/core";
import { Article } from ".";
import { Box, Stack } from "@mui/material";
import { CustomGridContainer } from "../../pages/EmployeeScreen/Resources";
import ErgoArticleCard from "./ErgoArticleCard";
import React, { useState } from "react";
import MLButton from "../ui/MLButton/MLButton";
import ViewMoreDownIcon from "../../assets/icons/ViewMoreDownIcon";
import Loading from "../../pages/Loading/Loading";
import MLTypography from "../ui/MLTypography/MLTypography";

interface ErgoArticlesTabProps {
    selectedTopics?: string[];
}

const ErgoArticlesTab: React.FC<ErgoArticlesTabProps> = ({ selectedTopics }) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoadingMore, setIsLoadingMore] = useState(false);

    // fetch order of ascending base on displayOrder 
    const { data: EpArticles, isLoading, refetch } = useList<Article>({
        resource: 'ep-articles',
        pagination: {
            current: 1,
            pageSize: currentPage * 6
        },
        meta: {
            populate: {
                thumbnail: {
                    fields: ['url']
                },
                topics: {
                    fields: ['id', 'title'],
                    populate: {
                        icon: {
                            fields: ['url']
                        }
                    }
                }
            },
            fields: [
                'id', 
                'title', 
                'description', 
                'displayOrder',
                'publishedDate',
                'readingTime'
            ]
        },
    });

    const filteredArticles = React.useMemo(() => {
        if (!selectedTopics?.length) return EpArticles?.data;

        return EpArticles?.data.filter(article =>
            article.topics?.some(topic => selectedTopics.includes(topic.id as any))
        );
    }, [EpArticles?.data, selectedTopics]);

    const sortedArticles = React.useMemo(() => {
        return filteredArticles?.sort((a, b) => {
            // If both have displayOrder, sort by it
            if (a.displayOrder && b.displayOrder) {
                return a.displayOrder - b.displayOrder;
            }
            // If only one has displayOrder, the one with displayOrder comes first
            if (a.displayOrder) return -1;
            if (b.displayOrder) return 1;
            // If neither has displayOrder, keep original order
            return 0;
        }) || [];
    }, [filteredArticles]);

    const handleViewMore = async () => {
        setIsLoadingMore(true);
        setCurrentPage(prev => prev + 1);
        await refetch();
        setTimeout(() => { setIsLoadingMore(false) }, 1000)
    };

    if (isLoading) return (
        <Stack minHeight={"70vh"} direction="column" alignItems="center" justifyContent="center">
            <Loading />
        </Stack>
    );

    const hasMore = sortedArticles.length < (EpArticles?.total ?? 0);

    return (
        <Stack>
            {sortedArticles && sortedArticles.length > 0 ? (
                <Stack minHeight={"70vh"} direction="column" justifyContent="space-between" alignItems="space-between" gap="60px">
                    <CustomGridContainer>
                        {sortedArticles.map((article) => (
                            <ErgoArticleCard key={article.id} articleData={article} />
                        ))}
                    </CustomGridContainer>
                    {hasMore && (
                        <Box sx={{ width: "100%", display: "flex", justifyContent: "center", p: "8px" }}>
                            {isLoadingMore &&isLoading ? (
                                <Loading />
                            ) : (
                                <MLButton
                                    variant="outlined"
                                    endIcon={<ViewMoreDownIcon />}
                                    onClick={handleViewMore}
                                >
                                    View More
                                </MLButton>
                            )}
                        </Box>
                    )}
                </Stack>
            ) : (
                <Stack margin="auto">
                    <MLTypography>
                        {selectedTopics && selectedTopics.length > 0
                            ? "No articles available for selected topics!"
                            : "No articles available!"}
                    </MLTypography>
                </Stack>
            )}
        </Stack>
    );
};

export default ErgoArticlesTab;