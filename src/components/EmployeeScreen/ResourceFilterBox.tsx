import {
    Popover,
    Stack,
    IconButton,
    Box,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { ErFilter } from '@mindlens/ergo-icons';
import TopicBadge from '../../components/EmployeeScreen/TopicBadge';
import { useState } from 'react';
import { useList } from '@refinedev/core';
import { Topic } from '.';
import Loading from '../../pages/Loading/Loading';
import MLButton from '../ui/MLButton/MLButton';
import MLTypography from '../ui/MLTypography/MLTypography';

interface ResourceFilterBoxProps {
    selectedTopics: string[];
    handleTopicToggle: (topics: string[]) => void;
    handleClearFilters: () => void;
}

const ResourceFilterBox: React.FC<ResourceFilterBoxProps> = ({ selectedTopics, handleTopicToggle, handleClearFilters }) => {
    const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
    // Add temporary state for staging selected topics
    const [stagedTopics, setStagedTopics] = useState<string[]>(selectedTopics);

    const { data: EpTopics, isLoading } = useList<Topic>({
        resource: 'ep-topics',
        meta: {
            populate: '*',
        },
    });

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
        // Initialize staged topics with current selection when opening
        setStagedTopics(selectedTopics);
    };

    const handleClose = () => {
        setAnchorEl(null);
        // Reset staged topics when closing without applying
        setStagedTopics(selectedTopics);
    };

    // Modified to work with staged topics
    const handleTopicSelection = (topicId: string) => {
        setStagedTopics(prev =>
            prev.includes(topicId)
                ? prev.filter(id => id !== topicId)
                : [...prev, topicId]
        );
    };

    // New function to handle applying filters
    const handleApplyFilters = () => {
        handleTopicToggle(stagedTopics);
        setAnchorEl(null);
    };

    // Modified clear filters to work with staged topics
    const handleClear = () => {
        setStagedTopics([]);
        handleClearFilters();
    };

    const open = Boolean(anchorEl);
    const id = open ? 'filter-popover' : undefined;

    return (
        <Box>
            {isLoading ?
                <Loading />
                : <Stack>
                    <IconButton onClick={handleClick}>
                        <ErFilter />
                    </IconButton>
                    <Popover
                        id={id}
                        open={open}
                        anchorEl={anchorEl}
                        onClose={handleClose}
                        anchorOrigin={{
                            vertical: "top",
                            horizontal: "right",
                        }}
                        sx={{
                            "& .MuiPopover-paper": {
                                borderRadius: "10px",
                                width: '400px',
                                p: "30px"
                            },
                        }}
                    >
                        <Stack spacing={3}>
                            <Stack direction="row" justifyContent="space-between" alignItems="center">
                                <MLTypography
                                    sx={{
                                        fontSize: "20px",
                                        fontWeight: 600
                                    }}
                                >
                                    Filter
                                </MLTypography>
                                <IconButton onClick={handleClose} size="small">
                                    <CloseIcon />
                                </IconButton>
                            </Stack>
                            <Stack direction="row" flexWrap="wrap" gap={1.5}>
                                {EpTopics?.data?.map((topic) => (
                                    <TopicBadge
                                        key={topic.id}
                                        imageUrl={topic?.icon?.[0].url}
                                        label={topic.title}
                                        selected={stagedTopics.includes(topic.id as any)}
                                        onClick={() => handleTopicSelection(topic.id as any)}
                                        variant="filter"
                                    />
                                ))}
                            </Stack>
                            <Stack direction="column" justifyContent="center" alignItems="center">
                                <Stack width="fit-content" direction="row" gap="23px">
                                    <MLButton
                                        variant="contained"
                                        onClick={handleApplyFilters}
                                    >
                                        Show Results
                                    </MLButton>
                                    <MLButton
                                        variant="outlined"
                                        onClick={handleClear}
                                    >
                                        CLEAR FILTERS
                                    </MLButton>
                                </Stack>
                            </Stack>
                        </Stack>
                    </Popover>
                </Stack>}
        </Box>
    );
}

export default ResourceFilterBox