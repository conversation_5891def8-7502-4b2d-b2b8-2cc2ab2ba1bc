import React, { useEffect } from 'react';
import { useList, useOne } from '@refinedev/core';
import {
    Stack,
    Typography,
    Box,
    List,
    ListItem,
    IconButton,
    Divider,
    Avatar
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Loading from '../../pages/Loading/Loading';
import { desktop, tablet } from '../../responsiveStyles';
import MLTypography from '../ui/MLTypography/MLTypography';
import MLButton from '../ui/MLButton/MLButton';
import ErgoArticleCard from './ErgoArticleCard';
import { Article, ContentNode, ImageNode, ListItemNode, TextNode } from '.';
import { CustomGridContainer } from '../../pages/EmployeeScreen/Resources';
import ReadMoreRightArrow from '../../assets/icons/ReadMoreRightArrow';
import BackArrow from '../../assets/icons/BackArrow';
import { ErrorComponent } from '@refinedev/mui';
import VisibilityOnEyeIcon from '../../assets/icons/VisibilityOnEyeIcon';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FacebookIcon from '@mui/icons-material/Facebook';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import TwitterIcon from '@mui/icons-material/Twitter';
import LinkIcon from '@mui/icons-material/Link';
import MLBanner from '../ui/MLBanner/MLBanner';
import MLContainer from '../ui/MLMaxWidthContainer/MLMaxWidthContainer';

interface ArticleHeaderProps {
    article: Article | undefined;
}

const ArticleHeader: React.FC<ArticleHeaderProps> = ({ article }) => {
    if (!article) {
        return null
    }

    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <Stack gap={{ xs: "10px", sm: "15px" }} sx={{ width: '100%' }}>
            <MLTypography
                variant="h1"
                sx={{
                    fontSize: { xs: '24px', sm: '28px', md: '32px' },
                    wordBreak: 'break-word'
                }}
            >
                {article.title}
            </MLTypography>

            <Stack
                direction={{ xs: "column", md: "row" }}
                justifyContent="space-between"
                alignItems={{ xs: "start", md: "flex-start" }}
                gap={{ xs: 3, md: 0 }}
            >
                {/* Author Info Section */}
                <Stack
                    direction="row"
                    spacing={{ xs: 1.5, sm: 2 }}
                // alignItems="center"
                >
                    <Avatar sx={{
                        width: { xs: 60, sm: 80 },
                        height: { xs: 60, sm: 80 },
                        bgcolor: 'primary.main'
                    }}
                    >
                        {article?.writtenBy?.charAt(0)}
                    </Avatar>
                    <Stack spacing={{ xs: 0.5, sm: 1 }}>
                        <MLTypography
                            sx={{
                                fontWeight: 600,
                                fontSize: { xs: '14px', sm: '18px' }
                            }}
                        >
                            Article by {article.writtenBy || "NA"}
                        </MLTypography>
                        <Stack gap={0.5}>
                            <MLTypography
                                sx={{ fontSize: { xs: '12px', sm: '14px' } }}
                            >
                                Published on {article.publishedDate ? formatDate(article?.publishedDate) : "NA"}
                            </MLTypography>
                            <MLTypography
                                sx={{ fontSize: { xs: '12px', sm: '14px' } }}
                                color="#9C9C9C"
                            >
                                {article.readingTime || "NA"} mins read
                            </MLTypography>
                        </Stack>
                    </Stack>
                </Stack>

                {/* likes view  and Social Section, will fix later */}
                {/* <Stack
                    direction={{ xs: "row", md: "column" }}
                    justifyContent="space-between"
                    alignItems={{ xs: "center", md: "flex-end" }}
                    spacing={{ xs: 2, md: 2 }}
                    sx={{
                        width: { xs: '100%', md: 'auto' },
                        minHeight: { md: '87px' }
                    }}
                >
                    <Stack
                        direction="row"
                        spacing={{ xs: 2, sm: 3 }}
                        alignItems="center"
                    >
                        <Stack direction="row" alignItems="center" spacing={1}>
                            <FavoriteIcon
                                color="error"
                                sx={{ fontSize: { xs: 16, sm: 20 } }}
                            />
                            <Typography
                                variant="body2"
                                sx={{ fontSize: { xs: '12px', sm: '14px' } }}
                            >
                                20 Likes
                            </Typography>
                        </Stack>
                        <Stack direction="row" alignItems="center" spacing={1}>
                            <VisibilityOnEyeIcon

                            />
                            <Typography
                                variant="body2"
                                sx={{ fontSize: { xs: '12px', sm: '14px' } }}
                            >
                                250 Views
                            </Typography>
                        </Stack>
                    </Stack>

                    <Stack
                        direction="row"
                        spacing={{ xs: 0.5, sm: 1 }}
                        justifyContent={{ xs: 'flex-end', md: 'space-between' }}
                        sx={{ minWidth: { xs: 'auto', md: '160px' } }}
                    >
                        {[FacebookIcon, LinkedInIcon, TwitterIcon, LinkIcon].map((Icon, index) => (
                            <IconButton
                                key={index}
                                size="small"
                                sx={{
                                    color: 'text.secondary',
                                    '&:hover': { color: 'primary.main' },
                                    padding: { xs: '4px', sm: '8px' }
                                }}
                            >
                                <Icon sx={{ fontSize: { xs: 18, sm: 20 } }} />
                            </IconButton>
                        ))}
                    </Stack>
                </Stack> 
                */}
            </Stack>
        </Stack>
    );
};

const RenderNode: React.FC<{ node: TextNode | ListItemNode | ImageNode | ContentNode }> = ({ node }) => {
    if ('text' in node) {
        return <>{node.text}</>;
    }

    if (node.type === 'image') {
        const imageData = 'image' in node ? node.image : null;
        if (!imageData) return null;
        return (
            <Box >
                {
                    imageData.alternativeText == "blue-light-two-column" ?

                        <Stack
                            direction={{ xs: "column", md: "row" }}
                            justifyContent="space-between"
                            sx={{ width: '100%' }}
                            gap={4}
                        >
                            {/* Image Container */}
                            <Box
                                component="img"
                                src={imageData.url}
                                alt={imageData.alternativeText || imageData.name}
                                width={{ xs: '100%', md: '70%' }}
                                sx={{
                                    height: {
                                        xs: '200px',
                                        sm: '225px',
                                        md: '200px'
                                    },
                                    objectFit: 'fill',
                                }}
                            />

                            {/* Caption Container */}
                            <Stack
                                sx={{
                                    width: { xs: '100%', md: '20%' },
                                    borderTop: '1px solid',
                                    borderBottom: '1px solid',
                                    borderColor: '#7856FF',
                                    height: { xs: 'auto', md: '100%' },
                                    minHeight: { xs: '100px', sm: '150px', md: '200px' },
                                    justifyContent: 'center',
                                }}
                            >
                                <MLTypography
                                    variant="h2"
                                    sx={{
                                        fontSize: {
                                            xs: '1.25rem',
                                            sm: '1.5rem',
                                            md: '1.75rem'
                                        },
                                        textAlign: { xs: 'center', md: 'left' }
                                    }}
                                >
                                    {imageData?.caption}
                                </MLTypography>
                            </Stack>
                        </Stack>
                        :
                        <Box
                            component="img"
                            src={imageData.url}
                            alt={imageData.alternativeText || imageData.name}
                            sx={{
                                width: '100%',
                            }}
                        />
                }
            </Box>
        );
    }

    if (node.type === 'list-item' && node.children) {
        return <>{node.children.map((child, i) => <RenderNode key={i} node={child} />)}</>;
    }

    return null;
};

const RenderContentBlocks: React.FC<{ content?: ContentNode[] }> = ({ content }) => {
    if (!content) return null;

    return (
        <>
            {content.map((block, index) => {
                switch (block.type) {
                    case 'heading': {
                        return (
                            <MLTypography
                                key={index}
                                // variant={block.level === 2 ? 'h3' : 'h3'}
                                variant='h2'
                            >
                                {block.children.map((child, i) => (
                                    <RenderNode key={i} node={child} />
                                ))}
                            </MLTypography>
                        );
                    }

                    case 'paragraph':
                        return (
                            <Typography
                                key={index}
                                variant="body1"
                            >
                                {block.children.map((child, i) => (
                                    <RenderNode key={i} node={child} />
                                ))}
                            </Typography>
                        );

                    case 'list':
                        return (
                            <List
                                key={index}
                                sx={{
                                    listStyleType: 'disc',
                                    pl: 4,
                                    '& .MuiListItem-root': {
                                        display: 'list-item',
                                    }
                                }}
                            >
                                {block.children.map((child, i) => (
                                    <ListItem key={i}>
                                        <RenderNode node={child} />
                                    </ListItem>
                                ))}
                            </List>
                        );

                    case 'image':
                        return <RenderNode key={index} node={block} />;

                    default:
                        return null;
                }
            })}
        </>
    );
};

const SingleArticle: React.FC = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { data: articleData, isLoading, isError } = useOne<Article>({
        resource: "ep-articles",
        id: id
    });
    // Add useEffect to handle scroll on navigation
    // useEffect(() => {
    //     window.scrollTo({
    //         top: 0,
    //         behavior: 'smooth'
    //     });
    // }, [id]); // This will trigger whenever the ID changes


    // need to fix this logic , currently showing three random for design
    const { data: relatedArticlesData, isLoading: relatedArticlesDataLoading } = useList<Article>({
        resource: "ep-articles",
        meta: {
            populate: {
                thumbnail: { populate: '*' },
                topics: {
                    populate: {
                        icon: { populate: '*' }
                    }
                }
            }
        },
    });

    // Get adjacent articles using relatedArticlesData
    const getAdjacentArticles = () => {
        if (!relatedArticlesData?.data || !id) return { prev: null, next: null };

        const currentIndex = relatedArticlesData.data.findIndex(
            article => article.id.toString() === id.toString()
        );

        return {
            prev: currentIndex > 0 ? relatedArticlesData.data[currentIndex - 1] : null,
            next: currentIndex < relatedArticlesData.data.length - 1
                ? relatedArticlesData.data[currentIndex + 1]
                : null
        };
    };

    const { prev, next } = getAdjacentArticles();

    const article = articleData?.data;

    // console.log('articleData: ', articleData);
    // console.log('isError: ', isError);
    // console.log('article: ', article);
    // console.log('relatedArticlesData: ', relatedArticlesData);
    if (isLoading || relatedArticlesDataLoading) return <Loading />;

    if (!articleData && !article) return <ErrorComponent />;

    return (
        <>
            <MLBanner backgroundColor='#EEFF94' title='Ergo Article' subtitle='Work Space setup &gt; Getting Started &gt; Course Overview' />
            <Stack
                direction="column"
                sx={{
                    paddingX: {
                        lg: desktop.contentContainer.paddingX,
                        md: tablet.contentContainer.paddingX,
                        xs: tablet.contentContainer.paddingX,
                    },
                    paddingY: {
                        lg: desktop.contentContainer.paddingY,
                        md: tablet.contentContainer.paddingY,
                        xs: tablet.contentContainer.paddingY,
                    },
                }}

            >
                <MLContainer>
                    <Stack gap="25px">
                        <ArticleHeader article={article} />
                        <Divider />
                        <RenderContentBlocks content={article?.content} />

                        <Box
                            display="flex"
                            justifyContent="space-between"
                        >
                            <MLButton
                                startIcon={<BackArrow />}
                                variant="outlined"
                                sx={{
                                    paddingX: { xs: 2, md: 8 },
                                    visibility: prev ? 'visible' : 'hidden'  // Hide if no previous article
                                }}
                                onClick={() => prev && navigate(`/article/${prev.id}`)}
                                disabled={!prev}
                            >
                                PREVIOUS ARTICLE
                            </MLButton>
                            <MLButton
                                endIcon={<ReadMoreRightArrow />}
                                variant="outlined"
                                sx={{
                                    paddingX: { xs: 2, md: 8 },
                                    visibility: next ? 'visible' : 'hidden'  // Hide if no next article
                                }}
                                onClick={() => next && navigate(`/article/${next.id}`)}
                                disabled={!next}
                            >
                                NEXT ARTICLE
                            </MLButton>
                        </Box>
                        {/* Social Icons will fix this later */}
                        {/* <Divider />
                    <Stack
                    direction="row"
                    spacing={{ xs: 0.5, sm: 1 }}
                    sx={{ minWidth: { xs: 'auto', md: '160px' } }}
                    gap="84px"
                >
                    {[FacebookIcon, LinkedInIcon, TwitterIcon, LinkIcon].map((Icon, index) => (
                        <IconButton
                            key={index}
                            size="small"
                            sx={{
                                color: 'text.secondary',
                                '&:hover': { color: 'primary.main' },
                                padding: { xs: '4px', sm: '8px' }
                            }}
                        >
                            <Icon sx={{ fontSize: { xs: 18, sm: 20 } }} />
                        </IconButton>
                    ))}
                    </Stack> 
                */}
                        <Divider />
                        <Stack gap="17px">
                            <MLTypography variant="h2">Other related articles</MLTypography>
                            <CustomGridContainer>
                                {relatedArticlesData?.data.map((article) => (
                                    <ErgoArticleCard
                                        key={article.id}
                                        articleData={article}
                                    />
                                ))}
                            </CustomGridContainer>
                        </Stack>
                    </Stack>
                </MLContainer>
            </Stack>
        </>

    );
};

export default SingleArticle;