import { useList } from "@refinedev/core";
import React, { useState } from "react";
import { ErgoReel } from ".";
import { TabPanel } from "@mui/lab";
import ErgoReelCard from "./ErgoReelCard";
import { Box, Stack } from "@mui/material";
import MLButton from "../ui/MLButton/MLButton";
import ViewMoreDownIcon from "../../assets/icons/ViewMoreDownIcon";
import Loading from "../../pages/Loading/Loading";
import MLTypography from "../ui/MLTypography/MLTypography";
import styled from "@emotion/styled";
import { StatusMessage } from "../../pages/MyAssessments/myAssessmentUtils";

interface ErgoReelsTabProps {
    selectedTopics: string[];
}
const CustomGridContainer = styled('div')({
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    columnGap: '50px',
    rowGap: '30px',
    width: '100%',
    '@media (max-width: 1200px)': {
        gridTemplateColumns: 'repeat(1, 1fr)',
    },
    '@media (max-width: 600px)': {
        gridTemplateColumns: '1fr',
        width: '100%',
    },
});

const ErgoReelsTab: React.FC<ErgoReelsTabProps> = ({ selectedTopics }) => {
    const [playingVideoId, setPlayingVideoId] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoadingMore, setIsLoadingMore] = useState(false);

    const { data: EpErgoReels, isLoading, isError, refetch } = useList<ErgoReel>({
        resource: 'ep-ergo-reels',
        pagination: {
            current: 1,
            pageSize: currentPage * 6
        },
        meta: {
            populate: {
                video: {
                    fields: ['url']
                },
                thumbnail: {
                    fields: ['url']
                },
                topics: {
                    fields: ['id', 'title'],
                    populate: {
                        icon: {
                            fields: ['url']
                        }
                    }
                }
            },
            fields: ['id', 'title', 'description', 'displayOrder']
        }
    });

    const filteredReels = React.useMemo(() => {
        if (!selectedTopics.length) return EpErgoReels?.data;

        return EpErgoReels?.data.filter(reel =>
            reel.topics?.some(topic => selectedTopics.includes(topic.id as any))
        );
    }, [EpErgoReels?.data, selectedTopics]);

    const sortedReels = React.useMemo(() => {
        return filteredReels?.sort((a, b) => a.displayOrder - b.displayOrder) || [];
    }, [filteredReels]);

    const handleVideoPlay = (videoId: any) => {
        setPlayingVideoId(videoId);
    };

    const handleViewMore = async () => {
        setIsLoadingMore(true);
        setCurrentPage(prev => prev + 1);
        await refetch();
        setTimeout(() => { setIsLoadingMore(false) }, 1000)
    };

    if (isLoading) return (
        <Stack minHeight={"70vh"} direction="column" alignItems="center" justifyContent="center">
            <Loading />
        </Stack>
    );

    const hasMore = sortedReels.length < (EpErgoReels?.total ?? 0);

    // Handle error state
    if (isError) {
        return (
            <Stack minHeight={"50vh"} direction="column" alignItems="center" justifyContent="center">
                <StatusMessage
                    title="unable to load videos"
                    message={"unable to load the Ergo Reels. Please try again later."}
                    type="error"
                />
                <Box mt={3}>
                    <MLButton variant="outlined" onClick={() => refetch()}>
                        Try Again
                    </MLButton>
                </Box>
            </Stack>
        );
    }

    return (
        <Stack>
            {filteredReels && filteredReels.length > 0 ? (
                <Stack minHeight={"70vh"} direction="column" justifyContent="space-between" alignItems="space-between" gap="60px">
                    <CustomGridContainer>
                        {sortedReels.map((video: any) => (
                            <ErgoReelCard
                                key={video.id}
                                reelData={video}
                                isPlaying={playingVideoId === video.id}
                                onPlay={handleVideoPlay}
                            />
                        ))}
                    </CustomGridContainer>
                    {hasMore && (
                        <Box sx={{ width: "100%", display: "flex", justifyContent: "center", p: "8px" }}>
                            {isLoadingMore ? (
                                <Loading />
                            ) : (
                                <MLButton
                                    variant="outlined"
                                    endIcon={<ViewMoreDownIcon />}
                                    onClick={handleViewMore}
                                >
                                    View More
                                </MLButton>
                            )}
                        </Box>
                    )}
                </Stack>
            ) : (
                <Stack margin="auto">
                    <MLTypography>
                        {selectedTopics.length > 0
                            ? "No reels available for selected topics!"
                            : "No reels available!"}
                    </MLTypography>
                </Stack>
            )}
        </Stack>
    );
};

export default ErgoReelsTab;