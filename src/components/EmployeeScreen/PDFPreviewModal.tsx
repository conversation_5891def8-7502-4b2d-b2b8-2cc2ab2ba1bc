import React, { useState, useEffect } from 'react';
import { Box, IconButton, Stack, Typography, Button } from '@mui/material';
import { Close, Download, FileOpen } from '@mui/icons-material';
import Loading from '../../pages/Loading/Loading';

interface PDFPreviewModalProps {
    open: boolean;
    onClose: () => void;
    pdfUrl: string;
    fileName?: string;
}

const PDFPreviewModal: React.FC<PDFPreviewModalProps> = ({
    open,
    onClose,
    pdfUrl,
    fileName = 'document.pdf',
}) => {
    const [isDownloading, setIsDownloading] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [viewerError, setViewerError] = useState<boolean>(false);

    // Reset states when modal opens
    useEffect(() => {
        if (open) {
            setIsLoading(true);
            setViewerError(false);
        }
    }, [open]);

    // If modal is not open, don't render anything
    if (!open) return null;

    const handleDownload = async (): Promise<void> => {
        if (isDownloading || !pdfUrl) return;

        try {
            setIsDownloading(true);

            const response = await fetch(pdfUrl);
            if (!response.ok) {
                throw new Error('Download failed');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();

            // Cleanup
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading file:', error);
            alert('Failed to download the file. Please try again.');
        } finally {
            setIsDownloading(false);
        }
    };

    const handleOpenInNewTab = (): void => {
        window.open(pdfUrl, '_blank');
    };

    // Prepare Google Docs viewer URL
    const googleDocsViewerUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(pdfUrl)}&embedded=true`;

    // Handle iframe load events
    const handleIframeLoad = () => {
        setIsLoading(false);
    };

    const handleIframeError = () => {
        setIsLoading(false);
        setViewerError(true);
    };

    // Fallback view shown if PDF preview fails
    const renderFallbackView = (): JSX.Element => (
        <Box
            sx={{
                width: '90%',
                maxWidth: '400px',
                bgcolor: 'background.paper',
                borderRadius: 2,
                p: 3,
                display: 'flex',
                flexDirection: 'column',
                gap: 2
            }}
        >
            <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="h6" component="h2" sx={{
                    fontWeight: 600,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '80%'
                }}>
                    {fileName}
                </Typography>
                <IconButton onClick={onClose} size="small">
                    <Close />
                </IconButton>
            </Stack>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                PDF preview failed to load. Please try opening or downloading the file.
            </Typography>

            <Button
                variant="contained"
                startIcon={<FileOpen />}
                fullWidth
                onClick={handleOpenInNewTab}
                sx={{ mb: 1 }}
            >
                Open in Browser
            </Button>

            <Button
                variant="outlined"
                startIcon={<Download />}
                fullWidth
                onClick={handleDownload}
                disabled={isDownloading}
            >
                Download
            </Button>
        </Box>
    );

    return (
        <Box
            sx={{
                position: 'fixed',
                top: { xs: -20, sm: 0 },
                left: 0,
                width: '100vw',
                height: '100vh',
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                zIndex: 999999,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
            }}
            onClick={onClose} // Close on background click
        >
            {viewerError ? renderFallbackView() : (
                <Box
                    onClick={(e) => e.stopPropagation()} // Prevent closing when clicking on content
                    sx={{
                        width: { xl: "60%", lg: "70%", md: '90%', xs: "95%" },
                        maxWidth: '1200px',
                        height: { xl: "90vh", lg: "90vh", md: '90vh', xs: "80vh" },
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        overflow: 'hidden',
                        borderRadius: '4px',
                        bgcolor: 'white',
                    }}
                >
                    {/* Loading indicator */}
                    {isLoading && (
                        <Box
                            sx={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                zIndex: 3,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: 2
                            }}
                        >
                            <Loading />
                        </Box>
                    )}

                    {/* Header with actions - Moved to top outside of the iframe */}
                    <Stack
                        direction="row"
                        justifyContent="flex-end"
                        alignItems="center"
                        sx={{
                            p: 1,
                            zIndex: 2,
                            bgcolor: 'black',
                        }}
                    >
                        <Stack direction="row" spacing={1}>
                            <IconButton
                                onClick={handleDownload}
                                disabled={isDownloading}
                                sx={{
                                    color: 'white',
                                    '&.Mui-disabled': {
                                        color: 'rgba(255, 255, 255, 0.3)',
                                    },
                                }}
                            >
                                <Download />
                            </IconButton>
                            <IconButton onClick={onClose} sx={{ color: 'white' }}>
                                <Close />
                            </IconButton>
                        </Stack>
                    </Stack>

                    {/* Google Docs PDF Viewer - Takes remaining space */}
                    <Box sx={{ flexGrow: 1, height: 'calc(100% - 4px)' }}>
                        <iframe
                            src={googleDocsViewerUrl}
                            width="100%"
                            height="100%"
                            style={{
                                border: 'none',
                                display: isLoading ? 'none' : 'block'
                            }}
                            onLoad={handleIframeLoad}
                            onError={handleIframeError}
                            title={fileName}
                            sandbox="allow-scripts allow-same-origin allow-popups"
                        />
                    </Box>

                    {/* Direct fallback for absolute reliability */}
                    {viewerError && (
                        <Box
                            sx={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)'
                            }}
                        >
                            {renderFallbackView()}
                        </Box>
                    )}
                </Box>
            )}
        </Box>
    );
};

export default PDFPreviewModal;