import { LogicalFilter, useList } from "@refinedev/core";
import { ErgoVideo } from ".";
import { Box, Stack } from "@mui/material";
import { CustomGridContainer } from "../../pages/EmployeeScreen/Resources";
import React, { useState, useEffect } from "react";
import MLButton from "../ui/MLButton/MLButton";
import ViewMoreDownIcon from "../../assets/icons/ViewMoreDownIcon";
import ErgoVideoCard from "./ErgoVideoCard";
import ResourceVideoPopup from "./ResourceVideoPopup";
import Loading from "../../pages/Loading/Loading";
import MLTypography from "../ui/MLTypography/MLTypography";
import { StatusMessage } from "../../pages/MyAssessments/myAssessmentUtils";

interface ErgoVideosTabProps {
    selectedTopics: string[];
}

const ErgoVideosTab: React.FC<ErgoVideosTabProps> = ({ selectedTopics }) => {
    const [videoOpen, setVideoOpen] = useState(false);
    const [currentVideo, setCurrentVideo] = useState();
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    // Track if there are more videos to load
    const [hasMoreVideos, setHasMoreVideos] = useState(true);

    // Create filters based on selected topics
    const filters: LogicalFilter[] = React.useMemo(() => {
        if (selectedTopics.length === 0) return [];

        return [
            {
                field: "topics.id",
                operator: "in",
                value: selectedTopics
            }
        ];
    }, [selectedTopics]);

    // Reset pagination when filters change
    useEffect(() => {
        setCurrentPage(1);
        setHasMoreVideos(true);
    }, [selectedTopics]);

    const { data: EpVideos, isLoading, refetch, isError } = useList<ErgoVideo>({
        resource: 'ep-videos',
        pagination: {
            current: 1,
            pageSize: currentPage * 6
        },
        filters: filters,
        sorters: [
            {
                field: 'displayOrder',
                order: 'asc'
            }
        ],
        meta: {
            populate: {
                video: {
                    fields: ['url']
                },
                thumbnail: {
                    fields: ['url']
                },
                topics: {
                    fields: ['id', 'title'],
                    populate: {
                        icon: {
                            fields: ['url']
                        }
                    }
                }
            },
            fields: ['id', 'title', 'description', 'displayOrder']
        },
    });

    // Sort videos by display order
    const sortedVideos = React.useMemo(() => {
        return EpVideos?.data?.sort((a, b) => a.displayOrder - b.displayOrder) || [];
    }, [EpVideos?.data]);

    // Update hasMoreVideos when data changes
    useEffect(() => {
        if (EpVideos?.data) {
            // If we received fewer videos than requested, there are no more to load
            setHasMoreVideos(EpVideos.data.length < EpVideos.total);
        }
    }, [EpVideos?.data, EpVideos?.total]);

    const handleClickOpenVideoPopup = (video: any) => {
        setCurrentVideo(video);
        setVideoOpen(true);
    };

    const handleCloseVideoPopup = () => {
        setVideoOpen(false);
    };

    const handleViewMore = async () => {
        setIsLoadingMore(true);
        setCurrentPage(prev => prev + 1);
        await refetch();
        setIsLoadingMore(false);
    };

    if (isLoading) return (
        <Stack minHeight={"70vh"} direction="column" alignItems="center" justifyContent="center">
            <Loading />
        </Stack>
    );

    // Handle error state
    if (isError) {
        return (
            <Stack minHeight={"50vh"} direction="column" alignItems="center" justifyContent="center">
                <StatusMessage
                    title="Unable to load Ergo Videos"
                    message={"unable to load the Ergo Videos. Please try again later."}
                    type="error"
                />
                <Box mt={3}>
                    <MLButton variant="outlined" onClick={() => refetch()}>
                        Try Again
                    </MLButton>
                </Box>
            </Stack>
        );
    }

    return (
        <Stack>
            {sortedVideos && sortedVideos.length > 0 ? (
                <Stack minHeight={"70vh"} direction="column" justifyContent="space-between" alignItems="space-between" gap="60px">
                    <CustomGridContainer>
                        {sortedVideos.map((video) => (
                            <ErgoVideoCard
                                key={video.id}
                                videoData={video}
                                handleClickOpenVideoPopup={handleClickOpenVideoPopup}
                                hideViewButton={true} // Pass true to hide the VIEW VIDEO button
                            />
                        ))}
                    </CustomGridContainer>
                    {hasMoreVideos && (
                        <Box sx={{ width: "100%", display: "flex", justifyContent: "center", p: "8px" }}>
                            {isLoadingMore ? (
                                <Loading />
                            ) : (
                                <MLButton
                                    variant="outlined"
                                    endIcon={<ViewMoreDownIcon />}
                                    onClick={handleViewMore}
                                >
                                    View More
                                </MLButton>
                            )}
                        </Box>
                    )}
                </Stack>
            ) : (
                // <Stack margin="auto">
                //    <Box sx={{
                //         display: 'flex',
                //         alignItems: 'center',
                //         justifyContent: 'center',
                //         minHeight: '60vh'
                //     }}>
                //         <MLTypography sx={{ textAlign: 'center' }}>
                //             {selectedTopics && selectedTopics.length > 0
                //                 ? "No videos available for selected topics!"
                //                 : "No videos available!"}
                //         </MLTypography>
                //     </Box> 
                // </Stack>
                    <Box py={{ sx: 0, md: 2 }} display="flex" justifyContent="flex-start">
                        <StatusMessage
                            title={`No videos found`}
                            message={selectedTopics && selectedTopics.length > 0
                                ? "Reset the filters to explore videos from other categories!"
                                : "No videos available!"}
                            type="info"
                        />
                    </Box>

            )}
            {/* Video popup */}
            <ResourceVideoPopup
                open={videoOpen}
                onClose={handleCloseVideoPopup}
                videoData={currentVideo}
            />
        </Stack>
    );
};

export default ErgoVideosTab;