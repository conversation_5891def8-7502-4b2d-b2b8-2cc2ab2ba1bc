import React, { useState, useEffect } from "react";
import { useList, LogicalFilter } from "@refinedev/core";
import { ErgoVideo } from ".";
import { Box, Stack } from "@mui/material";
import PosterCard from "./PosterCard";
import Loading from "../../pages/Loading/Loading";
import { CustomGridContainer } from "../../pages/EmployeeScreen/Resources";
import { StatusMessage } from "../../pages/MyAssessments/myAssessmentUtils";
import MLButton from "../ui/MLButton/MLButton";
import ViewMoreDownIcon from "../../assets/icons/ViewMoreDownIcon";

interface PostersTabProps {
    selectedTopics?: string[];
}

/**
 * PostersTab component 
 * **Created**
 */
const PostersTab: React.FC<PostersTabProps> = ({ selectedTopics = [] }) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [hasMorePosters, setHasMorePosters] = useState(true);

    // Create filters based on selected topics
    const filters: LogicalFilter[] = React.useMemo(() => {
        if (selectedTopics.length === 0) return [];

        return [
            {
                field: "topics.id",
                operator: "in",
                value: selectedTopics
            }
        ];
    }, [selectedTopics]);

    // Reset pagination when filters change
    useEffect(() => {
        setCurrentPage(1);
        setHasMorePosters(true);
    }, [selectedTopics]);

    const { data: EpPosters, isLoading, isError, refetch } = useList<ErgoVideo>({
        resource: 'ep-posters',
        pagination: {
            current: 1,
            pageSize: currentPage * 6
        },
        filters: filters,
        sorters: [
            {
                field: 'createdAt',
                order: 'desc'  // Sort by newest first
            }
        ],
        meta: {
            populate: {
                topics: {
                    fields: ['id', 'title'],
                    populate: {
                        icon: {
                            fields: ['url']
                        }
                    }
                },
                thumbnail: {
                    fields: ['url'] // Only need url for display and download
                }
            },
            fields: [
                'id',
                'title',
                'description',
                'posterUrl'
            ]
        },
    });

    // console.log('EpPosters: ', EpPosters);


    // Update hasMorePosters when data changes
    useEffect(() => {
        if (EpPosters?.data) {
            // If we received fewer posters than requested, there are no more to load
            setHasMorePosters(EpPosters.data.length < EpPosters.total);
        }
    }, [EpPosters?.data, EpPosters?.total]);

    const handleViewMore = async () => {
        setIsLoadingMore(true);
        setCurrentPage(prev => prev + 1);
        await refetch();
        setIsLoadingMore(false);
    };

    if (isLoading && !isLoadingMore) return (
        <Stack minHeight="70vh" direction="column" alignItems="center" justifyContent="center">
            <Loading />
        </Stack>
    );

    // Handle error state
    if (isError) {
        return (
            <Stack minHeight={"50vh"} direction="column" alignItems="center" justifyContent="center">
                <StatusMessage
                    title="Unable to load Posters"
                    message={"unable to load the Posters. Please try again later."}
                    type="error"
                />
                <Box mt={3}>
                    <MLButton variant="outlined" onClick={() => refetch()}>
                        Try Again
                    </MLButton>
                </Box>
            </Stack>
        );
    }

    return (
        <Stack>
            {EpPosters?.data && EpPosters.data.length > 0 ? (
                <Stack minHeight="70vh" direction="column" justifyContent="space-between" alignItems="space-between" gap="60px">
                    <CustomGridContainer>
                        {EpPosters.data.map((poster) => (
                            <PosterCard key={poster.id} posterData={poster} />
                        ))}
                    </CustomGridContainer>

                    {hasMorePosters && (
                        <Box sx={{ width: "100%", display: "flex", justifyContent: "center", p: "8px" }}>
                            {isLoadingMore ? (
                                <Loading />
                            ) : (
                                <MLButton
                                    variant="outlined"
                                    endIcon={<ViewMoreDownIcon />}
                                    onClick={handleViewMore}
                                >
                                    View More
                                </MLButton>
                            )}
                        </Box>
                    )}
                </Stack>
            ) : (
                <Box py={{ sx: 0, md: 2 }} display="flex" justifyContent="flex-start">
                    <StatusMessage
                        title={`No posters found`}
                        message={selectedTopics && selectedTopics.length > 0
                            ? "Reset the filters to explore posters from other categories!"
                            : "No posters available!"}
                        type="info"
                    />
                </Box>
            )}
        </Stack>
    );
};

export default PostersTab;