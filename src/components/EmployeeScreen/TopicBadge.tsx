import React from 'react';
import { Box, Typography, styled } from '@mui/material';
import MLTypography from '../ui/MLTypography/MLTypography';

interface BadgeProps {
  imageUrl: string;
  label: string;
  imageWidth?: number;
  imageHeight?: number;
  className?: string;
  selected?: boolean;
  onClick?: () => void;
  variant?: 'filter' | 'article';
}

const BadgeWrapper = styled(Box)<{ variant: 'filter' | 'article'; selected: boolean }>(({ variant, selected }) => ({
  display: 'flex',
  padding: '5px 10px',
  alignItems: 'center',
  gap: '8px',
  borderRadius: '5px',
  width: 'fit-content',
  height: '32px', // Fixed height for consistency
  ...(variant === 'filter' && {
    border: selected ? '1px solid #7856FF' : '0.5px solid #C1C1C1',
    background: '#FFF',
    '&:hover': {
      cursor: 'pointer'
    }
  }),
  ...(variant === 'article' && {
    border: 'none',
    background: '#EAEAEA',
  })
}));

const IconImage = styled('img')({
  objectFit: 'contain'
});

const BadgeLabel = styled(Typography)({
  fontSize: '14px',
  fontWeight: 600,
});

const TopicBadge: React.FC<BadgeProps> = ({
  imageUrl,
  label,
  imageWidth = 16,
  imageHeight = 16,
  className,
  selected = false,
  onClick,
  variant = 'article'
}) => {
  return (
    <BadgeWrapper
      selected={selected}
      className={className}
      onClick={variant === 'filter' ? onClick : undefined}
      variant={variant}
      sx={{
        ...(variant === 'filter' && {
          backgroundColor: selected ? '#E3DDFF' : '#FFF',
          '&:hover': {
            backgroundColor: '#E4DDFF',
          }
        })
      }}
    >
      {/* <IconImage
        src={imageUrl}
        alt={label}
        width={imageWidth}
        height={imageHeight}
      /> */}
      <MLTypography
        sx={{
          fontWeight: 600,
          fontSize: { xs: '12px', sm: '14px' },
        }}
      >
        {label}
      </MLTypography>
    </BadgeWrapper>
  );
};

export default TopicBadge;