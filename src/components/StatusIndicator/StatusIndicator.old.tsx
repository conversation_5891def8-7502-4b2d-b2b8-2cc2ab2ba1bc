import Case, { CaseStatus } from "../../models/Case";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import { Chip, Stack } from "@mui/material";

interface StatusIndicatorProps {
  case: Case;
}

export default function StatusIndicator(props: StatusIndicatorProps) {
  const status = props.case.status;

  const assessmentChip = () => {
    switch (status) {
      case CaseStatus.CREATED:
        return <Chip label="Assessment" color="info" />;
      case CaseStatus.SCHEDULED:
        return <Chip label="Assessment" color="info" />;
      case CaseStatus.PENDING_APPROVAL:
        return <Chip label="Assessment" color="success" />;
    }
  };
  const reportChip = () => {
    switch (status) {
      case CaseStatus.CREATED:
        return <Chip label="Report" />;
      case CaseStatus.SCHEDULED:
        return <Chip label="Report" />;
      case CaseStatus.PENDING_APPROVAL:
        return <Chip label="Report" color="info" />;
    }
  };
  const followUpChip = () => {
    switch (status) {
      case CaseStatus.CREATED:
        return <Chip label="Follow Up" />;
      case CaseStatus.SCHEDULED:
        return <Chip label="Follow Up" />;
      case CaseStatus.PENDING_APPROVAL:
        return <Chip label="Follow Up" />;
    }
  };
  const closedChip = () => {
    switch (status) {
      case CaseStatus.CREATED:
        return <Chip label="Closed" />;
      case CaseStatus.SCHEDULED:
        return <Chip label="Closed" />;
      case CaseStatus.PENDING_APPROVAL:
        return <Chip label="Closed" />;
    }
  };

  return (
    <Stack direction="row" alignItems="center">
      {assessmentChip()}
      <NavigateNextIcon />
      {reportChip()}
      <NavigateNextIcon />
      {followUpChip()}
      <NavigateNextIcon />
      {closedChip()}
    </Stack>
  );
}
