import Case, { CaseStatus } from "../../models/Case";
import { formatEnumString } from "../../utils/enumUtils";
import MLButton from "../ui/MLButton/MLButton";
import MLTypography from "../ui/MLTypography/MLTypography";
import { ErTick } from "@mindlens/ergo-icons";
import { Done, PriorityHigh } from "@mui/icons-material";
import Check from "@mui/icons-material/Check";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import HistoryEduIcon from "@mui/icons-material/HistoryEdu";
import PublishedWithChangesIcon from "@mui/icons-material/PublishedWithChanges";
import SummarizeIcon from "@mui/icons-material/Summarize";
import WorkOutlineIcon from "@mui/icons-material/WorkOutline";
import { Box, Button, Stack, Typography, useTheme } from "@mui/material";
import { green, grey, orange, yellow } from "@mui/material/colors";
import { useGo } from "@refinedev/core";
import dayjs from "dayjs";
import { PropsWithChildren } from "react";
import CompletedIcon from '../../assets/icons/completedStatus.svg'
import AlertCircle from '../../assets/icons/alert-circle.svg'
import { capitalizeWords } from "../../utils/colorCodeUtils";

interface StatusIndicatorProps {
  case: Case;
}

const CaseStatuses: CaseStatus[] = [CaseStatus.CREATED];

const AssignScheduleStatuses: CaseStatus[] = [CaseStatus.ASSIGNED];

const AssessmentStatuses: CaseStatus[] = [
  CaseStatus.SCHEDULED,
  CaseStatus.ASSESSMENT_IN_PROGRESS,
];
const ReportStatuses: CaseStatus[] = [
  CaseStatus.ASSESSMENT_COMPLETED,
  CaseStatus.REPORT_CREATED,
  CaseStatus.PENDING_APPROVAL,
];
const followupStatuses: CaseStatus[] = [
  CaseStatus.REPORT_SHARED,
  CaseStatus.FOLLOW_UP,
];
const closedStatuses: CaseStatus[] = [CaseStatus.CLOSED];

const IconContainer = (
  props: PropsWithChildren<{ backgroundColor?: string }>,
) => {
  const { backgroundColor, children } = props;
  return (
    <Box
      style={{
        backgroundColor,
        borderRadius: "100%",
        width: "32px",
        minWidth: "32px",
        height: "32px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 2,
      }}
    >
      {children}
    </Box>
  );
};

const stateMap = new Map();
for (const stat of CaseStatuses) {
  stateMap.set(stat, 0);
}
for (const stat of AssignScheduleStatuses) {
  stateMap.set(stat, 1);
}
for (const stat of AssessmentStatuses) {
  stateMap.set(stat, 2);
}
for (const stat of ReportStatuses) {
  stateMap.set(stat, 3);
}
for (const stat of followupStatuses) {
  stateMap.set(stat, 4);
}
for (const stat of closedStatuses) {
  stateMap.set(stat, 5);
}

function WhiteCircle() {
  return (
    <div
      style={{
        height: 18,
        width: 18,
        borderRadius: "100%",
        backgroundColor: "white",
        border: "1px solid black",
        marginLeft: "7px",
        marginRight: "7px",
        zIndex: 9,
      }}
    ></div>
  );
}

export default function StatusIndicator(props: StatusIndicatorProps) {
  const status = props.case.status;
  const go = useGo();
  const theme = useTheme();

  if (status === CaseStatus.DELETED) {
    return (
      <MLTypography variant="subtitle2">Case has been deleted</MLTypography>
    );
  }

  const caseField = () => {
    return (
      <Stack
        direction="row"
        gap={2}
        justifyContent={"flex-start"}
        height={80}
        alignItems={"center"}
      >
        <IconContainer>
          <img height={30} width={30} src={CompletedIcon} />
        </IconContainer>
        <Stack
          direction={"column"}
          alignItems={"flex-start"}
          sx={{
            flex: 1,
          }}
        >
          <MLTypography variant={"body1"} fontWeight={"600"}>
            Case received
          </MLTypography>
          <MLTypography variant="body2" fontSize={"12px"}>
            Received on: {dayjs(props.case.createdAt).format("DD MMM YYYY")}
          </MLTypography>
        </Stack>
      </Stack>
    );
  };

  const assignField = () => {
    let bgColor: string = grey[500]; // grey by default
    if (stateMap.get(status) === 1) {
      bgColor = theme.palette.warning.main;
    } else if (stateMap.get(status) > 1) {
      bgColor = green[500];
    }
    return (
      <Stack
        direction="row"
        gap={2}
        justifyContent={"flex-start"}
        height={80}
        alignItems={"center"}
      >
        {stateMap.get(status) >= 1 ? (
          <IconContainer >
            {stateMap.get(status) === 1 ? (
              <img height={35} width={35} src={AlertCircle} />
            ) : (
              <img height={30} width={30} src={CompletedIcon} />
            )}
          </IconContainer>
        ) : (
          <WhiteCircle />
        )}
        <Stack
          direction={"column"}
          alignItems={"flex-start"}
          sx={{
            flex: 1,
          }}
        >
          <MLTypography variant={"body1"} fontWeight={"600"}>
            Assign and schedule
          </MLTypography>

          {props.case.assessmentAssignedTo?.username ? (
            <MLTypography variant="body2" fontSize={"12px"}>
              Assigned to: {capitalizeWords(props.case.assessmentAssignedTo.username)}
            </MLTypography>
          ) : null}
          {props.case.appointment ? (
            <MLTypography variant="body2" fontSize={"12px"}>
              Scheduled on:{" "}
              {dayjs(props.case.appointment.startTime).format("DD MMM YYYY")}
            </MLTypography>
          ) : null}
        </Stack>
      </Stack>
    );
  };

  const assessmentField = () => {
    let bgColor: string = grey[500]; // grey by default
    if (stateMap.get(status) === 2) {
      bgColor = theme.palette.warning.main;
    } else if (stateMap.get(status) > 2) {
      bgColor = green[500];
    }
    return (
      <Stack
        direction="row"
        gap={2}
        justifyContent={"flex-start"}
        height={80}
        alignItems={"center"}
      >
        {stateMap.get(status) >= 2 ? (
          <IconContainer>
            {stateMap.get(status) === 2 ? (
              <img height={35} width={35} src={AlertCircle} />
            ) : (
              <img height={30} width={30} src={CompletedIcon} />
            )}
          </IconContainer>
        ) : (
          <WhiteCircle />
        )}
        <Stack direction={"column"} sx={{ flex: 1 }}>
          <MLTypography variant={"body1"} fontWeight={"600"}>
            Assessment
          </MLTypography>

          <MLTypography variant="body2" fontSize={"12px"}>
            {stateMap.get(status) === 2 ? (
              <>
                {props.case.assessment
                  ? `Started on ${dayjs(props.case.assessment.createdAt).format(
                    "DD MMM YYYY",
                  )}`
                  : ""}
              </>
            ) : null}
            {stateMap.get(status) > 2 ? (
              <>
                {props.case.assessment?.completedOn
                  ? `Completed on: ${dayjs(
                    props.case.assessment.createdAt,
                  ).format("DD MMM YYYY")}`
                  : null}
              </>
            ) : null}
          </MLTypography>
        </Stack>
      </Stack>
    );
  };
  const reportField = () => {
    let bgColor: string = grey[500]; // grey by default
    if (stateMap.get(status) === 3) {
      bgColor = theme.palette.warning.main;
    } else if (stateMap.get(status) > 3) {
      bgColor = green[500];
    }

    const messages: { dayStr: string; message: string }[] = [];
    if (props.case.assessment?.report?.createdAt) {
      messages.push({
        dayStr: dayjs(props.case.assessment.report.createdAt).format(
          "DD MMM YYYY",
        ),
        message: "Report generated ",
      });
    }
    if (
      props.case.assessment?.report?.completed_on &&
      props.case.assessment.report.assigned_to?.username
    ) {
      messages.push({
        dayStr: dayjs(props.case.assessment.report.completed_on).format(
          "DD MMM YYYY",
        ),
        message:
          "Reviewed and edited by " +
          props.case.assessment.report.assigned_to.username,
      });
    }
    if (props.case.assessment?.report?.report_review?.reviewer) {
      messages.push({
        dayStr: dayjs(
          props.case.assessment.report.report_review.createdAt,
        ).format("DD MMM YYYY"),
        message:
          "Reviewed and approved by " +
          props.case.assessment?.report?.report_review?.reviewer.username,
      });
    }
    return (
      <Stack
        direction="row"
        gap={2}
        justifyContent={"flex-start"}
        height={80}
        alignItems={"center"}
      >
        {stateMap.get(status) >= 3 ? (
          <IconContainer>
            {stateMap.get(status) === 3 ? (
              <img height={35} width={35} src={AlertCircle} />
            ) : (
              <img height={30} width={30} src={CompletedIcon} />

            )}
          </IconContainer>
        ) : (
          <WhiteCircle />
        )}
        <Stack direction={"column"}>
          <MLTypography variant={"body1"} fontWeight={"600"}>
            Report
          </MLTypography>
          <div>
            {stateMap.get(status) >= 3 ? (
              <Stack gap={"4px"}>
                {messages.map((m, i) => (
                  <Stack
                    direction={"row"}
                    key={i}
                  >
                    {/* <Stack
                      direction={"row"}
                      gap={0.5}
                      alignItems={"center"}
                    >
                      <Check
                        sx={{
                          height: "12px",
                          width: "12px",
                          color: theme.palette.success.main,
                        }}
                      />
                      <MLTypography variant={"body2"}>{`m.message`}</MLTypography>
                    </Stack>
                    <MLTypography variant={"body2"} >{`${m.dayStr}`}</MLTypography> */}
                    <MLTypography variant={"body2"} fontSize={"12px"}>{`${m.message} on ${m.dayStr}`}</MLTypography>
                  </Stack>
                ))}

              </Stack>
            ) : null}
          </div>

          {/* {stateMap.get(status) === 3 ? (
            <MLButton
              size="small"
              endIcon={<ChevronRightIcon />}
              onClick={() =>
                go({ to: `/reports/edit/${props.case.assessment?.report?.id}` })
              }
            >
              Review report
            </MLButton>
          ) : null} */}
        </Stack>
      </Stack>
    );
  };

  const followUpField = () => {
    let bgColor: string = grey[500]; // grey by default
    let icon = <Done sx={{ color: "white" }} />;
    if (stateMap.get(status) > 4) {
      bgColor = green[500];
    } else if (stateMap.get(status) === 4) {
      if (
        props.case.followUpStatus === "not_required" ||
        props.case.followUpStatus === "completed"
      ) {
        bgColor = green[500];
      } else if (props.case.followUpStatus === "pending") {
        bgColor = theme.palette.warning.main;
        icon = <PriorityHigh sx={{ color: "black" }} />;
      }
    }
    return (
      <Stack
        direction="row"
        gap={2}
        justifyContent={"flex-start"}
        height={80}
        alignItems={"center"}
      >
        {stateMap.get(status) >= 4 ? (
          <IconContainer backgroundColor={bgColor}>{icon}</IconContainer>
        ) : (
          <WhiteCircle />
        )}
        <Stack
          direction={"column"}
          alignItems={"flex-start"}
          sx={{
            flex: 1,
          }}
        >
          <MLTypography variant={"body1"} fontWeight={"600"}>
            Follow up
          </MLTypography>

          {props.case.followUpUpdatedAt ? (
            <MLTypography variant="body2" fontSize={"12px"}>
              Updated on: {dayjs(props.case.followUpUpdatedAt).format("DD MMM YYYY")}
            </MLTypography>
          ) : null}

          {props.case.followUpStatus ? (
            <MLTypography variant="body2" fontSize={"12px"}>
              Status: {formatEnumString(props.case.followUpStatus)}
            </MLTypography>
          ) : null}
          {props.case.followUpMode ? (
            <MLTypography variant="body2" fontSize={"12px"}>
              Mode: {formatEnumString(props.case.followUpMode)}
            </MLTypography>
          ) : null}
        </Stack>
      </Stack>
    );
  };
  const closeField = () => {
    let bgColor: string = grey[500]; // grey by default
    if (stateMap.get(status) === 1) {
      bgColor = theme.palette.warning.main;
    } else if (stateMap.get(status) > 1) {
      bgColor = green[500];
    }
    return (
      <Stack
        direction="row"
        gap={2}
        justifyContent={"flex-start"}
        height={80}
        alignItems={"center"}
      >
        {stateMap.get(status) >= 5 ? (
          <IconContainer>
            <img height={30} width={30} src={CompletedIcon} />
          </IconContainer>
        ) : (
          <WhiteCircle />
        )}
        <Stack
          direction={"column"}
          alignItems={"flex-start"}
          sx={{
            flex: 1,
          }}
        >
          <MLTypography variant={"body1"} fontWeight={"600"}>
            Case closed
          </MLTypography>
          {props.case.closeTime ? (
            <MLTypography variant="body2" fontSize={"12px"}>
              {dayjs(props.case.closeTime).format("DD MMM YYYY")}
            </MLTypography>
          ) : null}
        </Stack>
      </Stack>
    );
  };
  // const followUpChip = () => {
  //   if (status === CaseStatus.CLOSED) {
  //     return <CheckCircleIcon color="success" />;
  //   } else if (followupStatuses.includes(status)) {
  //     return <PublishedWithChangesIcon color="warning" />;
  //   } else {
  //     return <CheckCircleIcon color="disabled" />;
  //   }
  // };
  // const closedChip = () => {
  //   if (status === CaseStatus.CLOSED) {
  //     return <CheckCircleIcon color="success" />;
  //   } else {
  //     return <CheckCircleIcon color="disabled" />;
  //   }
  // };

  let firstLineBgColor: string = grey[500]; // grey by default
  if (stateMap.get(status) === 1) {
    firstLineBgColor = theme.palette.warning.main;
  } else if (stateMap.get(status) > 1) {
    firstLineBgColor = green[500];
  }

  let secondLineBgColor: string = grey[500]; // grey by default
  if (stateMap.get(status) === 2) {
    secondLineBgColor = theme.palette.warning.main;
  } else if (stateMap.get(status) > 2) {
    secondLineBgColor = green[500];
  }

  let thirdLineBgColor: string = grey[500]; // grey by default
  if (stateMap.get(status) === 3) {
    thirdLineBgColor = theme.palette.warning.main;
  } else if (stateMap.get(status) > 3) {
    thirdLineBgColor = green[500];
  }

  let forthLineBgColor: string = grey[500]; // grey by default
  if (stateMap.get(status) === 4) {
    forthLineBgColor = green[500];
  } else if (stateMap.get(status) > 4) {
    forthLineBgColor = green[500];
  }
  let fifthLineBgColor: string = grey[500]; // grey by default
  if (stateMap.get(status) === 5) {
    fifthLineBgColor = green[500];
  } else if (stateMap.get(status) > 5) {
    fifthLineBgColor = green[500];
  }

  return (
    <div style={{ position: "relative" }}>
      <Stack direction="column" justifyContent={"flex-start"}>
        {caseField()}
        {assignField()}
        {assessmentField()}
        {reportField()}
        {followUpField()}
        {closeField()}
        {/* <Stack direction="row" gap={2}>
        {followUpChip()}
        <MLTypography variant="body2">Follow Up</MLTypography>
      </Stack>
      <Stack direction="row" gap={2}>
        {closedChip()}
        <MLTypography variant="body2">Closed</MLTypography>
      </Stack> */}
      </Stack>
      <div
        style={{
          height: 80,
          position: "absolute",
          top: 40,
          left: 15.5,
          borderLeft: `1px dashed ${firstLineBgColor}`,
          borderRight: `1px dashed ${firstLineBgColor}`,
        }}
      ></div>
      <div
        style={{
          height: 80,
          position: "absolute",
          top: 120,
          left: 15.5,
          borderLeft: `1px dashed ${secondLineBgColor}`,
          borderRight: `1px dashed ${secondLineBgColor}`,
        }}
      ></div>
      <div
        style={{
          height: 80,
          position: "absolute",
          top: 200,
          left: 15.5,
          borderLeft: `1px dashed ${thirdLineBgColor}`,
          borderRight: `1px dashed ${thirdLineBgColor}`,
        }}
      ></div>
      <div
        style={{
          height: 80,
          position: "absolute",
          top: 280,
          left: 15.5,
          borderLeft: `1px dashed ${forthLineBgColor}`,
          borderRight: `1px dashed ${forthLineBgColor}`,
        }}
      ></div>
      <div
        style={{
          height: 80,
          position: "absolute",
          top: 360,
          left: 15.5,
          borderLeft: `1px dashed ${fifthLineBgColor}`,
          borderRight: `1px dashed ${fifthLineBgColor}`,
        }}
      ></div>
    </div>
  );
}
