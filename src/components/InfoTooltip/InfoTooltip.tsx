import MLCard from "../ui/MLCard/MLCard";
import { Close } from "@mui/icons-material";
import InfoIcon from "@mui/icons-material/Info";
import { CardContent, IconButton, Popover } from "@mui/material";
import { useState } from "react";

interface InfoTooltipProps {
  popoverContent: JSX.Element;
}

export default function InfoTooltip(props: InfoTooltipProps) {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? "info-popover" : undefined;
  return (
    <>
      <IconButton onClick={handleClick}>
        <InfoIcon />
      </IconButton>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        slotProps={{
          paper: {
            sx: {
              borderRadius: "0.625rem",
            },
          },
        }}
      >
        <MLCard
          sx={{
            borderWidth: 0,
            p: 4,
            backgroundColor: "#e3ddff",
          }}
        >
          <CardContent sx={{ backgroundColor: "none" }}>
            <IconButton
              onClick={handleClose}
              sx={{
                position: "absolute",
                right: 24,
                top: 24,
              }}
            >
              <Close />
            </IconButton>
            {props.popoverContent}
          </CardContent>
        </MLCard>
      </Popover>
    </>
  );
}
