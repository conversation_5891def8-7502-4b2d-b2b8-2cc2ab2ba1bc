import Case, { CaseStatus, FollowUpStatus } from "../../models/Case";
import dayjs from "dayjs";

// Utility function to get the date range and month names for the current and previous month
export const getDateRanges = () => {
  const now = new Date();
  const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const nextMonthStart = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  const currentMonthEnd = new Date(nextMonthStart.getTime() - 1);

  const prevMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const prevMonthEnd = new Date(currentMonthStart.getTime() - 1);

  const currentMonthName = dayjs(currentMonthStart).format("MMMM");
  const prevMonthName = dayjs(prevMonthStart).format("MMMM");

  return {
    currentMonthRange: {
      from: currentMonthStart,
      to: currentMonthEnd,
    },
    prevMonthRange: {
      from: prevMonthStart,
      to: prevMonthEnd,
    },
    currentMonthName,
    prevMonthName,
  };
};

/**
 * Returns the appropriate action button configuration based on the case status.
 * @param caseObj - The case object.
 * @param go - The navigation function.
 * @returns An object containing the button label and the onClick handler.
 */

export const getActionButton = (caseObj: Case, go: any) => {
  switch (caseObj.status) {
    case CaseStatus.CREATED:
    case CaseStatus.ASSIGNED:
    case CaseStatus.CLOSED:
      return {
        label: "View case",
        onClick: () =>
          go({
            to: `/cases/show/${caseObj.id}`,
            options: { keepQuery: false },
          }),
      };
    case CaseStatus.SCHEDULED:
      if (!caseObj.assessment?.createdAt) {
        return {
          label: "View case",
          onClick: () =>
            go({
              to: `/cases/show/${caseObj.id}`,
              options: { keepQuery: false },
            }),
        };
      }
      return {
        label: "Begin assessment",
        onClick: () =>
          go({
            to: `/assessments/${caseObj.assessment!.id}`,
            options: { keepQuery: false },
          }),
      };
    case CaseStatus.ASSESSMENT_IN_PROGRESS:
      return {
        label: "Resume assessment",
        onClick: () =>
          go({
            to: `/assessments/${caseObj.assessment!.id}`,
            options: { keepQuery: false },
          }),
      };
    case CaseStatus.ASSESSMENT_COMPLETED:
    case CaseStatus.REPORT_CREATED:
    case CaseStatus.PENDING_APPROVAL:
      return {
        label: "Review Report",
        onClick: () =>
          go({
            to: `/reports/edit/${caseObj.assessment!.report!.id}`,
            options: { keepQuery: false },
          }),
      };
    case CaseStatus.REPORT_SHARED:
      return {
        label: "Follow up case",
        onClick: () =>
          go({
            to: `/cases/followup/${caseObj.id}`,
            options: { keepQuery: false },
          }),
      };
    case CaseStatus.FOLLOW_UP:
      if (caseObj.assessment?.case?.followUpStatus === "pending") {
        return {
          label: "Follow up case",
          onClick: () =>
            go({
              to: `/cases/followup/${caseObj.id}`,
              options: { keepQuery: false },
            }),
        };
      } else if (caseObj.assessment?.case?.followUpStatus === "completed") {
        return {
          label: "Follow-up is completed",
          onClick: () => {},
        };
      }
      return {
        label: "Close case",
        onClick: () =>
          go({
            to: `/cases/close/${caseObj.id}`,
            options: { keepQuery: false },
          }),
      };
    default:
      return {
        label: "View case",
        onClick: () =>
          go({
            to: `/cases/show/${caseObj.id}`,
            options: { keepQuery: false },
          }),
      };
  }
};

/**
 * Returns a status message based on the case status and other related parameters.
 * @param status - The current status of the case.
 * @param createdAt - The date the case was created.
 * @param hasAssessor - Whether the case has been assigned to an assessor.
 * @param isScheduled - Whether the case has been scheduled.
 * @param followUpStatus - The follow-up status of the case (optional).
 * @returns A JSX element or string containing the status message.
 */

export const getStatusMessage = (
  status: CaseStatus,
  createdAt: Date,
  hasAssessor: boolean,
  isScheduled: boolean,
  followUpStatus?: FollowUpStatus, // Additional parameter for follow-up status
) => {
  const formattedDate = dayjs(createdAt).format("DD/MM/YYYY");
  const commonStyles = {
    fontFamily: "Work Sans",
    fontSize: "14px",
    fontWeight: "regular",
  };

  switch (status) {
    case CaseStatus.CREATED:
      if (!hasAssessor)
        return (
          <span style={commonStyles}>
            Pending case assignment <br /> Case created on {formattedDate}
          </span>
        );
      if (!isScheduled)
        return (
          <span style={commonStyles}>
            Pending case scheduling <br /> Assigned Case on {formattedDate}
          </span>
        );

      return <span style={commonStyles}>Case created on {formattedDate}</span>;
    case CaseStatus.ASSIGNED:
      return (
        <span style={commonStyles}>
          Assessment not started <br /> Case assigned on {formattedDate}
        </span>
      );
    case CaseStatus.SCHEDULED:
      return (
        <span style={commonStyles}>
          Assessment not started <br /> Case scheduled on {formattedDate}
        </span>
      );
    case CaseStatus.ASSESSMENT_IN_PROGRESS:
      return (
        <span style={commonStyles}>
          Assessment in progress <br /> Assessment started on {formattedDate}
        </span>
      );
    case CaseStatus.ASSESSMENT_COMPLETED:
      return (
        <span style={commonStyles}>
          Assessment completed on {formattedDate}
        </span>
      );
    case CaseStatus.REPORT_CREATED:
      return (
        <span style={commonStyles}>
          Pending report approval <br /> Report generated on {formattedDate}
        </span>
      );
    case CaseStatus.PENDING_APPROVAL:
      return (
        <span style={commonStyles}>
          Pending report shared <br /> Report generated on {formattedDate}
        </span>
      );
    case CaseStatus.REPORT_SHARED:
      return (
        <span style={commonStyles}>
          Pending follow up <br /> Report shared on {formattedDate}
        </span>
      );
    case CaseStatus.FOLLOW_UP:
      if (followUpStatus === "pending") {
        return (
          <span style={commonStyles}>
            Follow up pending <br /> Redirect to close case
          </span>
        );
      } else if (followUpStatus === "completed") {
        return (
          <span style={commonStyles}>
            Follow-up completed <br /> Redirect to close case
          </span>
        );
      }
      return (
        <span style={commonStyles}>
          Follow-up required <br /> Since {formattedDate}
        </span>
      );
    case CaseStatus.CLOSED:
      return (
        <span style={commonStyles}>
          Case completed <br /> Case closed on {formattedDate}
        </span>
      );
    default:
      return <span style={commonStyles}>Case created on {formattedDate}</span>;
  }
};
