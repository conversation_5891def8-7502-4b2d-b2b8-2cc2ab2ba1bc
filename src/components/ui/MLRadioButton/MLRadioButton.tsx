// import { Box, RadioProps, useRadioGroup } from "@mui/material";

// export default function MLRadioButton(props: RadioProps) {
//   const { value, name } = props;
//   const radioGroup = useRadioGroup();
//   const checked = radioGroup?.value === value;
//   const handleChange = (e: React.MouseEvent<HTMLDivElement>) => {
//     if (radioGroup) {
//       radioGroup.onChange(
//         e as unknown as React.ChangeEvent<HTMLInputElement>,
//         value as string,
//       );
//     }
//   };
//   return (
//     <Box
//       onClick={(e) => handleChange(e)}
//       sx={{
//         width: 40,
//         height: 40,
//         backgroundColor: checked ? "primary.main" : "grey.300",
//         display: "flex",
//         justifyContent: "center",
//         alignItems: "center",
//         cursor: "pointer",
//         "&:hover": {
//           backgroundColor: "primary.light",
//         },
//       }}
//     >
//       {checked && (
//         <Box sx={{ width: 20, height: 20, backgroundColor: "white" }} />
//       )}
//     </Box>
//   );
// }

import React from "react";
import { Radio, RadioProps, SvgIcon } from "@mui/material";

// Custom SVG components for selected and unselected states
const CustomCheckedIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <circle cx="12" cy="12" r="11.5" stroke="#7856FF" />
      <circle cx="12" cy="12" r="8" fill="#7856FF" />
    </svg>
  </SvgIcon>
);

const CustomUncheckedIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <circle cx="12" cy="12" r="11.5" stroke="#7856FF" />
    </svg>
  </SvgIcon>
);

// Customized reusable radio component
const MLRadioButton: React.FC<RadioProps> = (props) => {
  return (
    <Radio
      icon={<CustomUncheckedIcon />}         // Custom unchecked state icon
      checkedIcon={<CustomCheckedIcon />}    // Custom checked state icon
      {...props}                             // Pass through props like `value`, `checked`, `onChange`
    />
  );
};

export default MLRadioButton;
