import React from 'react';
import Autocomplete, { AutocompleteProps } from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import Checkbox from '@mui/material/Checkbox';
import UncheckedIcon from '../../../../src/assets/icons/UncheckedIcon';
import CheckedIcon from '../../../../src/assets/icons/CheckedIcon';
import { IconButton } from '@mui/material';
import Chip from '@mui/material/Chip';
import DeleteCrossIcon from "../../../../src/assets/icons/DeleteCrossIcon"

export interface OptionType {
    [key: string]: any;
}

export interface MLSelecttagProps<T extends OptionType> {
    heading: string;
    value: T[];
    placeholder: string;
    options: T[];
    onChange: (value: T[]) => void;
    labelKey: keyof T;
    valueKey: keyof T;
}
const CustomIcon = () => {
    return (
        <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
        }}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                <path d="M4 6.5L8 10.5L12 6.5" stroke="#7856FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
        </div >
    )
};

const StyledAutocomplete = styled(Autocomplete)`
    .MuiOutlinedInput-root {
        border-radius: 10px;
        margin-top: 8px;
        padding:0px 8px;
    }
    .MuiAutocomplete-endAdornment {
        right: 8px;
        margin-right:8px;
    }
    & .MuiAutocomplete-paper {
        background-color: #F4F0FF;
    }
    & .MuiAutocomplete-option {
        &[data-focus="true"] {
            background-color: #E8E0FF;
        }
        &[aria-selected="true"] {
            background-color: #FFFFFF;
            &:hover {
                background-color: #E8E0FF;
            }
        }
    }
    & .MuiAutocomplete-tag {
        background-color: #FFFFFF;
        border: 1px solid #D0D0D0;
        border-radius: 4px;
        margin:8px;
        color: black;
        & .MuiChip-deleteIcon {
            color: #666666;
            &:hover {
                color: #000000;
            }
        }
    }
` as typeof Autocomplete;

function MLMultiSelecttag<T extends OptionType>({
    heading,
    value,
    options,
    onChange,
    placeholder,
    labelKey,
    valueKey
}: MLSelecttagProps<T>) {
    const isOptionType = (option: unknown): option is T => {
        return typeof option === 'object' && option !== null && labelKey in option;
    };

    return (
        <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
            <Typography sx={{
                fontSize: '16px',
                fontWeight: 500,
                lineHeight: '120%',
            }}>
                {heading}
            </Typography>
            <StyledAutocomplete
                multiple
                popupIcon={<CustomIcon />}
                value={value}
                options={options}
                disableCloseOnSelect
                getOptionLabel={(option) => isOptionType(option) ? String(option[labelKey]) : ''}
                onChange={(e, newValue) => onChange(newValue as T[])}
                renderTags={(value, getTagProps) =>
                    value.map((option, index) => {
                        if (isOptionType(option)) {
                            return (
                                <Chip
                                    label={String(option[labelKey])}
                                    {...getTagProps({ index })}
                                    deleteIcon={
                                        <IconButton
                                            onMouseDown={(event) => {
                                                event.stopPropagation();
                                            }}
                                        >
                                            <DeleteCrossIcon />
                                        </IconButton>
                                    }
                                />
                            );
                        }
                        return null;
                    })
                }
                renderOption={(props, option, { selected }) => {
                    if (isOptionType(option)) {
                        return (
                            <li
                                {...props}
                                className={selected ? 'option-selected' : "option"}
                                style={{
                                    backgroundColor: selected ? '#FFFFFF' : undefined,
                                    color: 'black',
                                }}
                            >
                                <Checkbox
                                    icon={<UncheckedIcon />}
                                    checkedIcon={<CheckedIcon />}
                                    style={{ marginRight: 8 }}
                                    checked={selected}
                                />
                                {String(option[labelKey])}
                            </li>
                        );
                    }
                    return null;
                }}
                isOptionEqualToValue={(option, value) =>
                    isOptionType(option) && isOptionType(value) && option[valueKey] === value[valueKey]
                }
                sx={{ width: "100%" }}
                renderInput={(params) => (
                    <TextField {...params} variant="outlined" placeholder={placeholder} />
                )}
            />
        </div>
    );
}

export default MLMultiSelecttag;