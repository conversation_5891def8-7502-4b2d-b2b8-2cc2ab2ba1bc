import React from 'react';
import MLButton from '../MLButton/MLButton';
import { Stack } from '@mui/material';
import MLTypography from '../MLTypography/MLTypography';

// Props type definitions
type MLMobileButtonProps = {
  value: string | number;
  label: string;
  isSelected?: boolean;
  onPress?: (value: string | number) => void;
  color?: string;
};

const MLMobileButton: React.FC<MLMobileButtonProps> = ({
  value,
  label,
  isSelected = false,
  onPress,
}) => (
  <MLButton
    sx={{
      textTransform: 'none',
      '& .MuiTouchRipple-root': {
        display: 'none'
      },
      WebkitTapHighlightColor: 'transparent'
    }}
    variant={isSelected ? "contained" : "outlined"}
    onClick={() => onPress && onPress(value)}
  >
    <MLTypography marginX={"8px"} variant='body1' fontSize={"13px"} fontWeight={400} color={isSelected ? "white" : "black"}>
      {label}
    </MLTypography>
  </MLButton>
);

type MLMobileButtonGroupProps = {
  value: any;
  onChange: (value: any) => void;
  children: React.ReactElement<MLMobileButtonProps> | React.ReactElement<MLMobileButtonProps>[];
  color?: string;
  exclusive?: boolean;
};

const MLMobileButtonGroup: React.FC<MLMobileButtonGroupProps> = ({
  value,
  onChange,
  children,
  color,
  exclusive = false
}) => {
  const handlePress = (buttonValue: string | number) => {
    if (exclusive) {
      onChange(value === buttonValue ? null : buttonValue);
    } else {
      if (Array.isArray(value)) {
        const newValue = value.includes(buttonValue)
          ? value.filter(v => v !== buttonValue)
          : [...value, buttonValue];
        onChange(newValue as any);
      } else {
        onChange(buttonValue);
      }
    }
  };

  return (
    <Stack gap={1} direction={"row"} flexWrap={"wrap"}>
      {React.Children.map(children, child =>
        React.cloneElement(child, {
          isSelected: exclusive
            ? child.props.value === value
            : (Array.isArray(value) && value.includes(child.props.value)),
          onPress: handlePress,
          color
        })
      )}
    </Stack>
  );
};

export { MLMobileButtonGroup, MLMobileButton };