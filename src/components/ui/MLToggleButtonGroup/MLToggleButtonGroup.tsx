import "./styles.css";
import {
  ToggleButtonGroup,
  ToggleButtonGroupProps,
  colors,
  styled,
} from "@mui/material";

const MLToggleButtonGroup = styled(ToggleButtonGroup)<ToggleButtonGroupProps>(
  ({ theme }) => ({
    "& .MuiToggleButton-root.Mui-selected": {
      backgroundColor: theme.palette.primary.main,
      color: "white !important",
    },
  }),
);

export default MLToggleButtonGroup;
