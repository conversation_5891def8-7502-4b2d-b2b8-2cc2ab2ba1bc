import MLToggleButton from "../MLToggleButton/MLToggleButton";
import MLToggleButtonGroup from "./MLToggleButtonGroup";

export default {
  component: MLToggleButtonGroup,
};

/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */
export const Primary = {
  render: () => (
    <MLToggleButtonGroup value={"one"}>
      <MLToggleButton value={"one"}>One</MLToggleButton>
      <MLToggleButton value={"two"}>Two</MLToggleButton>
      <MLToggleButton value={"three"}>Three</MLToggleButton>
    </MLToggleButtonGroup>
  ),
};

export const Secondary = {
  render: () => (
    <MLToggleButtonGroup color="secondary" value={"one"}>
      <MLToggleButton color="secondary" value={"one"}>
        One
      </MLToggleButton>
      <MLToggleButton color="secondary" value={"two"}>
        Two
      </MLToggleButton>
      <MLToggleButton color="secondary" value={"three"}>
        Three
      </MLToggleButton>
    </MLToggleButtonGroup>
  ),
};
