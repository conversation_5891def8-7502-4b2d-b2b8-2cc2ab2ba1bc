import { API_URL } from "../../../constants";
import { MediaAttachment } from "../../../models/MediaAttachment";
import CloseIcon from "@mui/icons-material/Close";
import { Dialog, IconButton } from "@mui/material";

interface ImageModalProps {
  open: boolean;
  onClose: (event: unknown, reason: "backdropClick" | "escapeKeyDown") => void;
  media?: MediaAttachment;
  url?: string;
}

export default function ImageDialog(props: ImageModalProps) {
  const { open, onClose, media, url } = props;

  if (!media && !url) {
    return <></>;
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        // maxHeight: "100vh",
      }}
      // aria-labelledby="modal-modal-title"
      // aria-describedby="modal-modal-description"
    >
      {media ? (
        <img
          src={`${media.url}`}
          srcSet={`${media.url}`}
          alt={media.caption}
          loading="lazy"
          style={{
            height: "100%",
            width: "100%",
            objectFit: "cover",
          }}
        />
      ) : null}
      {!media && url ? (
        <img
          src={url}
          srcSet={url}
          alt={"preview image"}
          loading="lazy"
          style={{
            height: "100%",
            width: "100%",
            objectFit: "cover",
          }}
        />
      ) : null}

      <IconButton
        aria-label="close"
        onClick={() => onClose({}, "backdropClick")}
        sx={{
          position: "absolute",
          right: 8,
          top: 8,
          color: (theme) => theme.palette.grey[500],
        }}
      >
        <CloseIcon />
      </IconButton>
    </Dialog>
  );
}
