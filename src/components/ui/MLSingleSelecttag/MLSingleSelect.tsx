import React, { ReactNode, useState } from 'react';
import { styled } from '@mui/material/styles';
import { Select, SelectChangeEvent, MenuItem, Box } from '@mui/material';
import RequiredStarIcon from '../../../assets/icons/RequiredStarIcon';

const CustomIcon: React.FC<{ open: boolean }> = ({ open }) => (
    <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: '10px',
        top: '50%',
        transform: open ? 'translateY(-50%) rotate(180deg)' : 'translateY(-50%)',
        transition: 'transform 0.5s ease'
    }}>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
            <path d="M4 6.5L8 10.5L12 6.5" stroke="#9C9C9C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    </div>
);

interface StyledSelectWrapperProps {
    disabledTextColor?: string;
}

const StyledSelectWrapper = styled('div')<StyledSelectWrapperProps>(({ theme, disabledTextColor }) => ({
    '.MuiOutlinedInput-root': {
        borderRadius: '10px',
        padddingRight: "30px",
        position: 'relative',
        '&.Mui-disabled .MuiSelect-select': {
            color: disabledTextColor || '#6B7280',
            WebkitTextFillColor: disabledTextColor || '#6B7280',
        }
    },
    '& .MuiAutocomplete-paper': {
        backgroundColor: '#F4F0FF',
    },
    '& .MuiAutocomplete-option': {
        '&[data-focus="true"]': {
            backgroundColor: '#E8E0FF',
        },
        '&[aria-selected="true"]': {
            backgroundColor: '#FFFFFF',
            '&:hover': {
                backgroundColor: '#E8E0FF',
            },
        },
    },
    '& .MuiAutocomplete-tag': {
        backgroundColor: '#FFFFFF',
        border: '1px solid #D0D0D0',
        borderRadius: '4px',
        color: 'black',
        '& .MuiChip-deleteIcon': {
            color: '#666666',
            '&:hover': {
                color: '#000000',
            },
        },
    },
    '& .MuiOutlinedInput-input': {
        padding: "8px"
    },
}));

interface CustomSelectProps {
    heading: string;
    placeholder?: string;
    value: string | number | undefined;
    name?: string;
    onChange: (event: SelectChangeEvent<string | number>) => void;
    onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
    children: ReactNode;
    disabled?: boolean;
    fullWidth?: boolean;
    size?: 'small' | 'medium';
    key?: React.Key;
    sx?: any;
    required?: boolean;
    disabledTextColor?: string; // Add new prop for disabled text color
}

const MLSingleSelect: React.FC<CustomSelectProps> = ({ 
    heading, 
    value, 
    name, 
    children, 
    placeholder, 
    onBlur, 
    required, 
    disabledTextColor = '#6B7280', // Default to specified color
    ...props 
}) => {
    const [open, setOpen] = useState(false);
    
    return (
        <div style={{ display: "flex", flexDirection: "column", gap: heading ? "8px" : "" }}>
            <Box
                sx={{
                    fontSize: '16px',
                    fontWeight: 500,
                    lineHeight: '120%',
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "flex-start",
                    fontFamily: 'work sans'
                }}
            >
                {heading}
                {
                    required &&
                    <Box sx={{ ml: 0.5 }}>
                        <RequiredStarIcon />
                    </Box>
                }
            </Box>
            <StyledSelectWrapper disabledTextColor={disabledTextColor}>
                <Select
                    IconComponent={() => <CustomIcon open={open} />}
                    displayEmpty
                    {...props}
                    value={value}
                    name={name}
                    required
                    onOpen={() => setOpen(true)}
                    onClose={() => setOpen(false)}
                    onBlur={onBlur}
                    sx={{
                        ...props.sx,
                        '&.Mui-disabled .MuiSelect-select': {
                            color: disabledTextColor,
                            WebkitTextFillColor: disabledTextColor,
                            opacity: 1,
                        }
                    }}
                >
                    {!open && value === '' && (
                        <MenuItem value="" disabled>
                            {placeholder || 'Select a value'}
                        </MenuItem>
                    )}
                    {children}
                </Select>
            </StyledSelectWrapper>
        </div>
    )
};

export default MLSingleSelect;