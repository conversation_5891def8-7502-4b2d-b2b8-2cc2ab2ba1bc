import MLTypography from "../MLTypography/MLTypography";
import MLButton from "./MLButton";
import { ChevronRight } from "@mui/icons-material";
import { Stack } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/preact";

const buttonVariants = ["text", "outlined", "contained"];

const meta = {
  component: MLButton,
  argTypes: {
    variant: {
      options: buttonVariants,
      control: { type: "radio" },
      mapping: Object.fromEntries(buttonVariants.map((e) => [e, e])),
    },
  },
  args: {
    children: "Button",
  },
} satisfies Meta<typeof MLButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */
export const Contained: Story = {
  render: () => (
    <div>
      <MLTypography>Small</MLTypography>
      <Stack direction={"row"} gap={2}>
        <MLButton variant="contained" size="small">
          {meta.args.children}
        </MLButton>
        <MLButton variant="contained" size="small" color="secondary">
          {meta.args.children}
        </MLButton>
        <MLButton variant="contained" size="small" endIcon={<ChevronRight />}>
          {meta.args.children}
        </MLButton>
        <MLButton variant="contained" size="small" startIcon={<ChevronRight />}>
          {meta.args.children}
        </MLButton>
      </Stack>
      <MLTypography>Medium</MLTypography>
      <Stack direction={"row"} gap={2}>
        <MLButton variant="contained">{meta.args.children}</MLButton>
        <MLButton variant="contained" color="secondary">
          {meta.args.children}
        </MLButton>
        <MLButton variant="contained" endIcon={<ChevronRight />}>
          {meta.args.children}
        </MLButton>
        <MLButton variant="contained" startIcon={<ChevronRight />}>
          {meta.args.children}
        </MLButton>
      </Stack>
    </div>
  ),
};

export const Text: Story = {
  render: () => (
    <Stack direction={"row"} gap={2}>
      <MLButton variant="text">{meta.args.children}</MLButton>
      <MLButton variant="text" color="secondary">
        {meta.args.children}
      </MLButton>
      <MLButton variant="text" endIcon={<ChevronRight />}>
        {meta.args.children}
      </MLButton>
    </Stack>
  ),
};

export const Outlined: Story = {
  render: () => (
    <Stack direction={"row"} gap={2}>
      <MLButton variant="outlined">{meta.args.children}</MLButton>
      <MLButton variant="outlined" color="secondary">
        {meta.args.children}
      </MLButton>
      <MLButton variant="outlined" endIcon={<ChevronRight />}>
        {meta.args.children}
      </MLButton>
    </Stack>
  ),
};
