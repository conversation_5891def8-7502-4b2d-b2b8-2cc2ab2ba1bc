import { Button, ButtonProps, styled } from "@mui/material";

const MLButton = styled(Button)({
  //   borderRadius: "0.5rem",
  //   padding: "0.5rem",
  //   lineHeight: "1",
  //   "&.MuiButton-sizeSmall": {
  //     paddingTop: "0.25rem",
  //     paddingBottom: "0.25rem",
  //     fontSize: "0.75rem",
  //   },
  //   "&.Mui<PERSON>utton-colorSecondary, &.Mui<PERSON>utton-containedSecondary": {
  //     // contained secondary is deprecated, can remove in the future
  //     border: "1px solid #333333",
  //   },
}) as typeof Button;
export default MLButton;
