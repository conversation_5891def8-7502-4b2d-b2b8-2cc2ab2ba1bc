import { Tabs, TabsProps, useTheme } from "@mui/material";
import { orange } from "@mui/material/colors";

const MLTabs = (props: TabsProps) => {
  const theme = useTheme();
  return (
    <Tabs
      {...props}
      sx={{
        "& .MuiTabs-scroller > .MuiTabs-flexContainer": {
          gap: "2rem",
        },
        ...props.sx,
      }}
      TabIndicatorProps={{
        sx: {
          backgroundColor: theme.palette.mlOrange.main,
          height: "3.5px",
          mb: 0.8
        },
      }}
    />
  );
};
export default MLTabs;
