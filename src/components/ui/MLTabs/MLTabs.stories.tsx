import MLTab from "../MLTab/MLTab";
import MLTabs from "./MLTabs";
import type { Meta, StoryObj } from "@storybook/preact";

const meta = {
  component: MLTabs,
  args: {
    children: "Tabs",
  },
} satisfies Meta<typeof MLTabs>;

export default meta;
type Story = StoryObj<typeof meta>;

/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */
export const Default: Story = {
  render: () => (
    <div>
      <MLTabs value={2}>
        <MLTab value={1} label="Item One" />
        <MLTab value={2} label="Item Two" />
        <MLTab value={3} label="Item Three" />
      </MLTabs>
    </div>
  ),
};
