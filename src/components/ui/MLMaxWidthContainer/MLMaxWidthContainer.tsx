import React, { useContext } from 'react';
import { Box, BoxProps } from '@mui/material';
import { AuthContext } from '../../../contexts/authContext/AuthContext';
import { desktop, tablet } from '../../../responsiveStyles';
import { ViewModeContext } from '../../../contexts/ViewModeContext/ViewModeContext';

interface MLContainerProps extends BoxProps {
    children: React.ReactNode;
    forceContainer?: boolean; // Optional prop to force container style regardless of user role
    usePaddingY?: boolean; // Optional prop to use top and bottom padding; default false
}

const MLContainer: React.FC<MLContainerProps> = ({
    children,
    sx,
    forceContainer = false,
    usePaddingY = false,
    ...props
}) => {
    const { isEmployeeView } = useContext(ViewModeContext);

    const { userDetails } = useContext(AuthContext);
    const isEmployee = userDetails?.role?.name.toLowerCase() === "employee";
    // const { isEmployeeView } = useContext(ViewModeContext)
    // console.log('isEmployeeView: ', isEmployeeView);

    // Apply container styles only if user is employee or if forceContainer is true
    const shouldApplyContainer = isEmployee || forceContainer || userDetails?.employeeView;
    // console.log('ML max container userDetails?.employeeView: ', userDetails?.employeeView);

    return (
        <Box
            // sx={{
            //     ...(shouldApplyContainer && {
            //         maxWidth: '1440px',
            //         width: '100%',
            //         m: '0 auto',
            //         paddingX: {
            //             lg: desktop.contentContainer.paddingX,
            //             // lg: 0,
            //             md: tablet.contentContainer.paddingX,
            //             xs: tablet.contentContainer.paddingX,
            //         },
            //     }),
            //     ...sx
            // }}
            sx={{
                ...(isEmployeeView && { // we only use this styling if user is in employee view
                    maxWidth: '1440px',
                    width: '100%',
                    m: '0 auto',
                }),
                paddingX: {
                    lg: desktop.contentContainer.paddingX,
                    md: tablet.contentContainer.paddingX,
                    xs: tablet.contentContainer.paddingX,
                },
                ...(usePaddingY && {
                    paddingY: {
                        lg: desktop.contentContainer.paddingY,
                        md: tablet.contentContainer.paddingY,
                        xs: tablet.contentContainer.paddingY,
                    },
                }),
                ...sx
            }}
            {...props}
        >
            {children}
        </Box>
    );
};

export default MLContainer;