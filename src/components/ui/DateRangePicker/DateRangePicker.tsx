import MLButton from "../MLButton/MLButton";
import "./style.css";
import { ErCalendar } from "@mindlens/ergo-icons";
import { Button, Popover, Stack, Typography, useMediaQuery, useTheme } from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { addDays } from "date-fns";
import dayjs from "dayjs";
import React, { Dispatch, useEffect, useState } from "react";
import { DayPicker, DateRange, ClassNames } from "react-day-picker";
import styles from "react-day-picker/dist/style.module.css";

interface DateRangePickerProps {
  date: DateRange | undefined;
  setDate: Dispatch<DateRange | undefined>;
}

export default function DateRangePicker(props: DateRangePickerProps) {
  const { date, setDate } = props;

  const [innerDate, setInnerDate] = React.useState<DateRange | undefined>();
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
    null,
  );

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setDate(innerDate);
    setAnchorEl(null);
  };
  const classNames: ClassNames = {
    ...styles,
  };
  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  useEffect(() => {
    setInnerDate(date);
  }, [date]);

  const theme = useTheme();
  const matchDownMd = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <>
      <MLButton
        aria-describedby={id}
        onClick={handleClick}
        startIcon={<ErCalendar height={20} width={20} />}
      >
        {innerDate
          ? `${
              innerDate.from ? dayjs(innerDate.from).format("DD MMM") : ""
            } - ${innerDate.to ? dayjs(innerDate.to).format("DD MMM") : ""}`
          : "Select date"}
      </MLButton>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <Stack direction={"column"} alignItems={"center"} p={2}>
          <DayPicker
            initialFocus
            mode="range"
            defaultMonth={innerDate?.from}
            selected={innerDate}
            onSelect={setInnerDate}
            numberOfMonths={matchDownMd ? 1 : 2 }
            showOutsideDays={true}
            classNames={classNames}
          />
          <Stack direction={"row"} gap={2}>
            <MLButton
              variant={"outlined"}
              onClick={() => setInnerDate(undefined)}
            >
              Clear filter
            </MLButton>
            <MLButton variant={"contained"} onClick={() => handleClose()}>
              Apply
            </MLButton>
          </Stack>
        </Stack>
      </Popover>
    </>
  );
}
