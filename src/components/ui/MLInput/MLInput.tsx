import { Input, styled, useTheme } from "@mui/material";

const MLInput = styled(Input)(() => {
  const theme = useTheme();
  return {
    border: `1px solid`,
    borderRadius: `0.5rem`,
    paddingX: "0.2rem",
    "& .MuiInput-input": {
      borderColor: `${theme.palette.error.main}`,
    },
    "& .MuiInput-input:focus": {
      borderColor: `${theme.palette.primary.main} !important`,
    },
  };
}) as unknown as typeof Input;

export default MLInput;
