import MLButton from "../MLButton/MLButton";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  ButtonProps,
  ClickAwayListener,
  Grow,
  MenuItem,
  MenuList,
  Paper,
  Popper,
} from "@mui/material";
import { useRef, useState } from "react";

interface DropdownAction {
  title: string;
  disabled?: boolean;
  onClick: () => void;
}

export interface MLDropdownButtonProps extends ButtonProps {
  actions: (DropdownAction | null)[];
  size?: "small" | "medium" | "large";
}

export default function MLDropdownButton(
  props: MLDropdownButtonProps,
): JSX.Element {
  const { actions: oldActions, ...rest } = props;
  const actions: DropdownAction[] = oldActions.filter(
    (e) => e !== null,
  ) as DropdownAction[];
  const [open, setOpen] = useState(false);
  const anchorRef = useRef<HTMLDivElement>(null);

  const handleClose = (event: Event) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return;
    }

    setOpen(false);
  };
  return (
    <div ref={anchorRef}>
      <MLButton
        {...rest}
        aria-label="more-actions"
        onClick={() => setOpen(true)}
        size={props.size}
        sx={{
          minWidth: 0,
        }}
      >
        <MoreVertIcon fontSize={"small"} />
      </MLButton>
      <Popper
        sx={{
          zIndex: 1,
        }}
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === "bottom" ? "center top" : "center bottom",
            }}
          >
            <Paper>
              <ClickAwayListener onClickAway={handleClose}>
                <MenuList id="split-button-menu" autoFocusItem>
                  {actions.map((action, index) => (
                    <MenuItem
                      key={index}
                      onClick={action.onClick}
                      disabled={action.disabled}
                    >
                      {action.title}
                    </MenuItem>
                  ))}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </div>
  );
}
