import { theme } from "../../../theme";
import "./styles.css";
import { ToggleButton, styled } from "@mui/material";

const MLToggleButton = styled(ToggleButton)({
  borderColor: `${theme.palette.primary.main}`,
  textTransform: "none",
  color: "black",
  lineHeight: 1,
  "&.MuiToggleButton-root": {
    padding: "0.5rem 1.5rem 0.5rem 1.5rem",
  },
  "&.MuiToggleButton-root.Mui-selected:hover": {
    backgroundColor: "#543cb2",
  },
  "&.MuiToggleButton-root:last-child": {
    borderRadius: "0px 8px 8px 0px !important",
  },
  "&.MuiToggleButton-root:first-of-type": {
    borderRadius: "8px 0px 0px 8px !important",
  },
  // Add this rule to handle the case when there's only one button
  "&.MuiToggleButton-root:first-of-type:last-child": {
    borderRadius: "8px 8px 8px 8px !important",
  },
}) as typeof ToggleButton;

export default MLToggleButton;