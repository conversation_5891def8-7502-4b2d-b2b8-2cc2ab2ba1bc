import React from 'react';
import {
  TextField,
  InputLabel,
  styled,
  Autocomplete,
  CircularProgress
} from '@mui/material';
import Loading from '../../../pages/Loading/Loading';

const StyledTextField = styled(TextField)(({ theme, sx }) => ({
  '& .MuiOutlinedInput-root': {
    cursor: "pointer",
    '& fieldset': {
      borderColor: (sx as any)?.borderColor || '#e0e0e0',
      borderRadius: '8px',
      padding: '8px',
      minHeight: '19px',
    },
    '&:hover fieldset': {
      borderColor: '#b0b0b0',
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary.main,
    },
    // Add styling for disabled state
    '&.Mui-disabled .MuiInputBase-input': {
      color: '#6B7280', // Use the specific color for disabled text
      WebkitTextFillColor: '#6B7280', // Needed to override WebKit's autofill styling
    }
  },
  '& .MuiInputBase-input': {
    padding: "8px",
  },
}));

const StyledInputLabel = styled(InputLabel)(({ theme }) => ({
  position: 'static',
  transform: 'none',
  textAlign: 'left',
  color: theme.palette.text.primary,
  fontSize: '16px',
  fontStyle: 'normal',
  fontWeight: 500,
  lineHeight: '120%',
  marginBottom: '8px'
}));

// Define interface for employee data
interface EmployeeData {
  id: number;
  name: string;
  email: string;
}

interface MLInputBoxWithSuggetionDropdownProps {
  label: string;
  value: string;
  options: string[] | EmployeeData[]; // Can take either string arrays or employee objects
  onChange: (value: string) => void;
  onBlur?: () => void;
  onClick?: () => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  error?: boolean;
  required?: boolean;
  disabled?: boolean;
  id?: string;
  size?: 'small' | 'medium';
  type?: string;
  inputRef?: React.Ref<HTMLInputElement>;
  sx?: any;
  isLoading?: boolean;
  onOptionSelect?: (selectedOption: any) => void; // Changed to accept any type
  displayType?: 'name' | 'email'; // Which field to use as display value
  showBothFields?: boolean; // Whether to show both name and email in dropdown
  disabledTextColor?: string; // Add new prop for disabled text color
}

const MLInputBoxWithSuggetionDropdown: React.FC<MLInputBoxWithSuggetionDropdownProps> = ({
  label,
  value,
  options,
  onChange,
  onBlur,
  onKeyDown,
  onClick,
  error,
  required,
  disabled,
  id,
  size,
  type,
  inputRef,
  sx,
  isLoading,
  onOptionSelect,
  displayType = 'name',
  showBothFields = false,
  disabledTextColor = '#6B7280' // Default to specified color
}) => {
  // Check if the options are employee objects or simple strings
  const isEmployeeObject = options.length > 0 && typeof options[0] === 'object';
  
  // For display in the dropdown, create a map from display value to employee object
  // We need this to handle duplicate names
  const optionsMap = new Map();
  
  // This will store the actual values displayed in the dropdown
  let displayOptions: string[] = [];
  
  if (isEmployeeObject) {
    // For each employee object, create a unique display value
    (options as EmployeeData[]).forEach((emp, index) => {
      // Create a unique key that combines the name/email with ID 
      const displayValue = displayType === 'name' 
        ? `${emp.name}${emp.email ? ` (${emp.email})` : ''}` 
        : emp.email;
      
      // Store the mapping for later lookup
      optionsMap.set(displayValue, emp);
      displayOptions.push(displayValue);
    });
  } else {
    // For regular string arrays, just use them directly
    displayOptions = options as string[];
  }

  // Handle both onChange and potential onOptionSelect
  const handleChange = (_: any, newValue: string | null) => {
    if (!newValue) return;
    
    // Check if this is one of our employee objects in the map
    if (isEmployeeObject && optionsMap.has(newValue)) {
      const employee = optionsMap.get(newValue);
      
      // For the input field, just show the name or email
      const displayValue = displayType === 'name' ? employee.name : employee.email;
      onChange(displayValue);
      
      // But for the selection handler, pass the full employee object
      if (onOptionSelect) {
        onOptionSelect(employee);
      }
    } else {
      // Regular text input or simple string option
      onChange(newValue);
      
      if (onOptionSelect && (options as string[]).includes(newValue)) {
        onOptionSelect(newValue);
      }
    }
  };

  // Filter function to simplify the display in the input field
  const getOptionLabel = (option: string): string => {
    if (!option) return '';
    
    // For our employee display values, extract just the name/email part
    if (isEmployeeObject && option.includes('(') && option.includes(')')) {
      return displayType === 'name'
        ? option.substring(0, option.indexOf('(') - 1).trim()
        : option;
    }
    return option;
  };

  // Combine custom styling for disabled text with any existing sx props
  const combinedSx = {
    ...sx,
    '& .MuiOutlinedInput-root.Mui-disabled .MuiInputBase-input': {
      color: disabledTextColor,
      WebkitTextFillColor: disabledTextColor,
      opacity: 1,
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <StyledInputLabel>{label}</StyledInputLabel>
      <Autocomplete
        freeSolo
        disableClearable
        value={value}
        onChange={handleChange}
        options={displayOptions}
        getOptionLabel={getOptionLabel}
        disabled={disabled}
        sx={combinedSx} // Use combined sx props
        renderInput={(params) => (
          <StyledTextField
            {...params}
            inputRef={inputRef}
            fullWidth
            type={type || 'text'}
            required={required}
            error={error}
            size={size}
            id={id}
            onBlur={onBlur}
            onKeyDown={onKeyDown}
            onClick={onClick}
            onChange={(e) => onChange(e.target.value)}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <>
                  {isLoading && <CircularProgress size={20} />}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
        renderOption={(props, option, state) => {
          // Create a unique key for each option
          const key = `option-${state.index}`;
          
          // If we're using employee objects and option exists in our map
          if (isEmployeeObject && optionsMap.has(option)) {
            const employee = optionsMap.get(option);
            
            return (
              <li {...props} key={`emp-${employee.id}-${key}`} style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                padding: '8px 16px',
                alignItems: 'center' 
              }}>
                <div style={{ fontWeight: 500 }}>{employee.name}</div>
                <div style={{ 
                  fontSize: '0.875rem', 
                  color: '#666',
                  marginLeft: '12px' 
                }}>
                  {employee.email}
                </div>
              </li>
            );
          }
          
          // Default rendering for simple strings
          return (
            <li {...props} key={key}>
              {option}
            </li>
          );
        }}
      />
    </div>
  );
};

export default MLInputBoxWithSuggetionDropdown;