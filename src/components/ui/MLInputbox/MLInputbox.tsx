import React, { useState, forwardRef } from 'react';
import { TextField, InputLabel, styled, TextFieldProps, InputAdornment, IconButton, Box } from '@mui/material';
import VisibilityOnEyeIcon from '../../../../src/assets/icons/VisibilityOnEyeIcon';
import VisibilityOffEyeIcon from '../../../../src/assets/icons/VisibilityOffEyeIcon';
import RequiredStarIcon from '../../../assets/icons/RequiredStarIcon';

// Extend TextFieldProps to include the customFocusColor prop
interface CustomTextFieldProps extends Omit<TextFieldProps, 'variant'> {
    customfocuscolor?: string;
    disabledtextcolor?: string; // Add new prop for disabled text color
}

const StyledTextField = styled((props: CustomTextFieldProps) => (
    <TextField {...props} />
))(({ theme, customfocuscolor, disabledtextcolor, sx }) => ({
    '& .MuiOutlinedInput-root': {
        cursor: "pointer",
        '& fieldset': {
            borderColor: (sx as any)?.borderColor || '#e0e0e0',
            borderRadius: '8px',
            padding: '8px',
            minHeight: '19px',
        },
        '&:hover fieldset': {
            borderColor: '#b0b0b0',
        },
        '&.Mui-focused fieldset': {
            borderColor: customfocuscolor || theme.palette.primary.main, // Use custom focus color if provided
        },
        // Add styling for disabled state
        '&.Mui-disabled .MuiInputBase-input': {
            color: disabledtextcolor || '#6B7280', // Use custom disabled text color if provided
            WebkitTextFillColor: disabledtextcolor || '#6B7280', // Needed to override WebKit's autofill styling
            opacity: 1, // Keep text fully opaque when disabled
        }
    },
    '& .MuiInputBase-input': {
        padding: "8px",
        color: customfocuscolor, // Use custom focus color if provided
        '&.Mui-focused': {
            color: customfocuscolor || theme.palette.primary.main, // Use custom focus color if provided
        },
    },
    '& .MuiInputLabel-root': {
        color: customfocuscolor || theme.palette.primary.main, // Use custom focus color if provided
        '&.Mui-focused': {
            color: customfocuscolor || theme.palette.primary.main, // Use custom focus color if provided
        },
    },
}));

const StyledInputLabel = styled(InputLabel)(({ theme }) => ({
    position: 'static',
    transform: 'none',
    textAlign: 'left',
    color: theme.palette.text.primary,
}));

interface MLInputboxProps extends Omit<CustomTextFieldProps, 'variant'> {
    label: string;
    customFocusColor?: string;
    disabledTextColor?: string; // Add new prop for disabled text color
}

const MLInputbox = forwardRef<HTMLInputElement, MLInputboxProps>(({ 
    type, 
    label, 
    customFocusColor, 
    disabledTextColor,
    sx, 
    required, 
    ...props 
}, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const isPasswordField = type === 'password';
    const handleClickShowPassword = () => {
        setShowPassword((prev) => !prev);
    };

    return (
        <div style={{ display: 'flex', flexDirection: 'column', gap: label ? '8px' : "" }}>
            {label &&
                <StyledInputLabel
                    sx={{
                        fontSize: '16px',
                        fontStyle: 'normal',
                        fontWeight: 500,
                        lineHeight: '120%',
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "flex-start",
                    }}
                >
                    {label}
                    {required && <Box sx={{ ml: 0.5 }}><RequiredStarIcon /></Box>}
                </StyledInputLabel>
            }
            <StyledTextField
                inputRef={ref}
                fullWidth
                type={isPasswordField && !showPassword ? 'password' : type == 'password' ? "text" : type}
                InputLabelProps={{ shrink: true }}
                customfocuscolor={customFocusColor} // Pass customFocusColor
                disabledtextcolor={disabledTextColor} // Pass disabledTextColor
                InputProps={{
                    endAdornment: isPasswordField ? (
                        <InputAdornment position="end">
                            <IconButton
                                aria-label="toggle password visibility"
                                onClick={handleClickShowPassword}
                                edge="end"
                            >
                                {showPassword ? <VisibilityOffEyeIcon /> : <VisibilityOnEyeIcon />}
                            </IconButton>
                        </InputAdornment>
                    ) : undefined,
                }}
                sx={{ ...sx }}
                {...props} // This will include required, onChange, and other props
            />
        </div>
    );
});

export default MLInputbox;