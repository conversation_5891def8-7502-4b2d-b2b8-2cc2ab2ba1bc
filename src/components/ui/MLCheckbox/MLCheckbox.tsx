import CheckedIcon from "../../../../src/assets/icons/CheckedIcon";
import UncheckedIcon from "../../../../src/assets/icons/UncheckedIcon";
import DisableCheckedIcon from "../../../assets/icons/DisableCheckedIcon";
import { Checkbox } from "@mui/material";
import React from "react";

interface MLCheckboxProps {
  name: string;
  checked?: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
}

const MLCheckbox: React.FC<MLCheckboxProps> = ({
  name,
  checked,
  onChange,
  disabled = false,
}) => {
  return (
    <Checkbox
      name={name}
      checked={checked}
      onChange={onChange}
      icon={<UncheckedIcon color={disabled ? '#9c9c9c' : '#7856FF'} />}
      checkedIcon={disabled ? <DisableCheckedIcon /> : <CheckedIcon />}
      disabled={disabled}
      sx={{
        "&:hover": { bgcolor: "transparent" },
        "&.Mui-checked": {
          "&:hover": { bgcolor: "transparent" },
        },
      }}
    />
  );
};

export default MLCheckbox;
