import MLTypography from "../MLTypography/MLTypography";
import { Tab, TabProps, styled, useTheme } from "@mui/material";

interface MLTabProps extends TabProps {
  mlSize?: "small" | "large";
  mlcolor?: string;
  // Add new font-related props
  fontSize?: {
    xs?: string;
    sm?: string;
    md?: string;
  };
}

export default function MLTab({ 
  mlSize, 
  mlcolor, 
  fontSize,
  ...restProps 
}: MLTabProps) {
  const theme = useTheme();
  const size = mlSize ?? "small";
  const customColor = mlcolor ?? theme.palette.primary.main;
  const typography = size === "large" ? theme.typography.h2 : theme.typography.h3;

  // Default font sizes if not provided
  const defaultFontSize = {
    xs: '18px',
    sm: '24px',
    md: '32px'
  };

  return (
    <Tab
      disableRipple
      {...restProps}
      sx={{
        ...typography,
        textTransform: "capitalize",
        "&.MuiTab-textColorPrimary": {
          color: customColor,
        },
        "&.Mui-selected": {
          color: customColor,
        },
        minWidth: 0,
        paddingX: 0,
        ...restProps.sx,
        fontSize: fontSize || defaultFontSize,
        fontWeight:  600,
      }}
    />
  );
}