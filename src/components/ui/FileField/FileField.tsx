import MLButton from "../MLButton/MLButton";
import "./FileField.css";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import React, { RefObject, forwardRef } from "react";

type InputPropType = React.DetailedHTMLProps<
  React.InputHTMLAttributes<HTMLInputElement>,
  HTMLInputElement
>;

const FileField = forwardRef<HTMLInputElement, InputPropType>(
  function FileField(props, ref) {
    const { accept, onChange } = props;
    const aRef = ref as RefObject<HTMLInputElement>;
    return (
      <>
        <input
          type="file"
          ref={ref}
          accept={accept}
          onChange={onChange}
        ></input>

        <MLButton
          startIcon={<FileUploadIcon />}
          variant={"text"}
          onClick={() =>
            aRef && aRef.current
              ? aRef.current.click()
              : console.error("Forwarded Ref Error in FileField")
          }
        >
          Upload media
        </MLButton>
      </>
    );
  },
);

export default FileField;
