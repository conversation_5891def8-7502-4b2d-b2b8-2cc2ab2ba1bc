import MLTypography from "./MLTypography";

export default {
  component: MLTypography,
};

/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */
export const Variants = {
  render: () => (
    <div>
      <MLTypography variant="h1">Heading 1</MLTypography>
      <MLTypography variant="h2">Heading 2</MLTypography>
      <MLTypography variant="h3">Heading 3</MLTypography>
      <MLTypography variant="h4">Heading 4</MLTypography>
      <MLTypography variant="h5">Heading 5</MLTypography>
      <MLTypography variant="h6">Heading 6</MLTypography>
      <MLTypography variant="subtitle1">Subtitle 1</MLTypography>
      <MLTypography variant="subtitle2">Subtitle 2</MLTypography>
      <MLTypography variant="body1">Body Text 1</MLTypography>
      <MLTypography variant="body2">Body Text 2</MLTypography>
    </div>
  ),
};
