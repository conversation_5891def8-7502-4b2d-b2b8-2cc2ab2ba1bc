import React from 'react';
import { SxProps, Theme, Stack, } from '@mui/material';
import MLTypography from '../MLTypography/MLTypography';
import CustomLeftArrow from '../../../assets/icons/CustomLeftArrow';
import CustomRightArrow from '../../../assets/icons/CustomRightArrow';

interface NavigationButtonProps {
    label: string;
    onClick: () => void;
    sx?: SxProps<Theme>;
}

const PreviousButton: React.FC<NavigationButtonProps> = ({ label, onClick, sx }) => (
    <Stack
        onClick={onClick}
        flexDirection="row"
        gap="12px"
        sx={{
            cursor: "pointer",
            padding: '8px',
            borderRadius: '8px',
            backgroundColor: 'transparent',
            transition: 'background-color 0.3s',
            '&:hover': {
                backgroundColor: 'rgba(120, 86, 255, 0.04)', // Light purple background on hover
            },
            '&:active': {
                backgroundColor: 'rgba(120, 86, 255, 0.1)', // Slightly darker purple on click
            },
            ...sx // Allow additional styles to be passed
        }}
    >
        <CustomLeftArrow sx={{ fontSize: "30px", marginTop: "5px" }} />
        <Stack alignItems="start">
            <MLTypography
                color="#7856FF"
                sx={{
                    fontSize: "24px",
                    fontWeight: 700
                }}
            >
                Previous
            </MLTypography>
            <MLTypography
                sx={{
                    fontSize: "16px",
                    fontWeight: 600
                }}
            >
                {label}
            </MLTypography>
        </Stack>
    </Stack>
);

const NextButton: React.FC<NavigationButtonProps> = ({ label, onClick, sx }) => (
    <Stack
        onClick={onClick}
        flexDirection="row"
        gap="12px"
        sx={{
            cursor: "pointer",
            padding: '8px 12px',
            borderRadius: '8px',
            backgroundColor: 'transparent',
            transition: 'background-color 0.3s',
            '&:hover': {
                backgroundColor: 'rgba(120, 86, 255, 0.04)', // Light purple background on hover
            },
            '&:active': {
                backgroundColor: 'rgba(120, 86, 255, 0.1)', // Slightly darker purple on click
            },
            ...sx
        }}
    >
        <Stack alignItems="end">
            <MLTypography
                color="#7856FF"
                sx={{
                    fontSize: "24px",
                    fontWeight: 700
                }}
            >
                Next
            </MLTypography>
            <MLTypography
                sx={{
                    fontSize: "16px",
                    fontWeight: 600
                }}
            >
                {label}
            </MLTypography>
        </Stack>
        <CustomRightArrow sx={{ fontSize: "30px", marginTop: "5px" }} />
    </Stack>
);

export { PreviousButton, NextButton };
export type { NavigationButtonProps };
