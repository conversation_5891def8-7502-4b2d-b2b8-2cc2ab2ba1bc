import React, { ReactNode } from 'react';
import { Box, IconButton, Stack, SxProps } from '@mui/material';
import learningClipPath from '../../../assets/Images/learningClipPath.svg';
import resourceClipPath from '../../../assets/Images/resourceClipPath.svg';
import selfAssessmentClipPath from '../../../assets/Images/selfassessmentclipPath.svg';
import bannerClipPath from '../../../assets/Images/BannerClipPath.svg';
import MLTypography from '../MLTypography/MLTypography';
import { useBack } from '@refinedev/core';
import { ErChevronleft } from '@mindlens/ergo-icons';
import MLContainer from '../MLMaxWidthContainer/MLMaxWidthContainer';
import { desktop, tablet } from '../../../responsiveStyles';

interface BannerProps {
    title?: string;
    subtitle?: string | ReactNode;
    isBackButton?: boolean;
    backgroundColor?: string;
    sx?: SxProps;
}


const getClipPathStyles = (title?: string) => {
    switch (title) {
        case 'Learning':
            return {
                path: learningClipPath,
                styles: {
                    backgroundSize: "contain",
                    backgroundPosition: 'left bottom',
                    backgroundRepeat: 'no-repeat'
                },
            }
        case 'Resources':
            return {
                path: resourceClipPath,
                styles: {
                    backgroundSize: "contain",
                    backgroundPosition: '650px bottom',
                    backgroundRepeat: 'no-repeat',
                    backgroundAttachment: 'inherit'
                }
            };
        case 'Self Assessment':
            return {
                path: selfAssessmentClipPath,
                styles: {
                    backgroundSize: "contain",
                    backgroundPosition: '140px',
                    backgroundRepeat: 'no-repeat',
                    backgroundAttachment: 'inherit'
                }
            };
        default:
            return {
                path: bannerClipPath,
                styles: {
                    backgroundSize: "cover",
                    backgroundPosition: '450px',
                    backgroundRepeat: 'no-repeat',
                    backgroundAttachment: 'inherit'
                }
            };
    }
};

const MLBanner: React.FC<BannerProps> = ({
    title,
    isBackButton,
    subtitle,
    backgroundColor = '#DFFF32', // Keeping original default color
    sx,
}) => {
    const back = useBack();
    const clipPathConfig = getClipPathStyles(title);

    return (
        <Box
            sx={{
                width: '100%',
                backgroundImage: {
                    xs: 'none', // Hide background image on extra small screens
                    sm: `url(${clipPathConfig.path})` // Show background image from small screens and up
                },
                ...clipPathConfig.styles,
                backgroundColor: backgroundColor, // Using the passed background color
                overflow: 'hidden',
                height: { xs: 'auto', sm: '100px' }, // Height 100px only for non-mobile screens
                display: 'flex',
                alignItems: 'center',
                mb: { xs: '10px', md: '25px' },
                paddingX: {
                    // lg: desktop.contentContainer.paddingX,
                    // md: tablet.contentContainer.paddingX,
                    // xs: tablet.contentContainer.paddingX,
                },
                 paddingY: {
                    xs: "12px", // PaddingY 12px only for mobile
                    sm: 0 // No paddingY for non-mobile
                },
                ...sx,
            }}
        >
            <MLContainer>
                <Stack
                    direction="row"
                    alignItems="center"
                    gap={{ md: 1 }}
                >
                    {isBackButton &&
                        <IconButton sx={{ padding: { xs: '0px' } }} disableRipple size="large" onClick={() => back()}>
                            <ErChevronleft />
                        </IconButton>
                    }

                    <MLTypography
                        sx={{
                            overflow: "hidden",
                            fontSize: { md: '46px', sm: '38px', xs: '22px' },
                            textOverflow: "ellipsis",
                            // whiteSpace: "pre",
                        }}
                        variant="h1"
                        fontWeight={{ xs: 600, md: 600 }}
                    >
                        {title}
                    </MLTypography>
                </Stack>
                {/* <MLTypography
                    marginBottom={{ xs: "20px", md: "0px" }}
                    variant="body1"
                    sx={{
                        color: '#000000',
                        opacity: 0.8,
                        fontSize: { xs: '14px', sm: '16px' },
                        maxWidth: '850px',
                    }}
                >
                    {subtitle}
                </MLTypography> */}
            </MLContainer>
        </Box>
    );
};

export default MLBanner;
