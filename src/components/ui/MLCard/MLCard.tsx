import "./styles.css";
import { Card, styled } from "@mui/material";
import { Theme } from "@mui/material/styles";

const MLCard = styled(Card)(({ theme }) => ({
  boxShadow: "none",
  border: "0.5px #9C9C9C solid",
  borderRadius: "0.625rem",
  // "&.MuiCard-root": {
  backgroundColor: "rgba(255,255,255,.77)",
  // },
  padding: "20px", // default padding
  [theme.breakpoints.up("sm")]: {
    padding: "24px",
  },
})) as typeof Card;

export default MLCard;
