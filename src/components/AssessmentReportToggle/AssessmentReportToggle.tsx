import MLButton from "../ui/MLButton/MLButton";
import { ArrowDropDown, ExpandMore, MoreVert } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Grow,
  Paper,
  ClickAwayListener,
  MenuList,
  MenuItem,
  Stack,
} from "@mui/material";
import React from "react";
import { useState } from "react";

interface ItemLink {
  key: string;
  handler: () => void;
  disabled?: boolean;
}

export default function AssessmentReportToggle({
  current,
  items,
}: {
  current: string;
  items: (ItemLink | undefined)[];
}) {
  const [currentState, setCurrentState] = useState(current);
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef<HTMLButtonElement>(null);
  const handleClose = (event: Event) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return;
    }

    setOpen(false);
  };
  const filteredItems: ItemLink[] = items.filter(
    (e) => e !== undefined,
  ) as ItemLink[];

  return (
    <>
      <MLButton
        onClick={() => setOpen(true)}
        ref={anchorRef}
        variant="contained"
        color="secondary"
        sx={{
          minWidth: 0,
        }}
      >
        <Stack
          display={{ xs: "none", sm: "flex" }}
          direction={"row"}
          sx={{
            alignItems: "center",
          }}
        >
          {currentState} <ExpandMore />
        </Stack>
        <Stack
          display={{ xs: "flex", sm: "none" }}
          sx={{
            alignItems: "center",
          }}
        >
          <MoreVert />
        </Stack>
      </MLButton>
      <Popper
        sx={{
          zIndex: 1,
        }}
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === "bottom" ? "center top" : "center bottom",
            }}
          >
            <Paper>
              <ClickAwayListener onClickAway={handleClose}>
                <MenuList id="split-button-menu" autoFocusItem>
                  {filteredItems.map((option, index) => (
                    <MenuItem
                      key={option.key}
                      onClick={() => option.handler()}
                      disabled={option.disabled}
                    >
                      {option.key}
                    </MenuItem>
                  ))}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </>
  );
}
