import Box from "@mui/material/Box";
import {
  RefineThemedLayoutV2Props,
  ThemedHeaderV2 as De<PERSON>ult<PERSON>eader,
  ThemedLayoutContextProvider,
  ThemedSiderV2 as DefaultSider,
} from "@refinedev/mui";
import React from "react";
import { useLocation } from "react-router-dom";

export const CustomLayoutV2: React.FC<RefineThemedLayoutV2Props> = ({
  Sider,
  Header,
  Title,
  Footer,
  OffLayoutArea,
  children,
  initialSiderCollapsed,
}) => {
  const SiderToRender = Sider ?? DefaultSider;
  const HeaderToRender = Header ?? DefaultHeader;

  return (
    <ThemedLayoutContextProvider initialSiderCollapsed={initialSiderCollapsed}>
      <Box display="flex" flexDirection="row">
        <SiderToRender
          Title={Title}
        />
        <Box
          sx={[
            {
              display: "flex",
              flexDirection: "column",
              flex: 1,
              minHeight: "100vh",
            },
            { overflow: "auto" },
            { overflow: "clip" },
          ]}
        >
          <HeaderToRender />
          <Box
            component="main"
            sx={{
              //   p: { xs: 1, md: 2, lg: 3 },
              flexGrow: 1,
            }}
          >
            {children}
          </Box>
          {Footer && <Footer />}
        </Box>
        {OffLayoutArea && <OffLayoutArea />}
      </Box>
    </ThemedLayoutContextProvider>
  );
};
