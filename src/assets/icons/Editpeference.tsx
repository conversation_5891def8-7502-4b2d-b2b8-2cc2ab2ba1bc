const Editpeference = () => {
    return (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="upload" clipPath="url(#clip0_17646_320)">
                <g id="Interface-Essential / Edit / pencil-write-2">
                    <g id="Group 309">
                        <g id="pencil-write-2">
                            <path id="Shape 1445" d="M12.55 14.39L8.83997 14.92L9.36997 11.21L18.91 1.66C19.79 0.78 21.21 0.78 22.09 1.66C22.97 2.54 22.97 3.96 22.09 4.84L12.54 14.39H12.55Z" fill="#FF6E6E" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            <path id="Shape 1446" d="M19 14.5V22C19 22.83 18.33 23.5 17.5 23.5H2.5C1.67 23.5 1 22.83 1 22V7C1 6.17 1.67 5.5 2.5 5.5H10" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        </g>
                    </g>
                </g>
            </g>
            <defs>
                <clipPath id="clip0_17646_320">
                    <rect width="24" height="24" fill="white" />
                </clipPath>
            </defs>
        </svg>

    )
}

export default Editpeference