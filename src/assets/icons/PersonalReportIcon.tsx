import * as React from "react"
import { SVGProps } from "react"
const PersonalReportIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 45 50"
    preserveAspectRatio="xMidYMid meet"
    fill="none"
    style={{
      width: '100%',
      height: 'auto',
      ...props.style
    }}
    {...props}
  >
    <path
      stroke="#333"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M31.727 39.4c0 1.439-.94 2.6-2.104 2.6H4.376c-1.164 0-2.104-1.161-2.104-2.6V5.6c0-1.439.94-2.6 2.104-2.6h21.11c.547 0 1.08.26 1.472.745l4.138 4.992c.407.486.631 1.162.631 1.855V39.4Z"
    />
    <path
      stroke="#FF6E6E"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M8.655 16.008h16.831M8.655 23.797h16.831M8.655 31.598h8.415"
    />
  </svg>
)
export default PersonalReportIcon
