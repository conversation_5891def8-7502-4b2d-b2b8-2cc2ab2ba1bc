const ErgoQueriesIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 31 31" fill="none">
            <g clipPath="url(#clip0_13790_39043)">
                <path d="M22.2812 30.0312C26.5567 30.0312 30.0312 26.5567 30.0312 22.2812C30.0312 18.0058 26.5567 14.5312 22.2812 14.5312C18.0058 14.5312 14.5312 18.0058 14.5312 22.2812C14.5312 26.5567 18.0058 30.0312 22.2812 30.0312Z" stroke="#333333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M19.8658 20.8229C19.8658 19.4795 20.9508 18.3945 22.2942 18.3945C23.6375 18.3945 24.7225 19.4795 24.7225 20.8229C24.7225 22.1662 23.6375 23.2512 22.2942 23.2512" stroke="#FF6E6E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M22.2812 26.1562C22.5525 26.1562 22.7721 26.3758 22.7721 26.6471C22.7721 26.9183 22.5525 27.1379 22.2812 27.1379C22.01 27.1379 21.7904 26.9183 21.7904 26.6471C21.7904 26.3758 22.01 26.1562 22.2812 26.1562Z" fill="#FF6E6E" stroke="#FF6E6E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M4.84375 8.71875H18.4062" stroke="#FF6E6E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M4.84375 14.5312H12.5937" stroke="#FF6E6E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M4.84375 20.3438H9.6875" stroke="#FF6E6E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M9.6875 26.1562H2.90625C1.83417 26.1562 0.96875 25.2908 0.96875 24.2187V2.90625C0.96875 1.83417 1.83417 0.96875 2.90625 0.96875H16.6367C17.1533 0.96875 17.6442 1.17542 18.0058 1.53708L21.7129 5.24417C22.0746 5.60583 22.2812 6.09667 22.2812 6.61333V9.6875" stroke="#333333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </g>
            <defs>
                <clipPath id="clip0_13790_39043">
                    <rect width="31" height="31" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default ErgoQueriesIcon
