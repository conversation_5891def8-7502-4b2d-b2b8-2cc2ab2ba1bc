import * as React from "react"
import { SVGProps } from "react"
const IdentifyIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 45 45"
    preserveAspectRatio="xMidYMid meet"
    fill="none"
    style={{
      width: '100%',
      height: 'auto',
      ...props.style
    }}
    {...props}
  >
    <path
      stroke="#000"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M1 31.939a12.68 12.68 0 0 1 5.063-10.125A12.72 12.72 0 0 1 17.2 19.77"
    />
    <path
      fill="#FF6E6E"
      stroke="#000"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M13.656 16.469a7.726 7.726 0 0 0 7.725-7.725c0-4.256-3.45-7.744-7.725-7.744-4.275 0-7.725 3.469-7.725 7.744a7.726 7.726 0 0 0 7.725 7.725Z"
    />
    <path
      stroke="#000"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M29.125 38.969a9.843 9.843 0 0 0 9.844-9.844 9.843 9.843 0 0 0-9.844-9.844 9.843 9.843 0 0 0-9.844 9.844 9.843 9.843 0 0 0 9.844 9.844ZM43.187 43.188l-7.106-7.106"
    />
  </svg>
)
export default IdentifyIcon
