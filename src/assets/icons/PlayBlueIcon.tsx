

const PlayBlueIcon = () => {
    return (
        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g filter="url(#filter0_d_17819_31056)">
                <circle cx="30" cy="30" r="28" fill="white" />
                <path d="M42.7949 27.9089C44.1283 28.6787 44.1283 30.6032 42.7949 31.373L24.5642 41.8985C23.2308 42.6683 21.5642 41.706 21.5642 40.1664L21.5642 19.1154C21.5642 17.5758 23.2308 16.6135 24.5642 17.3833L42.7949 27.9089Z" fill="#7856FF" />
            </g>
            <defs>
                <filter id="filter0_d_17819_31056" x="0" y="0" width="64" height="64" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset dx="2" dy="2" />
                    <feGaussianBlur stdDeviation="2" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_17819_31056" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_17819_31056" result="shape" />
                </filter>
            </defs>
        </svg>

    )
}

export default PlayBlueIcon