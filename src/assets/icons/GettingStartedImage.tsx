import * as React from "react"
import { SVGProps } from "react"
const GettingStartedImage = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 441 409"
    preserveAspectRatio="xMidYMid meet"
    fill="none"
    style={{
      width: '100%',
      height: 'auto',
      ...props.style
    }}
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#DFFF32"
        d="M25.718 174.898s-6.665 72.37 47.485 130.383c54.159 58.013 137.074 75.368 203.972 91.19 66.907 15.823 123.494-13.682 138.513-70.062 15.019-56.371-34.423-81.918-54.196-152.239-19.773-70.331-14.585-91.495-68.735-141.759-54.141-50.263-149.831-40.926-207.018 20.15-57.178 61.077-60.02 122.337-60.02 122.337Z"
      />
      <path
        fill="#fff"
        d="M25.718 174.898s-6.665 72.37 47.485 130.383c54.159 58.013 137.074 75.368 203.972 91.19 66.907 15.823 123.494-13.682 138.513-70.062 15.019-56.371-34.423-81.918-54.196-152.239-19.773-70.331-14.585-91.495-68.735-141.759-54.141-50.263-149.831-40.926-207.018 20.15-57.178 61.077-60.02 122.337-60.02 122.337Z"
        opacity={0.7}
      />
      <path
        fill="#263238"
        d="M8.124 326.29c2.741-.203 8.141-3.192 8.141-3.201.056-.028-3.055-2.953-3.277-3.119-2.52-1.873-8.483-4.013-11.345-1.974-2.058 1.467-2.123 4.336-.526 6.163 1.634 1.864 4.662 2.297 6.997 2.131h.01Z"
      />
      <path
        fill="#263238"
        d="M18.333 341.096c2.493-.885 6.62-4.862 6.952-5.175 2.206 2.325 4.652 4.52 7.264 6.615a126.27 126.27 0 0 0 4.58 3.506c-.786-.416-1.496-.785-1.598-.822-2.926-1.144-9.24-1.633-11.465 1.089-1.597 1.965-.905 4.742 1.126 6.089 2.068 1.366 5.105.987 7.311.203 2.243-.793 5.834-4.115 6.767-5l1.8 1.301c2.89 2.066 5.788 4.114 8.557 6.209.978.738 1.91 1.504 2.852 2.26a41.146 41.146 0 0 0-1.384-.71c-2.926-1.144-9.24-1.633-11.465 1.088-1.597 1.966-.905 4.752 1.126 6.09 2.068 1.365 5.105.987 7.311.203 2.142-.757 5.483-3.792 6.6-4.844.942.803 1.883 1.614 2.751 2.436.582.572 1.182 1.088 1.736 1.706.563.591 1.117 1.181 1.67 1.763 1.016 1.162 2.077 2.352 3.047 3.505.674.803 1.31 1.578 1.947 2.353-.969-.747-2.889-2.168-3.064-2.27-2.714-1.568-8.89-3.016-11.502-.664-1.883 1.698-1.616 4.558.184 6.182 1.837 1.66 4.893 1.743 7.2 1.31 2.54-.48 7.053-3.635 7.616-4.032a185.055 185.055 0 0 1 2.991 3.764 209.73 209.73 0 0 1 6.231 8.47l2.068-1.089a218.887 218.887 0 0 0-6.674-8.672 198.263 198.263 0 0 0-3.425-4.115c.286-.332 1.911-2.251 3.203-4.235 2.502.886 7.644.757 8.308.729.167 1.661.305 3.266.416 4.797.295 3.977.443 7.464.517 10.26l2.252-1.181c-.138-2.62-.35-5.739-.692-9.254-.157-1.679-.36-3.478-.591-5.323.646-.185 5.945-1.735 7.911-3.414 1.782-1.522 3.628-3.958 3.48-6.431-.147-2.426-2.206-4.419-4.708-4.013-3.47.563-6.203 6.274-6.674 9.383-.027.185-.11 2.205-.138 3.478-.093-.756-.185-1.485-.286-2.251a154.626 154.626 0 0 0-.72-4.687c-.157-.784-.314-1.577-.471-2.38-.139-.794-.388-1.624-.582-2.445a88.461 88.461 0 0 0-1.698-5.683c1.338-.185 6.083-.904 8.086-2.141 1.994-1.236 4.191-3.358 4.413-5.831.221-2.417-1.514-4.705-4.043-4.686-3.518.027-7.09 5.268-8.022 8.257-.056.184-.49 2.408-.702 3.635-.388-1.135-.766-2.27-1.2-3.414-1.237-3.284-2.603-6.569-3.942-9.853a205.08 205.08 0 0 1-1.209-3.017c.48-.055 6.139-.793 8.382-2.177 1.994-1.237 4.19-3.359 4.422-5.831.221-2.417-1.514-4.705-4.044-4.678-3.517.028-7.09 5.268-8.022 8.258-.073.221-.683 3.422-.803 4.244-.886-2.215-1.744-4.438-2.501-6.652-1.053-3.064-1.92-6.127-2.53-9.153.222-.027 6.14-.765 8.428-2.186 1.994-1.237 4.191-3.359 4.413-5.831.221-2.417-1.514-4.705-4.043-4.678-3.517.028-7.09 5.268-8.022 8.258-.074.23-.73 3.681-.813 4.308-.037-.184-.092-.36-.129-.544-.618-3.239-.812-6.458-.84-9.485a64.291 64.291 0 0 1 .166-5.065c1.117-.027 6.213-.193 8.42-1.245 2.113-1.006 4.532-2.879 5.03-5.314.49-2.381-.988-4.844-3.508-5.102-3.498-.36-7.625 4.456-8.88 7.325-.065.157-.59 1.818-.95 3.045.064-.738.11-1.495.184-2.196.267-2.639.683-5.047 1.061-7.169.222-1.208.453-2.297.674-3.312 1.08-.59 5.41-3.045 6.859-4.964 1.412-1.872 2.695-4.64 2.012-7.03-.664-2.334-3.11-3.838-5.464-2.906-3.268 1.292-4.708 7.464-4.505 10.592.018.267.784 4.465.84 4.437 0 0 .055-.027.064-.037-.24.969-.48 2.021-.72 3.165-.424 2.122-.886 4.521-1.218 7.169-.332 2.647-.628 5.526-.656 8.589-.037 3.072.084 6.301.647 9.623 0 .037.018.074.027.11-.461-.922-1.846-3.441-1.975-3.625-1.81-2.565-6.813-6.459-10.164-5.416-2.418.756-3.378 3.46-2.418 5.692.969 2.279 3.71 3.635 5.99 4.198 2.567.637 8.207-.406 8.65-.489.554 3.156 1.385 6.339 2.4 9.522a135.73 135.73 0 0 0 1.884 5.443 42.13 42.13 0 0 0-.877-1.569c-1.81-2.564-6.813-6.458-10.164-5.415-2.418.756-3.379 3.46-2.418 5.692.969 2.279 3.71 3.635 5.99 4.198 2.308.572 7.136-.212 8.41-.443.259.692.517 1.384.785 2.076 1.274 3.312 2.557 6.615 3.71 9.9.407 1.153.767 2.306 1.136 3.459-.378-.692-.701-1.273-.766-1.356-1.81-2.565-6.813-6.458-10.163-5.415-2.42.756-3.38 3.459-2.42 5.692.97 2.279 3.712 3.635 5.992 4.198 2.206.553 6.665-.139 8.179-.406.341 1.19.674 2.38.95 3.552.167.803.388 1.559.508 2.38.139.803.277 1.606.416 2.39.212 1.531.434 3.109.6 4.604.12 1.042.221 2.029.323 3.035-.397-1.153-1.21-3.395-1.302-3.58-.932-1.863-3.166-4.521-5.575-5.941a4.824 4.824 0 0 0-.48-.785c-1.459-1.937-4.284-2.472-6.148-.747-2.585 2.38-1.708 8.654-.397 11.505.073.166 1.126 1.901 1.8 2.98-.499-.572-.97-1.135-1.486-1.725a163.503 163.503 0 0 0-3.185-3.515c-.563-.572-1.136-1.144-1.708-1.726-.554-.59-1.218-1.144-1.837-1.716a80.19 80.19 0 0 0-4.56-3.801c1.015-.895 4.578-4.105 5.566-6.246.979-2.131 1.634-5.111.471-7.298-1.145-2.14-3.859-3.09-5.963-1.67-2.917 1.966-3 8.295-2.133 11.312.056.184.923 2.278 1.422 3.422-.95-.738-1.902-1.476-2.88-2.186-2.843-2.058-5.797-4.041-8.724-6.043-.895-.609-1.781-1.236-2.677-1.855.37-.313 4.69-4.041 5.788-6.439.979-2.132 1.634-5.112.471-7.298-1.145-2.141-3.859-3.091-5.963-1.67-2.917 1.965-3 8.303-2.133 11.311.065.231 1.32 3.238 1.68 3.986-1.957-1.366-3.904-2.74-5.76-4.171-2.566-1.974-4.976-4.05-7.163-6.236.166-.148 4.698-4.023 5.825-6.468.978-2.131 1.633-5.111.461-7.298-1.144-2.14-3.858-3.09-5.963-1.669-2.917 1.965-3 8.303-2.133 11.311.065.24 1.422 3.469 1.708 4.041-.129-.13-.277-.249-.406-.379-2.308-2.361-4.237-4.945-5.936-7.445-.987-1.449-1.846-2.897-2.658-4.318.914-.636 5.077-3.58 6.332-5.683 1.21-2.011 2.188-4.899 1.265-7.206-.905-2.251-3.498-3.496-5.742-2.315-3.11 1.633-3.895 7.916-3.36 11.006.028.166.517 1.846.886 3.063-.35-.645-.729-1.31-1.061-1.937-1.237-2.353-2.216-4.585-3.074-6.569-.48-1.135-.886-2.168-1.265-3.137.572-1.089 2.834-5.526 2.982-7.916.148-2.334-.314-5.36-2.197-6.975-1.846-1.587-4.717-1.485-6.167.591-2.012 2.878.194 8.82 2.096 11.32.166.212 3.12 3.294 3.148 3.238 0 0 .027-.055.037-.064.341.941.72 1.947 1.153 3.035.813 2.002 1.754 4.263 2.945 6.652 1.182 2.39 2.53 4.955 4.2 7.529 1.671 2.583 3.554 5.212 5.844 7.666l.083.084c-.896-.508-3.443-1.855-3.646-1.938-2.927-1.144-9.24-1.633-11.466 1.089-1.597 1.965-.904 4.751 1.127 6.089 2.067 1.365 5.104.987 7.31.203l-.009.018Z"
      />
      <path
        fill="#263238"
        d="M63.972 298.427c.065 0-.914-4.152-1.006-4.401-1.062-2.952-4.865-8.027-8.373-7.907-2.53.083-4.172 2.445-3.84 4.853.332 2.454 2.622 4.484 4.662 5.637 2.4 1.347 8.557 1.818 8.557 1.818ZM432.886 326.29c-2.741-.203-8.142-3.192-8.142-3.201-.055-.028 3.056-2.953 3.277-3.119 2.521-1.873 8.484-4.013 11.346-1.974 2.058 1.467 2.123 4.336.526 6.163-1.634 1.864-4.662 2.297-6.997 2.131h-.01Z"
      />
      <path
        fill="#263238"
        d="M422.667 341.096c-2.492-.885-6.618-4.862-6.951-5.175-2.206 2.325-4.652 4.52-7.265 6.615a126.156 126.156 0 0 1-4.578 3.506c.784-.416 1.495-.785 1.597-.822 2.926-1.144 9.24-1.633 11.465 1.089 1.597 1.965.904 4.742-1.126 6.089-2.068 1.366-5.105.987-7.312.203-2.243-.793-5.834-4.115-6.766-5l-1.8 1.301c-2.889 2.066-5.788 4.114-8.557 6.209-.979.738-1.911 1.504-2.853 2.26a41.771 41.771 0 0 1 1.385-.71c2.926-1.144 9.24-1.633 11.465 1.088 1.597 1.966.905 4.752-1.126 6.09-2.068 1.365-5.105.987-7.311.203-2.142-.757-5.484-3.792-6.601-4.844-.941.803-1.883 1.614-2.75 2.436-.582.572-1.182 1.088-1.736 1.706-.563.591-1.117 1.181-1.671 1.763-1.015 1.162-2.077 2.352-3.046 3.505-.674.803-1.311 1.578-1.948 2.353.97-.747 2.89-2.168 3.065-2.27 2.714-1.568 8.89-3.016 11.502-.664 1.883 1.698 1.615 4.558-.185 6.182-1.837 1.66-4.892 1.743-7.2 1.31-2.538-.48-7.053-3.635-7.616-4.032a188.258 188.258 0 0 0-2.991 3.764 210.118 210.118 0 0 0-6.231 8.47l-2.067-1.089a218.57 218.57 0 0 1 6.674-8.672 196.424 196.424 0 0 1 3.425-4.115c-.287-.332-1.911-2.251-3.204-4.235-2.501.886-7.643.757-8.308.729a177.212 177.212 0 0 0-.415 4.797 216.495 216.495 0 0 0-.517 10.26l-2.252-1.181c.138-2.62.35-5.739.692-9.254.157-1.679.36-3.478.591-5.323-.646-.185-5.945-1.735-7.911-3.414-1.782-1.522-3.628-3.958-3.481-6.431.148-2.426 2.207-4.419 4.708-4.013 3.471.563 6.204 6.274 6.674 9.383.028.185.111 2.205.139 3.478.092-.756.185-1.485.286-2.251.203-1.531.452-3.054.72-4.687.157-.784.314-1.577.471-2.38.138-.794.388-1.624.581-2.445a88.722 88.722 0 0 1 1.699-5.683c-1.339-.185-6.083-.904-8.086-2.141-1.994-1.236-4.191-3.358-4.413-5.831-.222-2.417 1.514-4.705 4.043-4.686 3.517.027 7.09 5.268 8.022 8.257.056.184.489 2.408.702 3.635.387-1.135.766-2.27 1.2-3.414 1.237-3.284 2.603-6.569 3.941-9.853.416-1.006.813-2.011 1.21-3.017-.48-.055-6.139-.793-8.382-2.177-1.994-1.237-4.191-3.359-4.422-5.831-.221-2.417 1.514-4.705 4.043-4.678 3.517.028 7.09 5.268 8.022 8.258.074.221.683 3.422.803 4.244.887-2.215 1.745-4.438 2.502-6.652 1.052-3.064 1.92-6.127 2.529-9.153-.221-.027-6.138-.765-8.428-2.186-1.994-1.237-4.191-3.359-4.412-5.831-.222-2.417 1.514-4.705 4.043-4.678 3.517.028 7.09 5.268 8.022 8.258.074.23.729 3.681.812 4.308.037-.184.093-.36.13-.544.618-3.239.812-6.458.84-9.485a64.428 64.428 0 0 0-.167-5.065c-1.117-.027-6.212-.193-8.418-1.245-2.114-1.006-4.533-2.879-5.031-5.314-.49-2.381.987-4.844 3.507-5.102 3.499-.36 7.625 4.456 8.881 7.325.064.157.591 1.818.951 3.045-.065-.738-.111-1.495-.185-2.196-.268-2.639-.683-5.047-1.062-7.169a98.901 98.901 0 0 0-.673-3.312c-1.081-.59-5.41-3.045-6.859-4.964-1.413-1.872-2.696-4.64-2.013-7.03.665-2.334 3.111-3.838 5.465-2.906 3.268 1.292 4.708 7.464 4.505 10.592-.018.267-.785 4.465-.84 4.437 0 0-.055-.027-.065-.037.24.969.48 2.021.72 3.165.425 2.122.887 4.521 1.219 7.169.332 2.647.618 5.526.655 8.589.037 3.072-.083 6.301-.646 9.623 0 .037-.018.074-.028.11.462-.922 1.847-3.441 1.976-3.625 1.809-2.565 6.813-6.459 10.163-5.416 2.419.756 3.379 3.46 2.419 5.692-.969 2.279-3.711 3.635-5.991 4.198-2.566.637-8.207-.406-8.65-.489-.553 3.156-1.384 6.339-2.4 9.522a134.325 134.325 0 0 1-1.883 5.443c.425-.784.812-1.476.877-1.569 1.809-2.564 6.813-6.458 10.164-5.415 2.418.756 3.378 3.46 2.418 5.692-.969 2.279-3.711 3.635-5.991 4.198-2.308.572-7.136-.212-8.409-.443-.259.692-.517 1.384-.785 2.076-1.274 3.312-2.557 6.615-3.711 9.9-.406 1.153-.766 2.306-1.135 3.459.378-.692.701-1.273.766-1.356 1.809-2.565 6.812-6.458 10.163-5.415 2.419.756 3.379 3.459 2.419 5.692-.969 2.279-3.711 3.635-5.991 4.198-2.206.553-6.665-.139-8.179-.406a77.537 77.537 0 0 0-.951 3.552c-.166.803-.388 1.559-.508 2.38-.138.803-.277 1.606-.415 2.39-.212 1.531-.434 3.109-.6 4.604-.12 1.042-.222 2.029-.323 3.035.397-1.153 1.209-3.395 1.302-3.58.932-1.863 3.166-4.521 5.575-5.941.139-.277.296-.545.48-.785 1.459-1.937 4.283-2.472 6.148-.747 2.585 2.38 1.708 8.654.397 11.505-.074.166-1.126 1.901-1.8 2.98.498-.572.969-1.135 1.486-1.725a165.679 165.679 0 0 1 3.185-3.515c.563-.572 1.135-1.144 1.708-1.726.554-.59 1.218-1.144 1.837-1.716a79.94 79.94 0 0 1 4.56-3.801c-1.016-.895-4.579-4.105-5.566-6.246-.979-2.131-1.634-5.111-.462-7.298 1.145-2.14 3.859-3.09 5.963-1.67 2.917 1.966 3 8.295 2.133 11.312-.056.184-.923 2.278-1.422 3.422.951-.738 1.902-1.476 2.88-2.186 2.843-2.058 5.797-4.041 8.724-6.043.895-.609 1.781-1.236 2.677-1.855-.37-.313-4.69-4.041-5.788-6.439-.979-2.132-1.634-5.112-.471-7.298 1.145-2.141 3.859-3.091 5.963-1.67 2.917 1.965 3 8.303 2.133 11.311-.065.231-1.32 3.238-1.68 3.986 1.957-1.366 3.904-2.74 5.76-4.171 2.566-1.974 4.975-4.05 7.163-6.236-.166-.148-4.698-4.023-5.825-6.468-.978-2.131-1.634-5.111-.461-7.298 1.144-2.14 3.858-3.09 5.963-1.669 2.917 1.965 3 8.303 2.133 11.311-.065.24-1.422 3.469-1.708 4.041.129-.13.277-.249.406-.379 2.308-2.361 4.237-4.945 5.936-7.445.987-1.449 1.846-2.897 2.658-4.318-.914-.636-5.077-3.58-6.332-5.683-1.21-2.011-2.188-4.899-1.265-7.206.905-2.251 3.499-3.496 5.742-2.315 3.111 1.633 3.895 7.916 3.36 11.006-.028.166-.517 1.846-.886 3.063.35-.645.729-1.31 1.061-1.937 1.237-2.353 2.216-4.585 3.074-6.569.48-1.135.886-2.168 1.265-3.137-.572-1.089-2.834-5.526-2.982-7.916-.148-2.334.314-5.36 2.197-6.975 1.846-1.587 4.717-1.485 6.167.591 2.012 2.878-.194 8.82-2.096 11.32-.166.212-3.12 3.294-3.148 3.238 0 0-.027-.055-.037-.064-.341.941-.72 1.947-1.144 3.035-.813 2.002-1.754 4.263-2.945 6.652-1.182 2.39-2.529 4.955-4.2 7.529-1.671 2.583-3.554 5.212-5.844 7.666a6.535 6.535 0 0 1-.083.084c.896-.508 3.444-1.855 3.647-1.938 2.926-1.144 9.24-1.633 11.465 1.089 1.597 1.965.904 4.751-1.126 6.089-2.068 1.365-5.105.987-7.311.203l-.01.018Z"
      />
      <path
        fill="#263238"
        d="M377.028 298.427c-.064 0 .914-4.152 1.007-4.401 1.061-2.952 4.864-8.027 8.372-7.907 2.53.083 4.173 2.445 3.84 4.853-.332 2.454-2.621 4.484-4.661 5.637-2.4 1.347-8.558 1.818-8.558 1.818Z"
      />
      <path stroke="#263238" strokeMiterlimit={10} d="m75.63 165.699.776.508" />
      <path
        stroke="#263238"
        strokeDasharray="2 2"
        strokeMiterlimit={10}
        d="m77.948 167.215 141.449 91.919"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="m220.172 259.639.776.498-.508-.766"
      />
      <path
        stroke="#263238"
        strokeDasharray="1.98 1.98"
        strokeMiterlimit={10}
        d="M219.443 257.83 98.109 71.371"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="m97.61 70.607-.507-.775M366.265 165.699l-.776.508"
      />
      <path
        stroke="#263238"
        strokeDasharray="2 2"
        strokeMiterlimit={10}
        d="m363.948 167.215-141.449 91.919"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="m221.723 259.639-.775.498.498-.766"
      />
      <path
        stroke="#263238"
        strokeDasharray="1.98 1.98"
        strokeMiterlimit={10}
        d="M222.453 257.83 343.787 71.371"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="m344.285 70.607.499-.775M220.948 41.21v.923"
      />
      <path
        stroke="#263238"
        strokeDasharray="2.01 2.01"
        strokeMiterlimit={10}
        d="M220.948 43.988v214.303"
      />
      <path stroke="#263238" strokeMiterlimit={10} d="M220.948 259.215v.922" />
      <path
        fill="#263238"
        stroke="#263238"
        strokeMiterlimit={10}
        strokeWidth={2}
        d="M222.674 65.412c15.677 0 28.386-12.702 28.386-28.37 0-15.668-12.709-28.37-28.386-28.37s-28.386 12.702-28.386 28.37c0 15.668 12.709 28.37 28.386 28.37Z"
      />
      <path
        fill="#DFFF32"
        stroke="#263238"
        strokeMiterlimit={10}
        d="m222.674 37.04 23.89-15.315c-3.499-5.434-8.788-9.595-15.047-11.643l-8.843 26.959Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeMiterlimit={10}
        d="m222.674 37.042 13.413 25.012c8.917-4.789 14.973-14.19 14.973-25.012a28.175 28.175 0 0 0-4.496-15.315l-23.89 15.315Z"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="M360.117 89.71c11.081-11.075 11.081-29.032 0-40.108-11.082-11.075-29.049-11.075-40.131 0-11.082 11.076-11.082 29.033 0 40.108 11.082 11.076 29.049 11.076 40.131 0Z"
      />
      <path
        fill="#263238"
        d="M340.048 94.241c13.587 0 24.601-11.008 24.601-24.587 0-13.58-11.014-24.588-24.601-24.588-13.586 0-24.601 11.009-24.601 24.588 0 13.58 11.015 24.587 24.601 24.587Z"
      />
      <path
        fill="#fff"
        d="m354.209 69.654-23.826-13.756v27.503l23.826-13.746Z"
      />
      <path
        fill="#263238"
        d="M409.512 169.08c3.81-15.897-5.996-31.87-21.901-35.678-15.905-3.807-31.887 5.993-35.697 21.889-3.81 15.897 5.996 31.87 21.901 35.677 15.905 3.808 31.887-5.992 35.697-21.888Z"
      />
      <path
        fill="#fff"
        d="M401.308 167.107c2.711-11.355-4.302-22.756-15.663-25.465-11.361-2.71-22.769 4.299-25.479 15.654-2.711 11.355 4.301 22.756 15.662 25.465 11.362 2.709 22.769-4.299 25.48-15.654Z"
      />
      <path
        fill="#263238"
        d="M393.052 165.106c1.619-6.817-2.597-13.655-9.417-15.274-6.82-1.619-13.662 2.595-15.282 9.411-1.62 6.817 2.596 13.655 9.416 15.274 6.821 1.619 13.663-2.594 15.283-9.411Z"
      />
      <path
        fill="#fff"
        d="M387.387 163.778a6.826 6.826 0 0 0-5.072-8.217 6.828 6.828 0 1 0-3.15 13.287 6.832 6.832 0 0 0 8.222-5.07Z"
      />
      <path
        fill="#263238"
        d="m418.458 147.983-3.767-1.476 1.644-.803a.786.786 0 1 0-.693-1.412l-1.191.582 1.893-4.097-7.025 3.165-1.025 3.93-27.924 13.59a.784.784 0 0 0-.36 1.052.794.794 0 0 0 .711.443.774.774 0 0 0 .341-.083l28.017-13.636 3.055 1.725 6.324-2.989v.009Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeMiterlimit={10}
        strokeWidth={2}
        d="M99.586 98.008c16.35 0 29.604-13.247 29.604-29.588s-13.254-29.588-29.604-29.588S69.98 52.079 69.98 68.42c0 16.34 13.255 29.588 29.605 29.588Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeMiterlimit={10}
        d="M99.586 98.008c11.858 0 21.472-13.247 21.472-29.588s-9.614-29.588-21.472-29.588c-11.859 0-21.472 13.247-21.472 29.588 0 16.34 9.613 29.588 21.472 29.588Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeMiterlimit={10}
        d="M99.586 98.008c5.047 0 9.138-13.247 9.138-29.588s-4.091-29.588-9.138-29.588c-5.048 0-9.14 13.247-9.14 29.588 0 16.34 4.092 29.588 9.14 29.588Z"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="M83.893 43.426h31.902M73.166 55.023h52.775M70.12 66.613h59.015M71.43 78.207h55.96M79.406 89.797h40.885"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeMiterlimit={10}
        strokeWidth={2}
        d="M60.833 191.774c16.35 0 29.605-13.247 29.605-29.588s-13.255-29.588-29.605-29.588-29.604 13.247-29.604 29.588 13.254 29.588 29.604 29.588Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeMiterlimit={10}
        strokeWidth={2}
        d="M79.591 148.199h-39.26v31.396h39.26v-31.396Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeMiterlimit={10}
        strokeWidth={2}
        d="M82.047 148.199H37.876v12.751h44.17v-12.751Z"
      />
      <path
        fill="#DFFF32"
        d="M59.92 147.887S50.65 137.083 49.11 137.6c-1.542.517-5.659 7.722-3.342 8.488 2.317.766 14.16 1.799 14.16 1.799h-.008ZM60.002 147.887s9.268-10.804 10.81-10.287c1.542.517 5.659 7.722 3.342 8.488-2.317.766-14.16 1.799-14.16 1.799h.008ZM62.753 170.268l-2.778-4.826-2.89 4.826V148.66h5.668v21.608Z"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m73.941 243.696 146.905-82.637 150.505 82.637v119.801H75.741l-1.8-119.801Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeMiterlimit={10}
        d="M332.598 183.348H112.694v195.463h219.904V183.348Z"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="M137.932 217.578h166.724M137.932 234.691h166.724M137.932 251.809h166.724M137.932 268.922h166.724M137.932 286.035h166.724M137.932 303.148h166.724"
      />
      <path
        fill="#DFFF32"
        stroke="#263238"
        strokeMiterlimit={10}
        d="M371.351 408.537H73.941V243.695l148.705 100.887 148.705-100.887v164.842Z"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="m73.941 408.539 108.125-91.531M370.88 408.539l-107.331-91.68"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M254.817 128.86s-1.117 5.222.369 6.901c1.486 1.679 7.09 7.649 7.09 8.58 0 .932-1.865 27.798-1.865 27.798l-8.585-6.153-5.225 16.044s13.062 5.6 19.229 6.716c6.157 1.117 7.283-.562 7.283-5.406 0-4.844-4.856-41.979-4.856-41.979s1.496-8.58 1.496-10.074c0-1.495.563-2.242-.748-2.99-1.311-.747-11.945-1.863-11.945-1.863s-2.243.931-2.243 2.426Z"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m252.047 168.246-2.843 14.384-2.603-.6 5.446-13.784Z"
      />
      <path
        fill="#263238"
        d="m273.011 133.049-21.148-2.842v-5.268a.906.906 0 0 1 1.809 0v3.691l17.678 2.38.304-4.262a.888.888 0 0 1 .96-.831.898.898 0 0 1 .831.96l-.443 6.181.009-.009Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m256.682 131.287 11.576 1.116.747-4.106-11.945-1.863-.378 4.853Z"
      />
      <path
        fill="#fff"
        d="M254.632 131.85s0-4.668.563-5.037c.564-.369 8.585.184 8.585 1.31 0 1.126 0 1.679-.932 1.679s-2.797.185-2.797.185 4.293 3.921 2.991 7.464"
      />
      <path
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M254.632 131.85s0-4.668.563-5.037c.564-.369 8.585.184 8.585 1.31 0 1.126 0 1.679-.932 1.679s-2.797.185-2.797.185 4.293 3.921 2.991 7.464M260.42 172.141l-.748 8.211"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M165.625 168.562s-26.382 20.584-25.699 22.798c.683 2.214 20.253 20.925 26.724 25.519 6.471 4.595 10.043 6.634 10.043 6.634l4.09 2.555s-1.699 2.722-.683 3.23c1.015.507 4.763 2.38 5.28 1.365.508-1.024 3.231-8.672 2.889-10.545-.341-1.873-10.209-3.405-10.893-4.087-.683-.683-16.856-25.861-16.856-25.861l12.601-9.872-7.487-11.736h-.009Z"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m165.173 169.246 4.782 12.981 3.157-1.928-7.939-11.053Z"
      />
      <path
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M180.773 226.059s1.191 2.555 3.231 2.721M160.521 190.162l-3.407-5.951"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m183.155 376.444-.267 15.122 12.397.267 1.089-16.478-13.219 1.089Z"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M183.202 375.621v3.737l12.859.599.295-3.884-13.154-.452ZM189.017 387.619s-1.634-6.403-4.089-6.542c-2.456-.138-3.684 1.772-4.634 3.543-.951 1.772-6.952 7.759-8.992 9.263-2.04 1.504-6.544 3.405-7.218 7.759-.674 4.355 1.91 6.126 3.683 6.542 1.772.415 9.813.544 14.585 0 4.773-.545 17.789.747 18.536 0 .748-.748 1.773-3.137 1.09-4.77-.684-1.633-1.911-3.405-1.911-3.405s1.366-3.948.544-6.126c-.821-2.177-2.455-4.899-2.178-6.126.277-1.227 3.268-3.81 1.228-3.949-2.041-.138-5.318 2.316-7.358 2.999-2.04.683-3.268.821-3.268.821l-.018-.009Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M197.74 400.555c-1.91.277-9.268 3.811-13.902 4.899-4.634 1.089-8.723 1.227-10.902.139-2.178-1.089-4.089-4.355-5.723-6.403-1.634-2.048-3 3.137-3 3.137l-.222.101c-.277 3.792 2.105 5.37 3.767 5.757 1.772.406 9.812.545 14.585 0 4.772-.544 17.788.748 18.536 0 .748-.747 1.772-3.137 1.089-4.77-.683-1.633-1.91-3.404-1.91-3.404-.822-.138-.407.277-2.318.544ZM177.838 388.43s3.683-.821 8.317 3.681l-2.455 2.039s-4.773-3.681-7.763-3.681l1.91-2.039h-.009Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M174.432 391.16s3.683-.821 8.317 3.681l-2.455 2.039s-4.773-3.681-7.764-3.681l1.911-2.039h-.009Z"
      />
      <path
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m185.195 390.745 3.822-3.128"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M180.294 396.88s.951 2.039-.683 3.266c-1.634 1.227-12.398-.821-12.269-2.315.139-1.495 5.179-4.632 5.179-4.632s4.09.821 7.764 3.681h.009ZM261.352 376.444l.277 15.122-12.406.267-1.09-16.478 13.219 1.089Z"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M248.374 378.162v2.242l13.154-.148-.148-3.284-13.302-.148.296 1.338ZM255.491 387.619s1.634-6.403 4.089-6.542c2.456-.138 3.683 1.772 4.634 3.543.951 1.772 6.951 7.759 8.991 9.263 2.04 1.504 6.545 3.405 7.219 7.759.683 4.355-1.911 6.126-3.683 6.542-1.773.405-9.813.544-14.585 0-4.773-.545-17.789.747-18.537 0-.747-.748-1.772-3.137-1.089-4.77.683-1.633 1.911-3.405 1.911-3.405s-1.366-3.948-.545-6.126c.822-2.177 2.456-4.899 2.179-6.126-.277-1.227-3.268-3.81-1.228-3.949 2.04-.138 5.317 2.316 7.357 2.999 2.04.683 3.268.821 3.268.821l.019-.009Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M246.777 400.555c1.91.277 9.268 3.811 13.902 4.899 4.634 1.089 8.723 1.227 10.902.139 2.178-1.089 4.089-4.355 5.723-6.403 1.634-2.048 3 3.137 3 3.137l.222.101c.277 3.792-2.105 5.37-3.767 5.757-1.772.406-9.812.545-14.585 0-4.772-.544-17.788.748-18.536 0-.748-.747-1.772-3.137-1.089-4.77.683-1.633 1.911-3.404 1.911-3.404.821-.138.406.277 2.317.544ZM266.67 388.43s-3.683-.821-8.317 3.681l2.455 2.039s4.773-3.681 7.763-3.681l-1.91-2.039h.009Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M270.076 391.16s-3.683-.821-8.317 3.681l2.455 2.039s4.773-3.681 7.764-3.681l-1.911-2.039h.009Z"
      />
      <path
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m259.312 390.745-3.821-3.128"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M264.214 396.88s-.951 2.039.683 3.266c1.634 1.227 12.398-.821 12.268-2.315-.138-1.495-5.178-4.632-5.178-4.632s-4.09.821-7.764 3.681h-.009Z"
      />
      <path
        fill="#B0B0B0"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M188.851 216.842s-7.21 29.422-9.914 54.341c-2.705 24.92-6.905 106.589-6.905 106.589h33.047s9.915-82.869 11.419-97.584c1.505-14.716 1.505-24.016 1.505-24.016l18.628 121.6h39.353s-15.019-77.167-21.029-112.595c-6.009-35.428-14.419-49.239-14.419-49.239l-51.667.904h-.018Z"
      />
      <path
        fill="#B0B0B0"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M217.089 218.946c-.286.858-8.114 12.612-5.705 13.507 2.4.904 6.905.904 6.905-1.504v-9.604s.6 13.507 3.305 12.907c2.705-.6 6.305 0 5.409-2.999-.904-2.998-7.809-12.612-7.809-12.612s-1.2-2.398-2.105.305Z"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="M218.326 219.934s6.813 9.668 6.157 10.545c-.655.876-1.541 1.762-2.197.443-.655-1.32-3.96-10.988-3.96-10.988ZM218.114 218.836s-5.059 10.767-4.394 11.21c.655.442 2.197.442 2.64-1.098.443-1.541 1.763-10.112 1.763-10.112h-.009Z"
      />
      <path
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M238.644 249.52c.276 1.531.507 2.961.683 4.253M231.508 219.848s3.748 13.377 6.157 24.735M228.803 219.848s2.4 16.81 3.61 21.321M196.116 243.125c-.129 1.162-.24 2.224-.351 3.137M198.47 219.848s-.997 10.277-1.874 18.756M196.06 220.449l-1.2 14.411"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M217.107 97.336s11.198 1.116 8.585 6.901c-2.612 5.785-5.224 6.901-5.788 6.901-.563 0-5.04-6.901-5.04-6.901l2.243-6.901Z"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M215.806 102.741s4.293 2.426 5.603 6.901c1.311 4.474 4.671 5.785 5.604 5.6.932-.184 2.243 2.611 1.117 4.475l-1.117 1.863s.932 12.686-.185 14.365c-1.117 1.679-8.022.748-8.022.748s-1.495 5.969-.184 9.696c1.31 3.728 5.224 5.6 6.72 5.6 1.495 0 4.671-1.863 8.963.369 4.293 2.242 18.666 13.055 18.666 13.055l-7.653 20.897-8.216-4.29 5.788 35.631-55.996 1.31s.563-25.371-.932-30.04c-1.496-4.668-3.923-8.958-3.923-8.958l-7.09 5.6-9.896-17.16s14.558-14.55 20.346-15.86c5.788-1.31 12.693.369 14.936-2.426 2.243-2.796.369-16.045.369-16.045s-5.41-1.863-8.585-11.375c-3.176-9.512 7.653-19.403 7.653-19.403s4.855-5.037 16.053-.562l-.019.009Z"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m218.788 136.693-5.04-.849 4.052 4.659.988-3.81Z"
      />
      <path
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M216.425 112.844s0-3.543 3.175-3.543"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m224.76 125.685-6.535-3.173s-.564 6.716 1.864 6.532c2.428-.185 4.671-3.359 4.671-3.359Z"
      />
      <path
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M218.373 116.305s2.104-2.279 3.858-.351"
      />
      <path
        fill="#fff"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m218.178 124.643 5.871 1.91c.444-.498.711-.868.711-.868l-6.535-3.173s-.084.95-.047 2.131Z"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M181.641 110.767s-4.107-8.396-9.895-7.464c-5.788.932-10.265 7.279-8.401 16.228 0 0 .933 2.427 1.68 4.475 0 0-7.283-2.242-11.197 2.611-3.923 4.853-.932 9.89 3.545 12.317 4.477 2.426 7.089 2.242 7.089 2.242s-9.896 2.989-6.351 6.716c3.545 3.727 14.373 1.31 12.13-1.863-2.243-3.174-3.923-2.796-3.923-2.796s8.963-3.358 8.022-5.785c-.933-2.426-5.41-4.29-5.41-4.29s8.585 2.989 13.256.185c4.671-2.805.563-14.734.563-14.734l-1.117-7.833.009-.009Z"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M219.166 93.23s1.495 5.221-1.117 9.696c-2.612 4.474-8.216 12.123-8.963 20.712-.748 8.58 1.68 19.218 3.175 22.761 1.495 3.543-1.865 4.29-4.292 4.106-2.428-.185-3.361-1.68-3.361-5.038 0-3.358-.747-9.327-.747-9.327s-9.518-.932-14.004-3.543c-4.477-2.611-11.013-11.57-8.216-21.829 2.797-10.259 10.265-13.249 14.742-15.112 4.478-1.864 10.644-1.68 14.936-2.611 4.293-.932 7.653-2.049 7.838.184h.009Z"
      />
      <path
        fill="#fff"
        d="m205.541 119.347-1.68 6.717s-3.545-1.679-4.108-4.669c-.563-2.989 3.729-4.474 6.72-1.863"
      />
      <path
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m205.541 119.347-1.68 6.717s-3.545-1.679-4.108-4.669c-.563-2.989 3.729-4.474 6.72-1.863M260.042 129.977l-2.428.193"
      />
      <path
        fill="#DFFF32"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m242.585 124.59 62.209 8.7-8.447-46-55.682 26.792 1.92 10.508ZM240.653 114.096l-9.244 1.689 1.913 10.455 9.244-1.689-1.913-10.455Z"
      />
      <path
        fill="#263238"
        stroke="#263238"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M252.952 165.425s-14.373-10.822-18.666-13.054c-4.237-2.205-7.375-.425-8.899-.379-2.353 3.377-7.846 9.088-17.982 8.959-12.277-.157-15.702-6.984-16.441-8.977-1.855.055-3.775.166-5.584.581-5.788 1.31-20.346 15.86-20.346 15.86l9.896 17.16 7.09-5.6s2.427 4.29 3.923 8.958c1.495 4.669.932 30.04.932 30.04l55.996-1.31-5.788-35.631 8.216 4.29 7.653-20.897Z"
      />
      <path
        stroke="#263238"
        strokeMiterlimit={10}
        d="M18.481 408.539h406.457"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h441v409H0z" />
      </clipPath>
    </defs>
  </svg>
)
export default GettingStartedImage;
