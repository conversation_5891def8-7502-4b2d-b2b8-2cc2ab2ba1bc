import * as React from "react"

interface ConditionToolTipIconProps {
  color?: string;
}

const ConditionToolTipIcon = ({
  color = "#7856FF",
}: ConditionToolTipIconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={25}
    height={25}
    fill="none"
  >
    <path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M14.5 16.75h-.75c-.83 0-1.5-.67-1.5-1.5V11.5c0-.41-.34-.75-.75-.75h-.75"
    />
    <path
      fill={color}
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M11.87 7a.38.38 0 1 0 0 .76.38.38 0 0 0 0-.76Z"
    />
    <path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M12.25 23.5c6.21 0 11.25-5.04 11.25-11.25S18.46 1 12.25 1 1 6.04 1 12.25 6.04 23.5 12.25 23.5Z"
    />
  </svg>
)
export default ConditionToolTipIcon
