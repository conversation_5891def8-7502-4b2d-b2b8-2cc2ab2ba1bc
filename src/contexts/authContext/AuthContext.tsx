import { createContext, useState, useEffect, useCallback } from "react";
import { authProvider } from "../../authProvider";

export type UserRole =
    | "authenticated"
    | "administrator"
    | "admin"
    | "employee"
    | "ergo ambassador"
    | "ergonomist";

export interface RoleObject {
    id: number;
    name: string;
    description: string;
    type: string;
    createdAt?: string;
    updatedAt?: string;
}

export interface User {
    id?: number;
    username?: string;
    email?: string;
    role?: RoleObject;
    employeeView?: boolean;
    [key: string]: any;
}

export interface AuthContextType {
    userDetails: User | undefined;
    isLoading: boolean;
    error: Error | null;
    login: (userData: User) => void;
    logout: () => void;
}

interface AuthContextProviderProps {
    children: React.ReactNode;
}


const initialState: AuthContextType = {
    userDetails: undefined,
    isLoading: true,
    error: null,
    login: () => { },
    logout: () => { }
};

export const AuthContext = createContext<AuthContextType>(initialState);

export const AuthContextProvider: React.FC<AuthContextProviderProps> = ({ children }) => {
    const [userDetails, setUserDetails] = useState<User | undefined>();
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    const login = useCallback((userData: User) => {
        if (userData && userData.role) {
            setUserDetails(userData);
            localStorage.setItem("userDetails", JSON.stringify(userData));
        }
        setIsLoading(false);
    }, []);

    const logout = useCallback(() => {
        setUserDetails(undefined);
        localStorage.removeItem("userDetails");
        setIsLoading(false);
    }, []);

    useEffect(() => {
        const initializeAuth = async () => {
            try {
                const storedUserDetails = localStorage.getItem("userDetails");
                //   console.log('Stored details:',  JSON.parse(storedUserDetails as any ));
                if (storedUserDetails) {
                    const parsedUserDetails = JSON.parse(storedUserDetails);
                    // Directly set the user details
                    setUserDetails(parsedUserDetails);
                } else if (authProvider.getIdentity) {
                    const user = await authProvider.getIdentity();
                    if (user) {
                        setUserDetails(user);
                        localStorage.setItem("userDetails", JSON.stringify(user));
                    }
                }
            } catch (err) {
                setError(err instanceof Error ? err : new Error('Failed to initialize auth'));
            } finally {
                setIsLoading(false);
            }
        };

        initializeAuth();
    }, []);

    const value = {
        userDetails,
        isLoading,
        error,
        login,
        logout
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};