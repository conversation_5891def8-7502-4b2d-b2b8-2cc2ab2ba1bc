import MLButton from "../../components/ui/MLButton/MLButton";
import { Stack } from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import React, {
  createContext,
  useState,
  useContext,
  PropsWithChildren,
} from "react";

interface ErrorDialogContextType {
  showError: (
    message: string,
    action?: () => void,
    actionMessage?: string,
  ) => void;
  closeError: () => void;
}

const ErrorDialogContext = createContext({} as ErrorDialogContextType);

export const useErrorDialog = () => {
  return useContext(ErrorDialogContext);
};

const ErrorDialogProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [error, setError] = useState<string | null>(null);
  const [actionItem, setActionItem] = useState<{
    action: () => void;
    actionMessage: string;
  }>();

  const showError = (
    message: string,
    action?: () => void,
    actionMessage?: string,
  ) => {
    setError(message);
    if (action && actionMessage) setActionItem({ action, actionMessage });
  };

  const closeError = () => {
    setError(null);
  };

  return (
    <ErrorDialogContext.Provider value={{ showError, closeError }}>
      {children}
      <Dialog
        open={Boolean(error)}
        onClose={closeError}
        fullWidth
        maxWidth="md"
      >
        <Stack
          direction={"column"}
          alignItems={"center"}
          gap={2}
          py={4}
          px={8}
          flexWrap={"nowrap"}
          style={{
            backgroundImage: "url('/background_waves/horizontal1.svg')",
            backgroundSize: "cover",
            backgroundAttachment: "fixed",
          }}
        >
          <DialogTitle variant="h1">An error occurred.</DialogTitle>
          <DialogContent>
            <DialogContentText>{error}</DialogContentText>
          </DialogContent>
          <DialogActions>
            {actionItem ? (
              <MLButton
                onClick={() => {
                  actionItem.action();
                  closeError();
                }}
                color="primary"
                variant="contained"
              >
                {actionItem.actionMessage}
              </MLButton>
            ) : null}
            <MLButton onClick={closeError} color="primary" variant="outlined">
              Close
            </MLButton>
          </DialogActions>
        </Stack>
      </Dialog>
    </ErrorDialogContext.Provider>
  );
};

export default ErrorDialogProvider;
