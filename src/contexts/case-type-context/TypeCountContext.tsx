import Case from "../../models/Case";
import { GetListResponse } from "@refinedev/core";
import React, { createContext, useContext, useState, ReactNode } from "react";

// Define the type for the context value
interface TypeCountsContextType {
  typeCounts: Record<string, number>;
  setTypeCounts: React.Dispatch<React.SetStateAction<Record<string, number>>>;
  prevMonthsTypeCounts: Record<string, number>;
  setPrevMonthTypeCounts: React.Dispatch<
    React.SetStateAction<Record<string, number>>
  >;
  prevMonthName: string;
  setPrevMonthName: (name: string) => void;
  allCases: GetListResponse<Case> | undefined;
  setAllCases: React.Dispatch<
    React.SetStateAction<GetListResponse<Case> | undefined>
  >;
}

// Initial state for the type counts
const initialTypeCounts: Record<string, number> = {};
const prevMonthTypeCounts: Record<string, number> = {};

// Create a context with the defined type
const TypeCountsContext = createContext<TypeCountsContextType | undefined>(
  undefined,
);

// Custom hook to use the TypeCountsContext
export const useTypeCountsContext = () => {
  const context = useContext(TypeCountsContext);
  if (!context) {
    throw new Error("useTypeCounts must be used within a TypeCountsProvider");
  }
  return context;
};

// Provider component to wrap the application and provide context values
export const TypeCountsProvider = ({ children }: { children: ReactNode }) => {
  // State for current month's Case type counts
  const [typeCounts, setTypeCounts] =
    useState<Record<string, number>>(initialTypeCounts);
  // State for previous month's Case type counts
  const [prevMonthsTypeCounts, setPrevMonthTypeCounts] =
    useState<Record<string, number>>(prevMonthTypeCounts);
  // State for previous month's name
  const [prevMonthName, setPrevMonthName] = useState("");
  const [allCases, setAllCases] = useState<GetListResponse<Case> | undefined>(
    undefined,
  );

  return (
    <TypeCountsContext.Provider
      value={{
        typeCounts,
        setTypeCounts,
        prevMonthsTypeCounts,
        setPrevMonthTypeCounts,
        prevMonthName,
        setPrevMonthName,
        allCases,
        setAllCases,
      }}
    >
      {children}
    </TypeCountsContext.Provider>
  );
};
