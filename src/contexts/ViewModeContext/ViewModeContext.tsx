import React, { createContext, useEffect, useState, useContext, useCallback, useRef } from 'react';
import Loading from '../../pages/Loading/Loading';
import { API_URL, TOKEN_KEY } from '../../constants';
import { AuthContext } from '../authContext/AuthContext';
import { useNavigate } from 'react-router-dom';

interface ViewModeContextType {
    toggleViewMode: (payload?: any) => void;
    isEmployeeView: boolean;
}

const initialState: ViewModeContextType = {
    toggleViewMode: () => { },
    isEmployeeView: true,
};

export const ViewModeContext = createContext<ViewModeContextType>(initialState);

export const ViewModeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { userDetails, login } = useContext(AuthContext);
    const [isEmployeeView, setIsEmployeeView] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    // Use ref to track the current value without causing re-renders
    const navigate = useNavigate();
    const isEmployeeViewRef = useRef(true);

    // Update ref when state changes
    useEffect(() => {
        isEmployeeViewRef.current = isEmployeeView;
    }, [isEmployeeView]);

    // Initialize from server-validated userDetails
    useEffect(() => {
        if (userDetails) {
            setIsEmployeeView(!!userDetails.employeeView);
        }
    }, [userDetails]);

    // Memoize the verification function to avoid recreating it on every render
    const verifyEmployeeViewStatus = useCallback(async () => {
        if (!userDetails || !userDetails.id) return;

        try {
            const token = localStorage.getItem(TOKEN_KEY);
            if (!token) return;

            const response = await fetch(`${API_URL}/api/users/${userDetails.id}`, {
                method: "GET",
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (response.status === 200) {
                const userData = await response.json();
                const serverEmployeeView = !!userData.employeeView;

                // Compare with ref value to avoid stale state issues
                if (serverEmployeeView !== isEmployeeViewRef.current) {
                    setIsEmployeeView(serverEmployeeView);

                    // Only update auth context if needed
                    if (userDetails.employeeView !== serverEmployeeView) {
                        login({
                            ...userDetails,
                            employeeView: serverEmployeeView
                        });
                    }
                }
            }
        } catch (error) {
            // Silent error handling or add logging as needed
            // console.error('Verification error:', error);
        }
    }, [userDetails, login]);

    // Set up verification on component mount and periodic checks
    useEffect(() => {
        // Verify on component mount
        verifyEmployeeViewStatus();

        // Set up periodic verification
        const intervalId = setInterval(verifyEmployeeViewStatus, 60000); // every minute

        return () => clearInterval(intervalId);
    }, [verifyEmployeeViewStatus]);

    const toggleViewMode = async (payload: any) => {
        if (!userDetails || !userDetails.id) return;

        const updatedEmployeeView = !isEmployeeView;
        setIsLoading(true);

        try {
            const token = localStorage.getItem(TOKEN_KEY);
            if (!token) {
                setIsLoading(false);
                return;
            }

            const updateEmployeeViewModeRes = await fetch(`${API_URL}/api/users/${userDetails.id}`, {
                method: "PUT",
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ employeeView: updatedEmployeeView }),
            });

            if (updateEmployeeViewModeRes.status !== 200) {
                const result = await updateEmployeeViewModeRes.json();
                // console.error('API error:', result.error);
                setIsLoading(false);
                return;
            }

            // Create updated user details
            const updatedUserDetails = {
                ...userDetails,
                employeeView: updatedEmployeeView
            };

            // Update both contexts simultaneously
            setIsEmployeeView(updatedEmployeeView);

            // Critical: Update the AuthContext by calling login with updated user details
            // This ensures both contexts have the same data
            login(updatedUserDetails);


            // Navigate to the appropriate home page after mode toggle
            if (updatedEmployeeView) {
                // If switching to employee view, navigate to employee home
                navigate('/');
            } else {
                // If switching to admin view, navigate to admin home
                navigate('/analytics');
            }
        } catch (error) {
            // console.error('API error:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const value = {
        isEmployeeView,
        toggleViewMode,
    };

    return (
        <ViewModeContext.Provider value={value}>
            {isLoading && <Loading />}
            {!isLoading && children}
        </ViewModeContext.Provider>
    );
};