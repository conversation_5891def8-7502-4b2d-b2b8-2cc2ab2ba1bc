import AssessmentReportToggle from "../../components/AssessmentReportToggle/AssessmentReportToggle";
import MLButton from "../../components/ui/MLButton/MLButton";
import MLTypography from "../../components/ui/MLTypography/MLTypography";
import Case, { CaseStatus } from "../../models/Case";
import { desktop, tablet } from "../../responsiveStyles";
import AssessmentGrid from "./AssessmentGrid";
import ExpertassessmentRsi from "./ExpertassessmentRsi";
import { ErChevronleft } from "@mindlens/ergo-icons";
import { Avatar, IconButton, Stack } from "@mui/material";
import { useBack, useGo, useShow } from "@refinedev/core";
import { useParams } from "react-router-dom";

const ExpertAssessment: React.FC = () => {
  const back = useBack();
  const go = useGo();
  const { id } = useParams();

  const { queryResult } = useShow<Case>({
    resource: "cases",
    id,
    meta: {
      populate: {
        employee: { populate: "*" },
        assessment: {
          populate: {
            report: {
              populate: {
                report_review: {
                  populate: { reviewer: { populate: "*" }, createdAt: "*" },
                },
                assigned_to: { populate: "*" },
              },
            },
          },
        },
        appointment: { populate: "*" },
        assessmentAssignedTo: { populate: "*" },
      },
    },
  });

  const { isLoading, data } = queryResult;

  return (
    <Stack
      direction={"column"}
      p={2}
      height={"100%"}
      sx={{
        backgroundImage: `url("/background_waves/horizontal2.svg")`,
        backgroundPosition: "center",
        backgroundSize: "cover",
        paddingX: {
          lg: desktop.contentContainer.paddingX,
          md: tablet.contentContainer.paddingX,
          xs: tablet.contentContainer.paddingX,
        },
        paddingY: {
          lg: desktop.contentContainer.paddingY,
          md: tablet.contentContainer.paddingY,
          xs: tablet.contentContainer.paddingY,
        },
      }}
    >
      <Stack
        direction="row"
        justifyContent={"space-between"}
        sx={{ flex: 0 }}
        marginBottom={{
          lg: desktop.contentContainer.header.marginBottom,
          md: tablet.contentContainer.header.marginBottom,
          xs: tablet.contentContainer.header.marginBottom,
        }}
        display={{ md: "flex", xs: "none" }}
      >
        <Stack direction="row" alignItems={"center"}>
          <IconButton size="large" onClick={() => back()}>
            <ErChevronleft />
          </IconButton>
          <MLTypography variant="h1" fontWeight={700}
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: { xs: "calc(100vw - 280px)" },
            }}
          >
            Expert Assessment
          </MLTypography>
        </Stack>

        <Stack
          direction="row"
          gap={1}
          justifyContent={"flex-end"}
          alignItems={"center"}
        >
          {data?.data?.employee ? (
            <MLButton
              onClick={() =>
                go({
                  to: `/employees/show/${data?.data.employee.id}`,
                })
              }
            >
              <Avatar sx={{ mr: 1, height: 30, width: 30 }}>
                {data?.data.employee.name.at(0) ?? ""}
              </Avatar>
              {data?.data.employee.name}
            </MLButton>
          ) : null}

          <AssessmentReportToggle
            current="Case details"
            items={[
              {
                key: "Case details",
                handler: () => go({ to: `/cases/show/${id}` }),
              },
              {
                key: "Assessment",
                disabled: !data?.data.assessment?.id,
                handler: () =>
                  data?.data.assessment
                    ? go({ to: `/assessments/${data?.data.assessment.id}` })
                    : console.error("Assessment not populated"),
              },
              {
                key: "Report",
                disabled: !data?.data.assessment?.report?.id,
                handler: () =>
                  go({
                    to: `/reports/edit/${data?.data.assessment?.report?.id}`,
                  }),
              },
              {
                key: "Follow Up",
                disabled: !data?.data.assessment?.report?.id,
                handler: () =>
                  go({
                    to: `/cases/followup/${data?.data.id}`,
                  }),
              },
              {
                key: "Close case",
                disabled: data?.data.status === CaseStatus.CLOSED,
                handler: () =>
                  go({
                    to: `/cases/close/${id}`,
                  }),
              },
            ]}
          />
        </Stack>
      </Stack>

      {/* header for mobile view */}
      <Stack
        direction={"column"}
        alignItems={"flex-start"}
        flex={1}
        marginBottom={{
          lg: desktop.contentContainer.header.marginBottom,
          md: tablet.contentContainer.header.marginBottom,
          xs: tablet.contentContainer.header.marginBottom,
        }}
        display={{ md: "none", xs: "flex" }}
        gap={"8px"}
      >
        <Stack direction={"row"} gap={1} justifyContent={"space-between"} width={"100%"}>
          <MLTypography variant="h1"
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: { xs: "calc(100vw - 100px)" },
            }}
          >Expert Assessment</MLTypography>
          <AssessmentReportToggle
            current="Case details"
            items={[
              {
                key: "Case details",
                handler: () => go({ to: `/cases/show/${id}` }),
              },
              {
                key: "Assessment",
                disabled: !data?.data.assessment?.id,
                handler: () =>
                  data?.data.assessment
                    ? go({ to: `/assessments/${data?.data.assessment.id}` })
                    : console.error("Assessment not populated"),
              },
              {
                key: "Report",
                disabled: !data?.data.assessment?.report?.id,
                handler: () =>
                  go({
                    to: `/reports/edit/${data?.data.assessment?.report?.id}`,
                  }),
              },
              {
                key: "Follow Up",
                disabled: !data?.data.assessment?.report?.id,
                handler: () =>
                  go({
                    to: `/cases/followup/${data?.data.id}`,
                  }),
              },
              {
                key: "Close case",
                disabled: data?.data.status === CaseStatus.CLOSED,
                handler: () =>
                  go({
                    to: `/cases/close/${id}`,
                  }),
              },
            ]}
          />
        </Stack>

        <Stack direction={"row"}>
          {data?.data?.employee ? (
            <MLButton
              sx={{
                padding: 0
              }}
              onClick={() =>
                go({
                  to: `/employees/show/${data?.data.employee.id}`,
                })
              }
            >
              <Avatar sx={{ mr: 1, height: 30, width: 30 }}>
                {data?.data.employee.name.at(0) ?? ""}
              </Avatar>
              {data?.data.employee.name}
            </MLButton>
          ) : null}
        </Stack>
      </Stack>
      {/* header for mobile view */}

      <ExpertassessmentRsi />

      <Stack mt={3}>
        <AssessmentGrid />
      </Stack>
    </Stack>
  );
};

export default ExpertAssessment;
