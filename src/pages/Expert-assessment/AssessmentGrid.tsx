import AssessmentHipsImage from "../../assets/expertAssessImg/Assessment_Hips.png";
import MLCheckbox from "../../components/ui/MLCheckbox/MLCheckbox";
import AssessmentCard from "./AssessmentCard";
import CustomCloseIcon from "./CustomCloseIcon";
import {
  Box,
  Grid,
  Typography,
  Popover,
  IconButton,
  FormControlLabel,
  Divider,
} from "@mui/material";
import { useCreate, useList, useUpdate } from "@refinedev/core";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

interface OptionConfig {
  id: string;
  option: string;
  optionText: string;
  optionImage: string | null;
  group: string;
  isLevelOne: boolean;
  levelOneText: string;
  optionCategory: {
    isAdjustable: boolean;
  };
}

interface AssessmentQuestionConfig {
  id: string;
  question: string;
  optionConfig: OptionConfig[];
}

interface AssessmentItem {
  label: string;
  imageSrc: string;
  id: string;
  isLevelOne: boolean;
  group: string;
  isAdjustable?: boolean; // Include the isAdjustable flag in AssessmentItem
}

interface AssessmentCategory {
  category: string;
  items: AssessmentItem[];
}

interface SelectedItem {
  label: string;
  checked: boolean;
  isAdjustable?: boolean;
}

interface SelectedGroup {
  [group: string]: SelectedItem[];
}

interface ChairOptions {
  [category: string]: SelectedGroup;
}

const AssessmentGrid: React.FC = () => {
  const { id } = useParams();
  const { mutate: createCaseDetails } = useCreate();
  const { mutate: updateCaseDetails } = useUpdate();

  const [selectedItems, setSelectedItems] = useState<ChairOptions>({});
  const [adjustableItems, setAdjustableItems] = useState<
    Record<string, boolean>
  >({});

  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [popoverOptions, setPopoverOptions] = useState<SelectedGroup | null>(
    null,
  );
  const [popoverCategory, setPopoverCategory] = useState<string | null>(null);

  const { data: expertAssessQuestion, isLoading: expertAssessQuestionLoading } =
    useList<AssessmentQuestionConfig>({
      resource: "expert-assessment-question-configs",
      pagination: {
        pageSize: 100,
      },
      meta: {
        populate: {
          optionConfig: {
            populate: "*", // Populate all fields within optionConfig
          },
        },
      },
      filters: [
        {
          field: "question",
          operator: "ne",
          value: "RSI",
        },
      ],
      sorters: [
        {
          field: "id",
          order: "asc",
        },
      ],
    });

  // console.log(expertAssessQuestion, "expertAssessQuestion");

  const { data: existingCaseDetail } = useList({
    resource: "case-details",
    filters: [
      {
        field: "case",
        operator: "eq",
        value: id,
      },
    ],
  });

  useEffect(() => {
    if (existingCaseDetail && existingCaseDetail.data.length > 0) {
      const workHabit = existingCaseDetail.data[0].workHabit || [];
      const workSetup = existingCaseDetail.data[0].workSetup || [];

      if (workHabit.length > 0) {
        const initialSelectedItems = workHabit.reduce(
          (acc: ChairOptions, categoryData: any) => {
            const { category, groups } = categoryData;

            acc[category] = Object.entries(groups).reduce(
              (groupAcc: SelectedGroup, [groupName, items]: [string, any]) => {
                groupAcc[groupName] = Array.isArray(items)
                  ? items.map((item: any) => ({
                      label: item,
                      checked: true,
                    }))
                  : [];
                return groupAcc;
              },
              {} as SelectedGroup,
            );

            return acc;
          },
          {} as ChairOptions,
        );

        setSelectedItems(initialSelectedItems);
      }

      if (workSetup.length > 0) {
        const initialAdjustableItems = workSetup.reduce(
          (acc: Record<string, boolean>, setup: any) => {
            const key = Object.keys(setup)[0];
            acc[key] = setup[key];
            return acc;
          },
          {},
        );

        setAdjustableItems(initialAdjustableItems);
      }
    }
  }, [existingCaseDetail]);

  const assessmentItems: AssessmentCategory[] =
    expertAssessQuestion?.data.map((category) => ({
      category: category.question,
      items: category.optionConfig.map((item) => ({
        label: item.optionText,
        imageSrc: AssessmentHipsImage,
        id: item.id,
        isLevelOne: item.isLevelOne,
        group: item.group || "Others",
        isAdjustable: item.optionCategory.isAdjustable, // Set isAdjustable flag
      })),
    })) || [];

  const handleRegularItemClick = (
    category: string,
    group: string,
    label: string,
  ) => {
    setSelectedItems((prevSelectedItems) => {
      const categoryGroups = prevSelectedItems[category] || {};
      let groupItems = categoryGroups[group] || [];

      const existingItemIndex = groupItems.findIndex(
        (item) => item.label === label,
      );

      if (existingItemIndex !== -1) {
        groupItems = groupItems.map((item, index) =>
          index === existingItemIndex
            ? { ...item, checked: !item.checked }
            : item,
        );
      } else {
        groupItems = [...groupItems, { label, checked: true }];
      }

      const updatedSelectedItems = {
        ...prevSelectedItems,
        [category]: {
          ...categoryGroups,
          [group]: groupItems,
        },
      };

      sendDataToBackend(updatedSelectedItems);

      return updatedSelectedItems;
    });
  };

  const handleAdjustableClick = (category: string, group: string) => {
    setAdjustableItems((prevAdjustableItems) => {
      const adjustableKey = `${category}-${group}-adjustable`;

      // Toggle the checked state for the specific adjustable item
      const isChecked = !prevAdjustableItems[adjustableKey];

      const updatedAdjustableItems = {
        ...prevAdjustableItems,
        [adjustableKey]: isChecked,
      };

      sendWorkSetupDataToBackend(updatedAdjustableItems);

      return updatedAdjustableItems;
    });
  };

  const sendWorkSetupDataToBackend = async (
    updatedWorkSetup: Record<string, boolean>,
  ) => {
    // console.log("Sending adjustable data to backend:", updatedWorkSetup);

    const existingDetailId =
      existingCaseDetail && existingCaseDetail.data.length > 0
        ? existingCaseDetail.data[0].id
        : null;

    const workSetupArray = Object.keys(updatedWorkSetup).map((key) => ({
      [key]: updatedWorkSetup[key],
    }));

    if (existingDetailId) {
      updateCaseDetails({
        resource: "case-details",
        id: existingDetailId,
        mutationMode: "optimistic",
        values: {
          workSetup: workSetupArray,
          case: id,
        },
      });
    } else {
      createCaseDetails({
        resource: "case-details",
        values: {
          workSetup: workSetupArray,
          case: id,
        },
      });
    }
  };

  const sendDataToBackend = async (updatedSelectedItems: ChairOptions) => {
    const formattedData = formatSelectedItemsForBackend(updatedSelectedItems);
    // console.log("Data to send to backend:", formattedData);

    const existingDetailId =
      existingCaseDetail && existingCaseDetail.data.length > 0
        ? existingCaseDetail.data[0].id
        : null;

    if (existingDetailId) {
      updateCaseDetails({
        resource: "case-details",
        id: existingDetailId,
        mutationMode: "optimistic",
        values: {
          workHabit: formattedData,
          case: id,
        },
      });
    } else {
      createCaseDetails({
        resource: "case-details",
        values: {
          workHabit: formattedData,
          case: id,
        },
      });
    }
  };

  const formatSelectedItemsForBackend = (selectedItems: ChairOptions) => {
    return Object.entries(selectedItems).map(([category, groups]) => ({
      category,
      groups: Object.entries(groups).reduce(
        (groupAcc, [group, items]) => {
          groupAcc[group] = items
            .filter((item) => item.checked)
            .map((item) => item.label);
          return groupAcc;
        },
        {} as Record<string, string[]>,
      ),
    }));
  };

  const handlePopoverOpen = (
    event: React.MouseEvent<HTMLElement>,
    category: string,
  ) => {
    const selectedCategory = expertAssessQuestion?.data.find(
      (cat) => cat.question === category,
    );

    if (selectedCategory) {
      const options: SelectedGroup = selectedCategory.optionConfig.reduce(
        (acc: SelectedGroup, item) => {
          const groupName = item.group || "Others";
          if (!acc[groupName]) {
            acc[groupName] = [];
          }
          acc[groupName].push({
            label: item.optionText,
            checked: false,
            isAdjustable: item.optionCategory.isAdjustable,
          });
          return acc;
        },
        {},
      );

      setPopoverOptions(options);
      setPopoverCategory(category);
    }

    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
    setPopoverOptions(null);
    setPopoverCategory(null);
  };

  const open = Boolean(anchorEl);

  return (
    <div>
      <Grid container spacing={5}>
        {assessmentItems.map((category, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <AssessmentCard
              key={category.category}
              category={category.category}
              items={category.items}
              selectedItems={selectedItems[category.category] || {}}
              handleRegularItemClick={(cat: string, grp: string, lbl: string) =>
                handleRegularItemClick(cat, grp, lbl)
              }
              onMoreItemsClick={(event) =>
                handlePopoverOpen(event, category.category)
              }
              handleAdjustableClick={handleAdjustableClick}
              adjustableItems={adjustableItems}
            />
          </Grid>
        ))}
      </Grid>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        PaperProps={{
          sx: {
            p: "30px",
            width: "89%",
            m: -2,
            borderRadius: "10px",
            border: "0.5px solid #929292",
            backgroundColor: "#FFF",
            boxShadow: "3px 4px 5px 0px rgba(0, 0, 0, 0.10)",
          },
        }}
      >
        {popoverOptions && popoverCategory && (
          <Box sx={{ position: "relative" }}>
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 3 }}>
              {popoverCategory} Options
            </Typography>
            <IconButton
              sx={{ position: "absolute", top: -5, right: 0 }}
              onClick={handlePopoverClose}
            >
              <CustomCloseIcon />
            </IconButton>
            <Grid container spacing={2}>
              {Object.entries(popoverOptions).map(([groupName, options]) => (
                <Grid item xs={12} md={3} key={groupName}>
                  <Typography
                    variant="subtitle1"
                    sx={{ fontWeight: 600, mb: 1, fontSize: "20px" }}
                  >
                    {groupName}
                  </Typography>
                  {options.map((option) => (
                    <Box
                      key={option.label}
                      sx={{ display: "flex", alignItems: "center", mb: 0.5 }}
                    >
                      <FormControlLabel
                        control={
                          <MLCheckbox
                            checked={
                              selectedItems[popoverCategory]?.[groupName]?.some(
                                (item) =>
                                  item.label === option.label && item.checked,
                              ) || false
                            }
                            onChange={() =>
                              handleRegularItemClick(
                                popoverCategory,
                                groupName,
                                option.label,
                              )
                            }
                            name={option.label}
                          />
                        }
                        label={option.label}
                      />
                    </Box>
                  ))}
                  {options.some((option) => option.isAdjustable) && (
                    <>
                      <Divider
                        sx={{
                          width: "50%",
                          borderBottomWidth: "1px",
                          borderColor: "black",
                        }}
                      />
                      <Box
                        key={`${groupName}-adjustable`}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          mt: 0.5,
                        }}
                      >
                        <FormControlLabel
                          control={
                            <MLCheckbox
                              checked={
                                adjustableItems[
                                  `${popoverCategory}-${groupName}-adjustable`
                                ] || false
                              }
                              onChange={() =>
                                handleAdjustableClick(
                                  popoverCategory || "",
                                  groupName,
                                )
                              }
                              name="Adjustable"
                            />
                          }
                          label="Adjustable"
                        />
                      </Box>
                    </>
                  )}
                </Grid>
              ))}
            </Grid>
          </Box>
        )}
      </Popover>
    </div>
  );
};

export default AssessmentGrid;
