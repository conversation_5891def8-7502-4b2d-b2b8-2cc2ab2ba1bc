import MLToggleButton from "../../components/ui/MLToggleButton/MLToggleButton";
import MLToggleButtonGroup from "../../components/ui/MLToggleButtonGroup/MLToggleButtonGroup";
import Loading from "../Loading/Loading";
import ExpertToggleButton from "./ExpertToggleButton";
import {
  Box,
  Popover,
  Typography,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import { useList } from "@refinedev/core";
import { useCreate, useUpdate } from "@refinedev/core";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

interface BodyPartState {
  severity: string | null;
  persistence: string | null;
}

const ExpertassessmentRsi = () => {
  const { id } = useParams();
  const [selectedButton, setSelectedButton] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null); // Declare anchorEl and setAnchorEl
  const [bodyPartsState, setBodyPartsState] = useState<{
    [key: string]: BodyPartState;
  }>({
    "Lower back": { severity: null, persistence: null },
    Neck: { severity: null, persistence: null },
    Shoulder: { severity: null, persistence: null },
  });
  const [severityOptions] = useState<string[]>(["Low", "Medium", "High"]);
  const [persistenceOptions] = useState<string[]>(["Low", "Medium", "High"]);
  const [selectedNonLevelOneOption, setSelectedNonLevelOneOption] =
    useState<string>("");
  const [customButtons, setCustomButtons] = useState<string[]>([]); // State to track custom selected buttons

  const { data: rsiExpertData, isLoading: rsiLoading } = useList({
    resource: "expert-assessment-option-configs",
    pagination: {
      pageSize: 100,
    },
    filters: [
      {
        field: "group",
        operator: "eq",
        value: "RSI",
      },
    ],
  });

  // console.log(bodyPartsState, "bodyPartsState");

  const { data: existingCaseDetail } = useList({
    resource: "case-details",
    filters: [
      {
        field: "case",
        operator: "eq",
        value: id,
      },
    ],
  });

  const { mutate: createCaseDetails } = useCreate();
  const { mutate: updateCaseDetails } = useUpdate();

  useEffect(() => {
    if (existingCaseDetail?.data && existingCaseDetail.data.length > 0) {
      const existingData = existingCaseDetail.data[0].discomfort;
      if (existingData && Array.isArray(existingData)) {
        const updatedState = { ...bodyPartsState };
        existingData.forEach((item) => {
          if (updatedState[item.bodyPart]) {
            updatedState[item.bodyPart].severity = item.severity;
            updatedState[item.bodyPart].persistence = item.persistence;
          } else {
            updatedState[item.bodyPart] = {
              severity: item.severity,
              persistence: item.persistence,
            };
            setCustomButtons((prev) => [...new Set([...prev, item.bodyPart])]);
          }
        });
        setBodyPartsState(updatedState);
      }
    }

    return () => {
      // Reset or clean up states here if needed
      setBodyPartsState({
        "Lower back": { severity: null, persistence: null },
        Neck: { severity: null, persistence: null },
        Shoulder: { severity: null, persistence: null },
      });
    };
  }, [existingCaseDetail?.data]);

  const buttonsToDisplay = rsiExpertData?.data?.filter((item) => {
    return item.group === "RSI" && item.isLevelOne;
  });

  const nonLevelOneOptions = rsiExpertData?.data?.filter((item) => {
    return (
      item.group === "RSI" &&
      !item.isLevelOne &&
      !customButtons.includes(item.optionText)
    );
  });

  const handleButtonClick = (
    buttonName: string,
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    if (selectedButton === buttonName) {
      setSelectedButton(null);
      setAnchorEl(null);
    } else {
      setSelectedButton(buttonName);
      setAnchorEl(event.currentTarget);
    }
  };

  const handleNonLevelOneOptionChange = (event: SelectChangeEvent<string>) => {
    const selectedOptionText = event.target.value as string;
    setSelectedNonLevelOneOption(selectedOptionText);

    setCustomButtons((prev) => [...prev, selectedOptionText]);
    setSelectedNonLevelOneOption("");
  };

  const handlePopoverChange = (
    type: "severity" | "persistence",
    value: string,
  ) => {
    if (selectedButton) {
      setBodyPartsState((prevState) => ({
        ...prevState,
        [selectedButton]: {
          ...prevState[selectedButton],
          [type]: value,
        },
      }));
    }
  };

  const handlePopoverClose = () => {
    if (selectedButton) {
      const { severity, persistence } = bodyPartsState[selectedButton];

      if (!severity && !persistence) {
        // If severity and persistence are unselected, remove the button from customButtons
        setCustomButtons((prev) =>
          prev.filter((button) => button !== selectedButton),
        );
      }

      // Trigger save when popover is closed regardless of whether severity and persistence are selected
      handleSave();
    }
    setAnchorEl(null);
  };

  const prepareDiscomfortData = () => {
    return Object.keys(bodyPartsState)
      .map((bodyPart) => {
        const { severity, persistence } = bodyPartsState[bodyPart];
        return severity || persistence
          ? { bodyPart, severity, persistence }
          : null;
      })
      .filter(Boolean);
  };

  const handleSave = () => {
    if (!id) {
      console.error(
        "Case ID is undefined. Cannot update or create case details.",
      );
      return;
    }

    const discomfortData = prepareDiscomfortData();

    if (existingCaseDetail?.data && existingCaseDetail.data.length > 0) {
      const existingDetailId = existingCaseDetail.data[0]?.id;

      if (existingDetailId) {
        updateCaseDetails({
          resource: "case-details",
          id: existingDetailId,
          mutationMode: "optimistic",
          values: {
            discomfort: discomfortData,
            case: id,
          },
        });
      }
    } else {
      createCaseDetails({
        resource: "case-details",
        values: {
          discomfort: discomfortData,
          case: id,
        },
      });
    }
  };

  // console.log(bodyPartsState, "bodyParts");
  return (
    <>
      <Box
        sx={{
          border: "1px solid #e0e0e0",
          borderRadius: "8px",
          padding: "28px",
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
          justifyContent: "center",
          width: "100%",
        }}
      >
        <Typography variant="h2" sx={{ marginBottom: "21px" }}>
          RSI
        </Typography>

        {rsiLoading ? (
          <Loading justifyContent="flex-start" />
        ) : (
          <Box
            sx={{
              display: "flex",
              gap: "12px",
              flexWrap: "wrap",
            }}
          >
            {buttonsToDisplay?.map((item) => (
              <ExpertToggleButton
                key={item.option}
                label={item.optionText}
                selectedButton={selectedButton}
                onClick={handleButtonClick}
                bodyPartState={
                  bodyPartsState[item.optionText] || {
                    severity: null,
                    persistence: null,
                  }
                }
              />
            ))}

            {customButtons.map((option) => (
              <ExpertToggleButton
                key={option}
                label={option}
                selectedButton={selectedButton}
                onClick={handleButtonClick}
                bodyPartState={
                  bodyPartsState[option] || {
                    severity: null,
                    persistence: null,
                  }
                }
              />
            ))}

            <Box sx={{ minWidth: 120 }}>
              <Select
                value={selectedNonLevelOneOption}
                onChange={handleNonLevelOneOptionChange}
                displayEmpty
                fullWidth
                sx={{
                  borderRadius: "8px",
                  "& .MuiSelect-select": {
                    padding: "15.5px 14px",
                  },
                }}
              >
                <MenuItem value="" disabled>
                  Add Discomfort
                </MenuItem>
                {nonLevelOneOptions?.map((option) => (
                  <MenuItem key={option.id} value={option.optionText}>
                    {option.optionText}
                  </MenuItem>
                ))}
              </Select>
            </Box>
          </Box>
        )}
      </Box>

      {selectedButton && (
        <Popover
          open={Boolean(anchorEl)}
          anchorEl={anchorEl}
          onClose={handlePopoverClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}
          sx={{
            "& .MuiPopover-paper": {
              borderRadius: "10px",
              marginTop: "8px",
              border: "0.5px solid #9C9C9C",
              padding: "30px",
              display: "flex",
              flexDirection: "column",
              boxShadow: "3px 4px 4.8px 0px rgba(0, 0, 0, 0.20)",
              gap: "20px",
            },
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: "8px" }}>
            <Typography variant="subtitle2" fontWeight={500}>
              Severity (S)
            </Typography>
            <MLToggleButtonGroup
              color="primary"
              exclusive
              value={
                selectedButton ? bodyPartsState[selectedButton]?.severity : null
              }
              onChange={(e, value) => handlePopoverChange("severity", value)}
              sx={{
                display: "flex",
                "& .MuiToggleButton-root": {
                  fontSize: "14px",
                  fontWeight: 400,
                },
                "& .Mui-selected": {
                  fontWeight: 500,
                },
              }}
            >
              {severityOptions.map((option) => (
                <MLToggleButton key={option} value={option}>
                  {option}
                </MLToggleButton>
              ))}
            </MLToggleButtonGroup>
          </Box>

          <Box sx={{ display: "flex", flexDirection: "column", gap: "8px" }}>
            <Typography variant="subtitle2" fontWeight={500}>
              Persistence (P)
            </Typography>
            <MLToggleButtonGroup
              color="primary"
              exclusive
              value={
                selectedButton
                  ? bodyPartsState[selectedButton]?.persistence
                  : null
              }
              onChange={(e, value) => handlePopoverChange("persistence", value)}
              sx={{
                display: "flex",
                "& .MuiToggleButton-root": {
                  fontSize: "14px",
                  fontWeight: 400,
                },
                "& .Mui-selected": {
                  fontWeight: 500,
                },
              }}
            >
              {persistenceOptions.map((option) => (
                <MLToggleButton key={option} value={option}>
                  {option}
                </MLToggleButton>
              ))}
            </MLToggleButtonGroup>
          </Box>
        </Popover>
      )}
    </>
  );
};

export default ExpertassessmentRsi;
