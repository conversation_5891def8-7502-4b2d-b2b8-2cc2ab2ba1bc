// Button to show body discomforts
import { Button } from "@mui/material";

// Body part state structure
interface BodyPartState {
  severity: string | null;
  persistence: string | null;
}

const ExpertToggleButton: React.FC<{
  label: string;
  selectedButton: string | null;
  onClick: (
    buttonName: string,
    event: React.MouseEvent<HTMLButtonElement>,
  ) => void;
  bodyPartState: BodyPartState;
}> = ({ label, selectedButton, onClick, bodyPartState }) => {
  const isSelected = selectedButton === label;
  const isPlusButton = label === "+";

  return (
    <Button
      variant="outlined"
      onClick={(event) => onClick(label, event)}
      sx={{
        // color: isSelected ? "#FFFFFF" : "#7856FF",
        color: isSelected
          ? "#FFFFFF"
          : bodyPartState.severity && bodyPartState.persistence
          ? "#000000" // Set text color to black if severity and persistence are present
          : "#7856FF",
        backgroundColor: isSelected
          ? "#7856FF"
          : bodyPartState.severity && bodyPartState.persistence
          ? "#E0E0FF" // Apply a light purple background color if severity and persistence are present
          : "transparent",
        border:
          "var(--Action-Primary-Stroke, 0.5px) solid var(--Action-Ghost-Text, #7856FF)",
        height: "52px",
        width: isPlusButton
          ? "49px"
          : bodyPartState.severity && bodyPartState.persistence
          ? "auto"
          : "144px",
        padding: isPlusButton ? "0 18px" : "8px 10px",
        fontSize: "16px",
        fontWeight: "400",
        borderRadius: "8px",
        textAlign: "left",
        textTransform: "capitalize",
        whiteSpace: "nowrap", // Ensure text doesn't wrap
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          whiteSpace: "nowrap", // Ensure text doesn't wrap within the div
        }}
      >
        <span
          style={{
            fontWeight:
              isSelected ||
              (bodyPartState.severity && bodyPartState.persistence)
                ? 600
                : 400,
            fontSize: "16px",
          }}
        >
          {label}
        </span>
        {!isPlusButton &&
          bodyPartState.severity &&
          bodyPartState.persistence && (
            <span
              style={{
                fontSize: "14px",
                lineHeight: "1.2",
                whiteSpace: "nowrap",
              }}
            >
              (S): {bodyPartState.severity} (P): {bodyPartState.persistence}
            </span>
          )}
      </div>
    </Button>
  );
};

export default ExpertToggleButton;
