import AssessmentHipsImage from "../../assets/expertAssessImg/Assessment_Hips.png";
import screenLogo from "../../assets/expertAssessImg/Screenheight.png";
import chairsLogo from "../../assets/expertAssessImg/chair.png";
import deskLogo from "../../assets/expertAssessImg/desk.png";
import mouseLogo from "../../assets/expertAssessImg/mousinghabits.png";
import keyboardLogo from "../../assets/expertAssessImg/typinghabits.png";
import MLCheckbox from "../../components/ui/MLCheckbox/MLCheckbox";
import CustomRadioButtonUnchecked from "./CustomRadioButtonUnchecked";
import { CheckCircle } from "@mui/icons-material";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Link,
  FormControlLabel,
} from "@mui/material";

interface AssessmentItem {
  label: string;
  imageSrc: string;
  id: string;
  isLevelOne: boolean;
  group: string;
  isAdjustable?: boolean; // Include the isAdjustable flag in AssessmentItem
}

interface SelectedItem {
  label: string;
  checked: boolean;
  isAdjustable?: boolean;
}

interface SelectedGroup {
  [group: string]: SelectedItem[];
}

type AssessmentCardProps = {
  category: string;
  items: AssessmentItem[];
  selectedItems: SelectedGroup;
  handleRegularItemClick: (
    category: string,
    group: string,
    label: string,
  ) => void;
  onMoreItemsClick: (event: React.MouseEvent<HTMLElement>) => void;
  handleAdjustableClick: (category: string, group: string) => void;
  adjustableItems: any;
};

type Category = "Chair" | "Desk" | "Screen" | "Mouse" | "Keyboard";

const categoryLogos: Record<Category, string> = {
  Chair: chairsLogo,
  Desk: deskLogo,
  Screen: screenLogo,
  Mouse: mouseLogo,
  Keyboard: keyboardLogo,
};

const AssessmentCard: React.FC<AssessmentCardProps> = ({
  category,
  items,
  selectedItems,
  handleRegularItemClick,
  onMoreItemsClick,
  handleAdjustableClick,
  adjustableItems,
}) => {
  // Helper function to check if the group has any adjustable items
  const hasAdjustableItem = (group: string) => {
    return items.some((item) => item.group === group && item.isAdjustable);
  };

  return (
    <Card variant="outlined" sx={{ padding: "16px", borderRadius: "8px" }}>
      <CardContent>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            paddingY: "30px",
            paddingX: "20px",
          }}
        >
          {category !== "Others" && (
            <img
              src={categoryLogos[category as Category] || AssessmentHipsImage}
              alt={`${category} logo`}
              style={{ marginRight: "10px" }}
              width="30"
              height="30"
            />
          )}
          <Typography variant="h2" component="div" sx={{ fontWeight: "bold" }}>
            {category}
          </Typography>
        </Box>
        {items
          .filter((item) => item.isLevelOne)
          .map((item, index) => {
            const isSelected = selectedItems[item.group]?.some(
              (selectedItem) =>
                selectedItem.label === item.label && selectedItem.checked,
            );

            return (
              <Box
                key={index}
                sx={{
                  position: "relative",
                  marginBottom: "7px",
                  backgroundColor: isSelected ? "#EDE8FF" : "transparent",
                  // borderRadius: "8px",
                  padding: "12px",
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <img
                    src={item.imageSrc}
                    style={{ marginRight: "16px" }}
                    width="70"
                    height={70}
                    alt={item.label}
                  />
                  <Typography variant="body1" sx={{ flexGrow: 1 }}>
                    {item.label}
                  </Typography>

                  <div
                    style={{
                      position: "absolute",
                      top: 35,
                      right: 21,
                      cursor: "pointer",
                    }}
                    onClick={() =>
                      handleRegularItemClick(category, item.group, item.label)
                    }
                  >
                    {isSelected ? (
                      <CheckCircle sx={{ color: "#31C100" }} />
                    ) : (
                      <CustomRadioButtonUnchecked />
                    )}
                  </div>
                </Box>

                {/* Render the Adjustable checkbox as a separate block below the label */}
                {isSelected && hasAdjustableItem(item.group) && (
                  <Box sx={{ marginTop: "8px" }}>
                    <FormControlLabel
                      control={
                        <MLCheckbox
                          checked={
                            adjustableItems[
                              `${category}-${item.group}-adjustable`
                            ] || false
                          }
                          onChange={() =>
                            handleAdjustableClick(category || "", item.group)
                          }
                          name="Adjustable"
                        />
                      }
                      label="Adjustable"
                    />
                  </Box>
                )}
              </Box>
            );
          })}
        {items.length > 1 && (
          <Link
            href="#"
            onClick={onMoreItemsClick}
            sx={{
              display: "block", // Make the link take up the full width
              paddingY: "10px", // Adjust the padding to ensure it matches the design
              marginTop: "16px", // Add some margin to separate it from the items above
              textDecoration: "none",
              color: "#7856FF",
              fontFamily: "work sans",
              fontSize: "14px",
              fontWeight: 500,
            }}
          >
            More items &#8599;
          </Link>
        )}
      </CardContent>
    </Card>
  );
};

export default AssessmentCard;
