import MLTypography from "../../components/ui/MLTypography/MLTypography";
import { Box, useTheme } from "@mui/material";

export default function MessageBox({
  found,
  errorMessage,
  width = "100%", // Default value for width
  ml = 0, // Default value for margin-left
}: {
  found: boolean;
  errorMessage: string;
  width?: any; // Making width optional
  ml?: any; // Making ml optional
}) {
  const theme = useTheme();
  return (
    <Box
      sx={{
        paddingX: "30px",
        width: width,
        paddingY: "16px",
        mb: "25px",
        ml: ml, // Applying the optional margin-left
        border: `1px solid ${
          found ? theme.palette.primary.main : theme.palette.error.main
        }`,
        backgroundColor: found ? "#f1eeff" : "#ffe0e0",
        borderRadius: "10px",
      }}
    >
      <MLTypography fontSize={15}>{errorMessage}</MLTypography>
    </Box>
  );
}
