import profileInnerCamera from "../../assets/icons/profileInnerCamera.png";
import MLTypography from "../../components/ui/MLTypography/MLTypography";
import { Card, IconButton, Avatar } from "@mui/material";
import React, { useState } from "react";

const AddPhotoCard = () => {
  const [photo, setPhoto] = useState<string | null>(null);

  const handlePhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => setPhoto(e.target?.result as string);
      reader.readAsDataURL(file);
    }
  };

  return (
    <Card
      sx={{
        width: 217,
        height: 218,
        position: "relative",
        padding: 2,
        boxShadow: "0px 2px 4px 0px rgba(0, 0, 0, 0.25)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: "10px",
        border: "0.5px solid var(--Forms-border-Complete, #333)",
        background: "#FFF",
      }}
    >
      <Avatar
        // src={photo}
        sx={{
          width: 122,
          height: 122,
          backgroundColor: "#F1F1F1",
          marginBottom: 2,
        }}
      />
      <IconButton
        color="error"
        component="label"
        sx={{
          position: "absolute",
          bottom: 70,
          right: 50,
          backgroundColor: "#FF6E6E",
          color: "#fff",
          height: "32px",
          width: "32px",
          "&:hover": { backgroundColor: "#FF6E6E" },
        }}
      >
        <img height={26} width={26} src={profileInnerCamera} />
        <input
          type="file"
          accept="image/*"
          hidden
          onChange={handlePhotoChange}
        />
      </IconButton>
      <MLTypography
        fontWeight={500}
        variant="body1"
        sx={{ textAlign: "center" }}
      >
        Add Photo
      </MLTypography>
    </Card>
  );
};

export default AddPhotoCard;
