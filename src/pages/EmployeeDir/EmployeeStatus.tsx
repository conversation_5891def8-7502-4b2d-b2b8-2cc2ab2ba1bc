import { Box, Stack, Typography, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/system";
import { useMemo } from "react";

interface EmployeeStatusProps {
  children: React.ReactNode;
  onFilterChange: (filter: string | null) => void;
  activeFilter: string | null;
  // Add a new prop for the full employee list
  allEmployees: {
    data: any[];
    total: number;
  };
}

const EmployeeStatus = ({
  children,
  onFilterChange,
  activeFilter,
  allEmployees
}: EmployeeStatusProps) => {
  const theme = useTheme();
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));

  // Use allEmployees for calculating counts instead of the filtered employees
  const employeeRiskLevelCounts = useMemo(() => {
    return allEmployees.data.reduce((acc: Record<string, number>, employee: { riskLevel?: "high" | "medium" | "low" }) => {
      const level = employee.riskLevel || 'none';
      acc[level] = (acc[level] || 0) + 1;
      return acc;
    }, {
      none: 0,
      high: 0,
      medium: 0,
      low: 0,
    });
  }, [allEmployees]);

  // Common styles for risk level boxes
  const getRiskBoxStyles = (riskLevel: string) => ({
    flex: "1 1 auto",
    backgroundColor: getRiskColor(riskLevel),
    color: riskLevel === "low" ? "black" : "#fff",
    padding: "15px",
    borderRadius: "10px",
    textAlign: "left" as const,
    cursor: "pointer",
    position: "relative" as const,
    transition: "transform 0.2s, box-shadow 0.2s",
    transform: activeFilter === riskLevel ? "scale(1.02)" : "scale(1)",
    boxShadow: activeFilter === riskLevel
      ? `0 4px 12px rgba(0,0,0,0.15)`
      : "none",
    "&:hover": {
      transform: "scale(1.03)",
      boxShadow: "0 6px 16px rgba(0,0,0,0.1)"
    },
    "&::after": activeFilter === riskLevel ? {
      content: '""',
      position: "absolute",
      bottom: "-10px",
      left: "50%",
      transform: "translateX(-50%)",
      width: "30%",
      height: "4px",
      backgroundColor: getRiskColor(riskLevel),
      borderRadius: "2px"
    } : {}
  });

  // Get color for risk level
  const getRiskColor = (riskLevel: string): string => {
    switch (riskLevel) {
      case "none": return "#6D4BF4";
      case "high": return "#FF6E6E";
      case "medium": return "#FF7A00";
      case "low": return "#DFFF32";
      case "null": return "#6D4BF4"; // this the bg color for total employee
      default: return "#6D4BF4";
    }
  };

  // Handle clicking on a risk level card
  const handleRiskLevelClick = (riskLevel: string) => {
    // Always set the filter to the clicked value, no toggling
    onFilterChange(riskLevel);
  };

  return (
    <Stack gap={{ xs: '15px', md: "30px" }}>
      {children}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: { sm: "start" },
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2,
          p: 0,
          m: 0
        }}
      >
        <Box
          onClick={() => handleRiskLevelClick("null")}
          sx={getRiskBoxStyles("null")}
        >
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            {allEmployees.total ?? 0}
          </Typography>
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            Total Employees
          </Typography>
        </Box>

        <Box
          onClick={() => handleRiskLevelClick("none")}
          sx={getRiskBoxStyles("none")}
        >
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            {employeeRiskLevelCounts.none ?? 0}
          </Typography>
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            Not completed
          </Typography>
        </Box>
        <Box
          onClick={() => handleRiskLevelClick("high")}
          sx={getRiskBoxStyles("high")}
        >
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            {employeeRiskLevelCounts.high ?? 0}
          </Typography>
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            High risk
          </Typography>
        </Box>
        <Box
          onClick={() => handleRiskLevelClick("medium")}
          sx={getRiskBoxStyles("medium")}
        >
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            {employeeRiskLevelCounts.medium ?? 0}
          </Typography>
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            Medium risk
          </Typography>
        </Box>
        <Box
          onClick={() => handleRiskLevelClick("low")}
          sx={getRiskBoxStyles("low")}
        >
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            {employeeRiskLevelCounts.low ?? 0}
          </Typography>
          <Typography variant={isTablet ? "h3" : "h2"} fontWeight={600}>
            Low risk
          </Typography>
        </Box>
      </Box>
    </Stack >
  );
};

export default EmployeeStatus;