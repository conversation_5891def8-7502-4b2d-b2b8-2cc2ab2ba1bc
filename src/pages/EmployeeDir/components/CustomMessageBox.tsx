import { Stack } from "@mui/material";

interface SystemInfoBoxProps {
    status?: "error" | "success" | "warning" | "info";
    backgroundColor?: string;
    borderColor?: string;
    children: React.ReactNode;
}

const CustomMessageBox = ({
    status = "info", // default to "info"
    backgroundColor,
    borderColor,
    children,
}: SystemInfoBoxProps) => {
    // Define default background and border colors
    const backgroundColors = {
        error: "#FFE0E0",
        success: "#E3F9C0",
        warning: "#FF6E6E",// need to fix
        info: "#F1EEFF",
    };

    const borderColors = {
        error: "#C40000",
        success: "#31C100",
        warning: "#FF6E6E", // need to fix
        info: "#7856FF",
    };

    return (
        <Stack
            padding="16px 30px"
            border={`1px solid ${borderColor || borderColors[status]}`}
            borderRadius="10px"
            sx={{
                backgroundColor: backgroundColor || backgroundColors[status]
            }}
        >
            {children}
        </Stack>
    );
};

export default CustomMessageBox
