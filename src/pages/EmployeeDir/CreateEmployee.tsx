import { isMobile } from "react-device-detect";
import notificationbell from "../../assets/icons/Notification.png";
import MLTypography from "../../components/ui/MLTypography/MLTypography";
import { desktop, tablet } from "../../responsiveStyles";
import AddPhotoCard from "./AddPhotoCard";
import EmployeeParticularsForm from "./EmployeeParticularsForm";
import { ErChevronleft } from "@mindlens/ergo-icons";
import { Box, IconButton, Stack } from "@mui/material";
import { useBack } from "@refinedev/core";
import { useParams } from "react-router-dom";

const CreateEmployee = () => {
  // Extracting the employee ID from the route parameters
  const { id: employeeId } = useParams();
  // Hook to navigate back to the previous page
  const back = useBack();
  return (
    <Stack
      direction="column"
      height="100%"
      sx={{
        backgroundImage: "url('/background_waves/horizontal1.svg')",
        backgroundSize: "cover",
        backgroundAttachment: "fixed",
        paddingX: {
          lg: desktop.contentContainer.paddingX,
          md: tablet.contentContainer.paddingX,
          xs: tablet.contentContainer.paddingX,
        },
        paddingY: {
          lg: desktop.contentContainer.paddingY,
          md: tablet.contentContainer.paddingY,
          xs: tablet.contentContainer.paddingY,
        },
      }}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Stack direction="row" alignItems="center" spacing={0.5}>
          <IconButton size="large" onClick={() => back()}>
            <ErChevronleft />
          </IconButton>
          {/* Displaying the title based on whether we are creating or editing an employee */}
          <MLTypography fontSize={{ xs: '24px', sm: '40px' }} marginLeft={{ xs: "47px", md: "0px" }} lineHeight="120%" variant="h1">
            {employeeId ? "Edit employee" : "Create employee"}
          </MLTypography>
        </Stack>
        <Stack direction="row" gap={1.5} alignItems="center">
          {/* <IconButton color="error">
            <img height={35} width={35} src={notificationbell} />
          </IconButton> */}
        </Stack>
      </Stack>

      <Stack mt={3}>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            p: isMobile ? 0 : 3,
            gap: 5,
          }}
        >
          {/* <AddPhotoCard /> */}
          {/* Form for entering employee particulars */}
          <EmployeeParticularsForm />
        </Box>
      </Stack>
    </Stack>
  );
};

export default CreateEmployee;
