import deleteIcon from "../../assets/icons/deleteIcon.svg";
import editIcon from "../../assets/icons/pencil.svg";
import MLButton from "../../components/ui/MLButton/MLButton";
import MLCard from "../../components/ui/MLCard/MLCard";
import MLTypography from "../../components/ui/MLTypography/MLTypography";
import { desktop, tablet } from "../../responsiveStyles";
import { capitalizeWords } from "../../utils/colorCodeUtils";
import { IIdentity } from "../AuthScreens/Profile/Profile";
import Loading from "../Loading/Loading";
import CustomMessageBox from "./components/CustomMessageBox";
import ImportEmployeeCsv from "./components/ImportEmployeeCsv";
import EmployeeStatus from "./EmployeeStatus";
import { Add } from "@mui/icons-material";
import {
  Box,
  CardContent,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Stack,
  TablePagination,
} from "@mui/material";
import { useGetIdentity, useList, useOne, useUpdate } from "@refinedev/core";
import { useTable } from "@refinedev/react-table";
import { ColumnDef, flexRender, RowData } from "@tanstack/react-table";
import dayjs from "dayjs";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { AscendingChevron, DefaultChevron, DescendingChevron } from "./EmployeeChevron";
import MLTextField from "../../components/ui/MLTextField/MLTextField";
import MLInputAdornment from "../../components/ui/MLInputAdornment/MLInputAdornment";
import { ErSearch } from "@mindlens/ergo-icons";

interface Employee {
  id: number;
  name: string;
  createdAt: string;
  assessmentDate?: string;
  jobTitle?: string;
  riskLevel?: string;
  contact: string;
  email: string;
  userID: {
    id: number;
    role: {
      name: string;
    };
  };
  organization: {
    name: string;
  };
  [key: string]: any;
}

interface RecordType {
  name: string;
  email: string;
  organization: string;
  level: string;
  deskno: string;
}

interface NotInsertedType {
  record: RecordType;
  error: string;
}

export interface ImportResponseState {
  success: Boolean;
  message: string;
  inserted: number;
  notInserted: number;
  details: {
    inserted: RecordType[];
    notInserted: NotInsertedType[];
  };
}

// Add type declaration for virtual columns
declare module '@tanstack/table-core' {
  interface ColumnMeta<TData extends RowData, TValue> {
    isVirtual?: boolean;
    filterOperator?: string;
  }
}

const Employee = () => {
  const { data: userIdentity } = useGetIdentity<IIdentity>();

  // Create a ref to track API requests and prevent duplicates
  const pendingRequestRef = useRef<AbortController | null>(null);
  const initialLoadCompleted = useRef<boolean>(false);

  const [open, setOpen] = useState(false);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<number | null>(null);
  const [openImportEmployeeCsvDialog, setOpenImportEmployeeCsvDialog] = useState(false);
  const [importResponse, setImportResponse] = useState<ImportResponseState | null>(null);
  const [showImportMessage, setShowImportMessage] = useState(false);
  const [showMoreErrors, setShowMoreErrors] = useState(false);
  const [activeRiskFilter, setActiveRiskFilter] = useState<string | null>("null");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debounceTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const { mutate: updateEmployee } = useUpdate();
  const { mutate: updateUser } = useUpdate();
  const navigate = useNavigate();

  // Get user details with increased staleTime to prevent frequent refetches
  const { data: userDetails, isLoading: isLoadingUserDetails } = useOne({
    resource: "users",
    id: userIdentity?.id!,
    meta: {
      populate: {
        organization: {
          fields: ['id', 'name']
        },
        vendorOrganisation: {
          fields: ['id']
        },
        vendorAssignment: {
          fields: ['id']
        }
      }
    },
    queryOptions: {
      staleTime: 30 * 60 * 1000, // Data stays fresh for 30 minutes (increased)
      cacheTime: 60 * 60 * 1000, // Cache kept for 60 minutes (increased)
      enabled: !!userIdentity?.id
    }
  });

  // Get vendor assignment data with better caching
  const { data: vendorAssignmentData, isLoading: isLoadingVendorAssignment } = useOne({
    resource: "vendor-assignments",
    id: userDetails?.data?.vendorAssignment?.id,
    meta: {
      populate: {
        clientOrganizations: {
          fields: ['id', 'name']
        }
      }
    },
    queryOptions: {
      enabled: !!userDetails?.data?.vendorAssignment?.id,
      staleTime: 30 * 60 * 1000, // Increased staleTime
      cacheTime: 60 * 60 * 1000  // Increased cacheTime
    }
  });

  // Add this vendor access check as a memoized value to prevent recalculation
  const hasVendorAccess = useMemo(() =>
    Boolean(userDetails?.data?.vendorOrganisation?.id && userDetails?.data?.vendorAssignment?.id),
    [userDetails?.data?.vendorOrganisation?.id, userDetails?.data?.vendorAssignment?.id]
  );

  // Get organization IDs based on user type - with more specific dependencies
  const organizationIds = useMemo(() => {
    if (hasVendorAccess && vendorAssignmentData?.data?.clientOrganizations) {
      // For vendor users, use client organization IDs from vendor assignment
      return vendorAssignmentData.data.clientOrganizations.map((org: any) => org.id);
    } else if (userDetails?.data?.organization?.id) {
      // For regular users, use their organization ID
      return [userDetails.data.organization.id];
    }
    return [];
  }, [
    hasVendorAccess,
    vendorAssignmentData?.data?.clientOrganizations,
    userDetails?.data?.organization?.id
  ]);

  // Get employee list for risk filters
  const { data: employeeList, isLoading: isEmployeeListData } = useList({
    resource: "employees",
    filters: [
      {
        field: 'organization.id',
        operator: 'eq',
        value: organizationIds.length > 0 ? organizationIds : undefined,
      },
      {
        field: 'archive',
        operator: 'eq',
        value: false,
      }
    ],
    meta: {
      fields: ["riskLevel"]
    },
    pagination: {
      mode: 'off',
    },
    queryOptions: {
      enabled: organizationIds.length > 0,
      staleTime: 5 * 60 * 1000, // 5 minute stale time for risk stats
      cacheTime: 10 * 60 * 1000,
    }
  });

  // Move columns definition outside component and memoize properly
  const columns = useMemo<ColumnDef<Employee>[]>(
    () => {
      const baseColumns: ColumnDef<Employee>[] = [
        {
          id: "name",
          header: "Name",
          meta: {
            isVirtual: false,
            filterOperator: "contains"
          },
          accessorFn: (row) => row.name,
          cell: ({ row }) => (
            <MLTypography fontSize={20} fontWeight={600}>
              <Link
                to={`/employees/show/${row.original.id}`}
                style={{ textDecoration: "none", color: "inherit" }}
              >
                {capitalizeWords(row.original.name)}
              </Link>
            </MLTypography>
          ),
        }
      ];

      if (hasVendorAccess) {
        baseColumns.push({
          id: "organization",
          header: "Company",
          meta: {
            filterOperator: "eq",
            isVirtual: false
          },
          accessorFn: (row: Employee) => row.organization?.name,
          cell: ({ getValue }) => (
            <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
              {getValue() as string || "N/A"}
            </MLTypography>
          ),
          enableSorting: true,
          sortingFn: "text",
        } as ColumnDef<Employee>);
      }

      const remainingColumns: ColumnDef<Employee>[] = [
        {
          id: "email",
          header: "Email address",
          meta: { isVirtual: false },
          accessorFn: (row) => row.email,
          cell: ({ getValue }) => (
            <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
              {getValue() as string}
            </MLTypography>
          ),
        },
        {
          id: "contact",
          header: "Contact number",
          meta: { isVirtual: false },
          accessorFn: (row) => row.contact,
          cell: ({ getValue }) => (
            <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
              {getValue() as string}
            </MLTypography>
          ),
        },
        {
          id: "role",
          header: "Role",
          meta: { isVirtual: false },
          accessorFn: (row) => {
            return row.userID?.role?.name || "N/A";
          },
          cell: ({ getValue }) => (
            <MLTypography>{getValue() as string}</MLTypography>
          ),
        },
        {
          id: "riskLevel",
          header: "Risk Level",
          meta: { isVirtual: false },
          accessorFn: (row) => row.riskLevel ? (row.riskLevel[0].toUpperCase() + row.riskLevel.slice(1)) : "Not completed",
          cell: ({ getValue }) => (
            <MLTypography fontWeight={400}>
              {getValue() as string}
            </MLTypography>
          ),
        },
        {
          id: "createdAt",
          header: "Date joined",
          meta: { isVirtual: false },
          accessorFn: (row) => row.createdAt,
          cell: ({ getValue }) => (
            <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
              {dayjs(getValue() as string).format("DD MMM YYYY")}
            </MLTypography>
          ),
        },
        {
          id: "assessmentDate",
          header: "Assessment date",
          meta: { isVirtual: false },
          accessorFn: (row) => row.assessmentDate || row.createdAt,
          cell: ({ getValue }) => (
            <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
              {dayjs(getValue() as string).format("DD MMM YYYY")}
            </MLTypography>
          ),
        },
        {
          id: "actions",
          header: "Actions",
          meta: { isVirtual: false },
          accessorFn: (row) => row,
          cell: ({ getValue }) => {
            const value = getValue() as Employee;
            return (
              <Stack direction="row">
                <MLButton
                  endIcon={<img src={editIcon} alt="edit" />}
                  sx={{
                    fontSize: "14px",
                    fontWeight: 500,
                  }}
                  size="small"
                  variant="outlined"
                  onClick={() => navigate(`/employees/edit/${value.id}`)}
                >
                  Edit
                </MLButton>
                <IconButton onClick={() => handleClickOpen(value.id)}>
                  <img src={deleteIcon} alt="delete" />
                </IconButton>
              </Stack>
            );
          },
        }
      ];

      return [...baseColumns, ...remainingColumns];
    },
    [hasVendorAccess, navigate]
  );

  // Modify the useTable implementation with optimized caching and improved filters
  const {
    refineCore: { tableQueryResult },
    getHeaderGroups,
    getRowModel,
    getState,
    setPageIndex,
    setPageSize,
  } = useTable({
    columns,
    refineCoreProps: {
      resource: "employees",
      syncWithLocation: false, // Changed to false to avoid unnecessary URL updates
      pagination: {
        mode: "server",
        pageSize: 10,
      },
      meta: {
        populate: {
          organization: true,
          userID: { populate: ["role"] },
        },
      },
      filters: {
        permanent: [
          {
            field: "organization.id",
            operator: "eq",
            value: organizationIds.length > 0 ? organizationIds : undefined,
          },
          {
            field: "archive",
            operator: "eq",
            value: false,
          },
          {
            field: "riskLevel",
            operator: activeRiskFilter === "none" ? "null" : "eq",
            value: activeRiskFilter === "null" ? null : activeRiskFilter,
          }
        ],
      },
      sorters: {
        mode: "server",
        initial: [{ field: "createdAt", order: "desc" }],
      },
      queryOptions: {
        staleTime: 5 * 60 * 1000, // 5 minutes - increased from 30 seconds
        cacheTime: 30 * 60 * 1000, // 30 minutes - increased from 5 minutes
      },
    },
  });

  // Controlled fetch function with abort controller to prevent duplicate requests
  const fetchEmployeeData = useCallback(() => {
    // Abort any pending request
    if (pendingRequestRef.current) {
      pendingRequestRef.current.abort();
    }

    // Create new abort controller
    const controller = new AbortController();
    pendingRequestRef.current = controller;

    // Execute fetch without passing the signal (since it's not supported in the type)
    // Instead, we'll use the controller to abort the request if needed
    tableQueryResult.refetch()
      .catch(error => {
        // Check if this is an abort error, which can be safely ignored
        if (error.name !== 'AbortError') {
          console.error('Error fetching employee data:', error);
        }
      })
      .finally(() => {
        // Only clear the reference if it's still the same controller
        if (pendingRequestRef.current === controller) {
          pendingRequestRef.current = null;
        }
      });
  }, [tableQueryResult]);

  // Simplify loading state
  const tableIsLoading = tableQueryResult.isFetching ||
    tableQueryResult.isLoading ||
    isLoadingUserDetails ||
    (hasVendorAccess && isLoadingVendorAssignment);

  // Optimize data fetching with a single useEffect and stricter conditions
  useEffect(() => {
    // Only fetch if we have valid organization IDs and haven't already loaded
    const shouldFetch =
      organizationIds.length > 0 &&
      !initialLoadCompleted.current &&
      !tableIsLoading;

    if (shouldFetch) {
      fetchEmployeeData();
      initialLoadCompleted.current = true;
    }
  }, [
    organizationIds,
    fetchEmployeeData,
    tableIsLoading
  ]);

  // Update search handling with improved debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);

    // Clear any existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Set a new timer with longer debounce delay
    debounceTimerRef.current = setTimeout(() => {
      const nameColumn = getHeaderGroups()[0]?.headers.find(
        (header) => header.id === "name"
      )?.column;

      nameColumn?.setFilterValue(value);
      setPageIndex(0);
      fetchEmployeeData(); // Use the controlled fetch function
    }, 800); // Increased from 500ms to 800ms
  };

  // Clear timeout on component unmount
  useEffect(() => {
    return () => {
      // Clean up timers and pending requests
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      if (pendingRequestRef.current) {
        pendingRequestRef.current.abort();
      }
    };
  }, []);

  const handleClickOpen = (id: number) => {
    setSelectedEmployeeId(id);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedEmployeeId(null);
  };

  const handleArchive = () => {
    if (selectedEmployeeId) {
      const employee = tableQueryResult.data?.data.find(
        (emp) => emp.id === selectedEmployeeId
      );

      try {
        updateEmployee({
          resource: "employees",
          id: selectedEmployeeId,
          values: { archive: true },
          mutationMode: "pessimistic",
        });

        if (employee?.userID?.id) {
          updateUser({
            resource: "users",
            id: employee.userID.id!,
            values: { archive: true },
            mutationMode: "pessimistic",
            successNotification: false,
          });
        }

        handleClose();
      } catch (error) {
        // console.error('Archive operation failed:', error);
      }
    }
  };

  const handleRiskFilterChange = useCallback((filter: string | null) => {
    setActiveRiskFilter(filter);
    // Reset pagination to first page when filter changes
    setPageIndex(0);
    // Use the controlled fetch function
    fetchEmployeeData();
  }, [setPageIndex, fetchEmployeeData]);

  const handleImportResponse = (response: ImportResponseState) => {
    setImportResponse(response);
    setShowImportMessage(true);
    setOpenImportEmployeeCsvDialog(false);
    fetchEmployeeData(); // Use controlled fetch
    setShowMoreErrors(false);
  };

  const downloadCSV = (data: NotInsertedType[]) => {
    if (data.length === 0) return; // Prevent download if there are no data
    // Extract headers from the first record
    const headers = Object.keys(data[0].record);

    // Generate CSV rows from record data only
    const csvRows = data.map(error => {
      // Extract values for the record
      return Object.values(error.record).join(',');
    });

    // Combine headers and rows
    const csvContent = [headers.join(','), ...csvRows].join('\n');

    // Create a Blob from the CSV string
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // Create a link to download the Blob
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `notinserted.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const renderMessageBaseOnResponseFromServer = () => {
    if (!importResponse) return null;
    const { success, inserted, notInserted, details } = importResponse;

    if (details.inserted.length > 0 && details.notInserted.length === 0) {
      return (
        <Stack width="70%">
          <CustomMessageBox status="success">
            <Stack gap="16px">
              <MLTypography
                sx={{
                  fontSize: "16px",
                  fontWeight: 600,
                }}
              >
                Users imported successfully
              </MLTypography>
              <Box>
                <MLTypography>
                  Number of users updated : {inserted}
                </MLTypography>
                <MLTypography>
                  Last updated: {dayjs().format("DD/MM/YYYY, HH:mm:ss")}
                </MLTypography>
              </Box>
            </Stack>
          </CustomMessageBox>
        </Stack>
      );
    }

    // some inserted and some failed
    if (details.inserted.length > 0 && details.notInserted.length > 0) {
      return (
        <Stack width="70%">
          <CustomMessageBox status="warning">
            <Stack gap="16px">
              <Box>
                <MLTypography
                  sx={{
                    fontSize: "16px",
                    fontWeight: 600,
                  }}
                >
                  {inserted} employees insert failed
                </MLTypography>
                <MLTypography
                  sx={{
                    fontSize: "16px",
                    fontWeight: 600,
                  }}
                >
                  {notInserted} employees updated successfull
                </MLTypography>
              </Box>
              <Box>
                {details.notInserted.slice(0, 3).map((item, index) => (
                  <MLTypography key={index}>
                    {item.error === "This attribute must be unique"
                      ? `The email address "${item.record.email}" is already registered. Please use a different email.`
                      : item.error}
                  </MLTypography>
                ))}
                {details.notInserted.length > 3 && (
                  <Collapse in={showMoreErrors}>
                    {details.notInserted.slice(3).map((item, index) => (
                      index <= 4 && <MLTypography key={index + 3}>
                        {item.error === "This attribute must be unique"
                          ? `The email address "${item.record.email}" is already registered. Please use a different email.`
                          : item.error}
                      </MLTypography>
                    ))}
                  </Collapse>
                )}
                {details.notInserted.length > 3 && (
                  <MLButton
                    variant="text"
                    onClick={() => setShowMoreErrors((prev) => !prev)}
                    style={{ marginTop: 8 }}
                  >
                    {showMoreErrors ? "Show less" : `Show more (${details.notInserted.length - 3} more)`}
                  </MLButton>
                )}
                {details.notInserted.length > 8 && (
                  <MLButton variant="text" style={{ marginTop: 8 }} onClick={() => downloadCSV(details.notInserted)}>
                    Download Not Inserted
                  </MLButton>
                )}
              </Box>
            </Stack>
          </CustomMessageBox>
        </Stack>
      );
    }

    // nothing is inserted
    if (details.inserted.length === 0) {
      return (
        <Stack width="70%">
          <CustomMessageBox status="error">
            <Stack gap="16px">
              <MLTypography
                sx={{
                  fontSize: "16px",
                  fontWeight: 600,
                }}
              >
                Users import failed
              </MLTypography>
              <Box>
                {details.notInserted.slice(0, 3).map((item, index) => (
                  <MLTypography key={index}>
                    {item.error === "This attribute must be unique"
                      ? `The email address "${item.record.email}" is already registered. Please use a different email.`
                      : item.error}
                  </MLTypography>
                ))}
                {details.notInserted.length > 3 && (
                  <Collapse in={showMoreErrors}>
                    {details.notInserted.slice(3).map((item, index) => (
                      index <= 4 && <MLTypography key={index + 3}>
                        {item.error === "This attribute must be unique"
                          ? `The email address "${item.record.email}" is already registered. Please use a different email.`
                          : item.error}
                      </MLTypography>
                    ))}
                  </Collapse>
                )}
                {details.notInserted.length > 3 && (
                  <MLButton
                    variant="text"
                    onClick={() => setShowMoreErrors((prev) => !prev)}
                    style={{ marginTop: 8 }}
                  >
                    {showMoreErrors ? "Show less" : `Show more (${details.notInserted.length - 3} more)`}
                  </MLButton>
                )}
                {details.notInserted.length > 8 && (
                  <MLButton variant="text" style={{ marginTop: 8 }} onClick={() => downloadCSV(details.notInserted)}>
                    Download Not Inserted
                  </MLButton>
                )}
              </Box>
            </Stack>
          </CustomMessageBox>
        </Stack>
      );
    }

    return null;
  };

  return (
    <Stack
      gap="30px"
      sx={{
        backgroundImage: "url('/background_waves/horizontal1.svg')",
        backgroundSize: "cover",
        backgroundAttachment: "fixed",
        paddingX: {
          lg: desktop.contentContainer.paddingX,
          md: tablet.contentContainer.paddingX,
          xs: tablet.contentContainer.paddingX,
        },
        paddingY: {
          lg: desktop.contentContainer.paddingY,
          md: tablet.contentContainer.paddingY,
          xs: tablet.contentContainer.paddingY,
        },
      }}
    >
      <Stack direction={"row"} justifyContent={"space-between"}>
        <MLTypography fontSize={{ xs: '24px', sm: '40px' }} marginLeft={{ xs: "47px", md: "0px" }} lineHeight="120%" variant="h1">
          Employee directory
        </MLTypography>
        <Stack direction="row" gap={1.5} alignItems={"center"}>
          <MLTextField
            label="Search for employee name"
            id="employee-filter-search-bar"
            sx={{ borderRadius: "10px", minWidth: 300 }}
            value={searchQuery}
            size="small"
            InputProps={{
              endAdornment: (
                <MLInputAdornment position="end">
                  <IconButton>
                    <ErSearch />
                  </IconButton>
                </MLInputAdornment>
              ),
            }}
            onChange={handleSearchChange}
          />
          <MLButton
            variant="outlined"
            onClick={() => setOpenImportEmployeeCsvDialog(true)}
          >
            Import
          </MLButton>
          <Link to="/employees/create">
            <MLButton endIcon={<Add />} variant="contained" color="secondary">
              Create
            </MLButton>
          </Link>
        </Stack>
      </Stack>
      <EmployeeStatus
        allEmployees={employeeList ?? { data: [], total: 0 }}
        onFilterChange={handleRiskFilterChange}
        activeFilter={activeRiskFilter}
      >
        {showImportMessage && renderMessageBaseOnResponseFromServer()}
      </EmployeeStatus>

      {/* Message for high risk employees */}
      {activeRiskFilter === "high" &&
        <CustomMessageBox status="error">
          <Stack gap="16px">
            <MLTypography
              sx={{
                fontSize: "16px",
                fontWeight: 600,
              }}
            >
              These employees are flagged as high risk. Consider scheduling an expert assessment
            </MLTypography>
          </Stack>
        </CustomMessageBox>
      }

      {tableIsLoading ? (
        <Loading />
      ) : (
        <Stack gap={2}>
          {getHeaderGroups().map((headerGroup) => (
            <React.Fragment key={headerGroup.id}>
              <Stack display={{ xs: "none", md: "flex" }}>
                <Box sx={{
                  display: "grid",
                  gridTemplateColumns: "1.5fr 1.5fr 1fr 0.9fr 0.9fr 1fr 1fr 1.2fr", // Match the data row columns
                  columnGap: "0.5rem",
                  padding: "0 1rem"
                }}>
                  {headerGroup.headers.map((header) => (
                    <Box
                      key={header.id}
                      onClick={header.id === "actions" ? undefined : header.column.getToggleSortingHandler()}
                      sx={{
                        cursor: header.id === "actions" ? "default" : "pointer",
                        textAlign: "left"
                      }}
                    >
                      <Stack direction="row" alignItems="center" gap={0.5}>
                        {header.id !== "actions" && (
                          <>{header.column.getIsSorted() === "asc" ? <AscendingChevron /> :
                            header.column.getIsSorted() === "desc" ? <DescendingChevron /> :
                              <DefaultChevron />}</>
                        )}
                        <MLTypography fontSize={14} fontWeight="medium">
                          {flexRender(header.column.columnDef.header, header.getContext())}
                        </MLTypography>
                      </Stack>
                    </Box>
                  ))}
                </Box>
              </Stack>
            </React.Fragment>
          ))}

          {/* Table Data */}
          <Stack direction="column" gap={{ xs: "15px", md: "20px" }}>
            {getRowModel().rows.length > 0 ? (
              getRowModel().rows.map((row) => (
                <Stack key={row.id}>
                  {/* Mobile Card View */}
                  <Stack display={{ xs: "flex", md: "none" }}>
                    <MLCard sx={{ padding: { xs: "20px", md: "24px" } }}>
                      <Stack gap="8px">
                        <Stack direction="row" justifyContent="space-between" alignItems={'center'}>
                          <MLTypography
                            variant="body1"
                            fontSize={{ xs: '17px', sm: "20px" }}
                            width={{ xs: '60px' }}
                            fontWeight={600}
                            lineHeight={1}
                          >
                            <Link
                              to={`/employees/show/${row.original.id}`}
                              style={{ textDecoration: "none", color: "inherit" }}
                            >
                              {capitalizeWords(row.getValue('name'))}
                            </Link>
                          </MLTypography>
                          <MLTypography
                            variant="body1"
                            fontSize="12.5px"
                            fontWeight={400}
                          >
                            Date joined: {dayjs(row.getValue('createdAt')).format("DD-MM-YYYY")}
                          </MLTypography>
                        </Stack>

                        <Stack direction="row">
                          <MLTypography
                            variant="body1"
                            fontSize="14px"
                            fontWeight={400}
                          >
                            {row.getValue('email')}
                          </MLTypography>
                        </Stack>

                        <Stack direction="row">
                          <MLTypography
                            variant="body1"
                            fontSize="14px"
                            fontWeight={400}
                          >
                            {row.getValue('role')}
                          </MLTypography>
                        </Stack>

                        <Stack direction="row">
                          <Box>
                            <MLTypography
                              variant="body1"
                              fontSize="14px"
                              fontWeight={400}
                            >
                              Assessment Date: {dayjs(row.getValue('assessmentDate')).format("DD MMM YYYY")}
                            </MLTypography>
                            <MLTypography
                              variant="body1"
                              fontSize="14px"
                              fontWeight={400}
                            >
                              Risk Level: {row.getValue('riskLevel')}
                            </MLTypography>
                            <MLTypography
                              variant="body1"
                              fontSize="14px"
                              fontWeight={400}
                            >
                              Contact no: {row.getValue('contact')}
                            </MLTypography>
                          </Box>
                        </Stack>

                        <Stack sx={{
                          justifyContent: 'space-between'
                        }}>
                          <Stack direction="row" spacing={1.5}>
                            <MLButton
                              endIcon={<img src={editIcon} alt="edit" />}
                              sx={{
                                fontSize: "14px",
                                fontWeight: 500,
                              }}
                              size="small"
                              variant="outlined"
                              onClick={() => {
                                const value = row.getValue('actions') as Employee;
                                return navigate(`/employees/edit/${value.id}`);
                              }}
                            >
                              Edit
                            </MLButton>
                            <IconButton
                              onClick={() => {
                                const value = row.getValue('actions') as Employee;
                                return handleClickOpen(value.id);
                              }}
                            >
                              <img src={deleteIcon} alt="delete" />
                            </IconButton>
                          </Stack>
                        </Stack>
                      </Stack>
                    </MLCard>
                  </Stack>

                  {/* Desktop screen view */}
                  <Stack display={{ xs: "none", md: "flex" }}>
                    <MLCard key={row.id}>
                      <Box sx={{
                        display: "grid",
                        gridTemplateColumns: "1.5fr 1.5fr 1fr 0.9fr 0.9fr 1fr 1fr 1.2fr",
                        columnGap: "0.5rem",
                        alignItems: "start", // Align items to the top for better text wrapping
                        padding: "0.5rem" // Minimal padding
                      }}>
                        {row.getVisibleCells().map((cell) => (
                          <Box
                            key={cell.id}
                            sx={{
                              overflow: "visible", // Allow content to flow
                              wordBreak: cell.column.id === "email" ? "break-all" : "normal", // Only break emails
                              maxWidth: "100%"  // Constrain width
                            }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </Box>
                        ))}
                      </Box>
                    </MLCard>
                  </Stack>
                </Stack>
              ))
            ) : (
              <MLCard>
                <CardContent>
                  <Stack
                    py={2}
                    direction="column"
                    gap={2}
                    alignItems={"center"}
                  >
                    <MLTypography variant="h3">
                      No employees found!
                    </MLTypography>
                    <MLTypography>
                      Consider trying different filter criteria
                    </MLTypography>
                    <MLButton onClick={() => {
                      setActiveRiskFilter("null");
                      setSearchQuery("");
                      const nameColumn = getHeaderGroups()[0]?.headers.find(
                        (header) => header.id === "name"
                      )?.column;
                      nameColumn?.setFilterValue("");
                      fetchEmployeeData();
                    }}>
                      Clear filters
                    </MLButton>
                  </Stack>
                </CardContent>
              </MLCard>
            )}

            <TablePagination
              component="div"
              count={tableQueryResult.data?.total ?? 0}
              page={getState().pagination.pageIndex}
              onPageChange={(e, newPage) => {
                setPageIndex(newPage);
                fetchEmployeeData();
              }}
              rowsPerPage={getState().pagination.pageSize}
              onRowsPerPageChange={(e) => {
                setPageSize(parseInt(e.target.value));
                setPageIndex(0);
                fetchEmployeeData();
              }}
            />
          </Stack>
        </Stack>
      )}

      {/* Dialog for deleting employee */}
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle fontSize={26}>{"Delete Employee"}</DialogTitle>
        <DialogContent>
          <DialogContentText
            fontSize={16}
            fontFamily={"Work Sans"}
            color={"rgba(0, 0, 0, 0.87);"}
            fontWeight={500}
          >
            Are you sure you want to delete this employee?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <MLButton
            sx={{
              mx: 0.5,
            }}
            onClick={handleArchive}
            size="medium"
            variant="contained"
            color="secondary"
          >
            Delete
          </MLButton>
          <MLButton onClick={handleClose} color="primary">
            Cancel
          </MLButton>
        </DialogActions>
      </Dialog>

      {/* Dialog for importing employee CSV */}
      <Dialog
        open={openImportEmployeeCsvDialog}
        onClose={() => setOpenImportEmployeeCsvDialog(false)}
        fullWidth
      >
        <DialogContent>
          <ImportEmployeeCsv
            handleCloseImportEmployeeCsvDialog={() => setOpenImportEmployeeCsvDialog(false)}
            handleImportResponse={handleImportResponse}
            organizationId={userDetails?.data.organization.id!}
          />
        </DialogContent>
      </Dialog>
    </Stack>
  );
};

export default Employee;

// import deleteIcon from "../../assets/icons/deleteIcon.svg";
// import editIcon from "../../assets/icons/pencil.svg";
// import MLButton from "../../components/ui/MLButton/MLButton";
// import MLCard from "../../components/ui/MLCard/MLCard";
// import MLTypography from "../../components/ui/MLTypography/MLTypography";
// import { desktop, tablet } from "../../responsiveStyles";
// import { capitalizeWords } from "../../utils/colorCodeUtils";
// import { IIdentity } from "../AuthScreens/Profile/Profile";
// import Loading from "../Loading/Loading";
// import CustomMessageBox from "./components/CustomMessageBox";
// import ImportEmployeeCsv from "./components/ImportEmployeeCsv";
// import EmployeeStatus from "./EmployeeStatus";
// import { Add } from "@mui/icons-material";
// import {
//   Box,
//   CardContent,
//   Collapse,
//   Dialog,
//   DialogActions,
//   DialogContent,
//   DialogContentText,
//   DialogTitle,
//   IconButton,
//   Stack,
//   TablePagination,
// } from "@mui/material";
// import { useGetIdentity, useList, useOne, useUpdate } from "@refinedev/core";
// import { useTable } from "@refinedev/react-table";
// import { ColumnDef, flexRender, RowData } from "@tanstack/react-table";
// import dayjs from "dayjs";
// import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
// import { Link, useNavigate } from "react-router-dom";
// import { AscendingChevron, DefaultChevron, DescendingChevron } from "./EmployeeChevron";
// import MLTextField from "../../components/ui/MLTextField/MLTextField";
// import MLInputAdornment from "../../components/ui/MLInputAdornment/MLInputAdornment";
// import { ErSearch } from "@mindlens/ergo-icons";

// interface Employee {
//   id: number;
//   name: string;
//   createdAt: string;
//   assessmentDate?: string;
//   jobTitle?: string;
//   riskLevel?: string;
//   contact: string;
//   email: string;
//   userID: {
//     id: number;
//     role: {
//       name: string;
//     };
//   };
//   organization: {
//     name: string;
//   };
//   [key: string]: any;
// }
// interface RecordType {
//   name: string;
//   email: string;
//   organization: string;
//   level: string;
//   deskno: string;
// }

// interface NotInsertedType {
//   record: RecordType;
//   error: string;
// }

// export interface ImportResponseState {
//   success: Boolean;
//   message: string;
//   inserted: number;
//   notInserted: number;
//   details: {
//     inserted: RecordType[];
//     notInserted: NotInsertedType[];
//   };
// }

// // Add type declaration for virtual columns
// declare module '@tanstack/table-core' {
//   interface ColumnMeta<TData extends RowData, TValue> {
//     isVirtual?: boolean;
//     filterOperator?: string;
//   }
// }

// const Employee = () => {
//   const { data: userIdentity } = useGetIdentity<IIdentity>();

//   const [open, setOpen] = useState(false);
//   const [selectedEmployeeId, setSelectedEmployeeId] = useState<number | null>(null);
//   const [openImportEmployeeCsvDialog, setOpenImportEmployeeCsvDialog] = useState(false);
//   const [importResponse, setImportResponse] = useState<ImportResponseState | null>(null);
//   const [showImportMessage, setShowImportMessage] = useState(false);
//   const [showMoreErrors, setShowMoreErrors] = useState(false);
//   const [activeRiskFilter, setActiveRiskFilter] = useState<string | null>("null");
//   const [searchQuery, setSearchQuery] = useState<string>("");
//   const debounceTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

//   const { mutate: updateEmployee } = useUpdate();
//   const { mutate: updateUser } = useUpdate();
//   const navigate = useNavigate();

//   const { data: userDetails, isLoading: isLoadingUserDetails } = useOne({
//     resource: "users",
//     id: userIdentity?.id!,
//     meta: {
//       populate: {
//         organization: {
//           fields: ['id', 'name']
//         },
//         vendorOrganisation: {
//           fields: ['id']
//         },
//         vendorAssignment: {
//           fields: ['id']
//         }
//       }
//     },
//     queryOptions: {
//       staleTime: 5 * 60 * 1000, // Data stays fresh for 5 minutes
//       cacheTime: 30 * 60 * 1000 // Cache kept for 30 minutes
//     }
//   });

//   const { data: vendorAssignmentData, isLoading: isLoadingVendorAssignment } = useOne({
//     resource: "vendor-assignments",
//     id: userDetails?.data?.vendorAssignment?.id,
//     meta: {
//       populate: {
//         clientOrganizations: {
//           fields: ['id', 'name']
//         }
//       }
//     },
//     queryOptions: {
//       enabled: !!userDetails?.data?.vendorAssignment?.id,
//       staleTime: 5 * 60 * 1000,
//       cacheTime: 30 * 60 * 1000
//     }
//   });


//   // Add this vendor access check
//   const hasVendorAccess = Boolean(userDetails?.data?.vendorOrganisation?.id && userDetails?.data?.vendorAssignment?.id);

//   // Get client organization IDs if user has vendor access

//   // Get organization IDs based on user type
//   const organizationIds = useMemo(() => {
//     if (hasVendorAccess && vendorAssignmentData?.data?.clientOrganizations) {
//       // For vendor users, use client organization IDs from vendor assignment
//       return vendorAssignmentData.data.clientOrganizations.map((org: any) => org.id);
//     } else if (userDetails?.data?.organization?.id) {
//       // For regular users, use their organization ID
//       return [userDetails.data.organization.id];
//     }
//     return [];
//   }, [hasVendorAccess, vendorAssignmentData?.data?.clientOrganizations, userDetails?.data?.organization?.id]);

//   const { data: employeeList, isLoading: isEmployeeListData } = useList({
//     resource: "employees",
//     filters: [
//       {
//         field: 'organization.id',
//         operator: 'eq',
//         value: organizationIds,
//       },
//       {
//         field: 'archive',
//         operator: 'eq',
//         value: false,
//       }
//     ],
//     meta: {
//       fields: ["riskLevel"]
//     },
//     pagination: {
//       mode: 'off',
//     },
//   });

//   // Move columns definition outside component and memoize properly
//   const columns = useMemo<ColumnDef<Employee>[]>(
//     () => {
//       const baseColumns: ColumnDef<Employee>[] = [
//         {
//           id: "name",
//           header: "Name",
//           meta: {
//             isVirtual: false,
//             filterOperator: "contains"
//           },
//           accessorFn: (row) => row.name,
//           cell: ({ row }) => (
//             <MLTypography fontSize={20} fontWeight={600}>
//               <Link
//                 to={`/employees/show/${row.original.id}`}
//                 style={{ textDecoration: "none", color: "inherit" }}
//               >
//                 {capitalizeWords(row.original.name)}
//               </Link>
//             </MLTypography>
//           ),
//         }
//       ];

//       if (hasVendorAccess) {
//         baseColumns.push({
//           id: "organization",
//           header: "Company",
//           meta: {
//             filterOperator: "eq",
//             isVirtual: false
//           },
//           accessorFn: (row: Employee) => row.organization?.name,
//           cell: ({ getValue }) => (
//             <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
//               {getValue() as string || "N/A"}
//             </MLTypography>
//           ),
//           enableSorting: true,
//           sortingFn: "text",
//         } as ColumnDef<Employee>);
//       }

//       const remainingColumns: ColumnDef<Employee>[] = [
//         {
//           id: "email",
//           header: "Email address",
//           meta: { isVirtual: false },
//           accessorFn: (row) => row.email,
//           cell: ({ getValue }) => (
//             <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
//               {getValue() as string}
//             </MLTypography>
//           ),
//         },
//         {
//           id: "contact",
//           header: "Contact number",
//           meta: { isVirtual: false },
//           accessorFn: (row) => row.contact,
//           cell: ({ getValue }) => (
//             <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
//               {getValue() as string}
//             </MLTypography>
//           ),
//         },
//         {
//           id: "role",
//           header: "Role",
//           meta: { isVirtual: false },
//           accessorFn: (row) => {
//             return row.userID?.role?.name || "N/A";
//           },
//           cell: ({ getValue }) => (
//             <MLTypography>{getValue() as string}</MLTypography>
//           ),
//         },
//         {
//           id: "riskLevel",
//           header: "Risk Level",
//           meta: { isVirtual: false },
//           accessorFn: (row) => row.riskLevel ? (row.riskLevel[0].toUpperCase() + row.riskLevel.slice(1)) : "Not completed",
//           cell: ({ getValue }) => (
//             <MLTypography fontSize={16} fontWeight={400} lineHeight={"205%"}>
//               {getValue() as string}
//             </MLTypography>
//           ),
//         },
//         {
//           id: "createdAt",
//           header: "Date joined",
//           meta: { isVirtual: false },
//           accessorFn: (row) => row.createdAt,
//           cell: ({ getValue }) => (
//             <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
//               {dayjs(getValue() as string).format("DD MMM YYYY")}
//             </MLTypography>
//           ),
//         },
//         {
//           id: "assessmentDate",
//           header: "Assessment date",
//           meta: { isVirtual: false },
//           accessorFn: (row) => row.assessmentDate || row.createdAt,
//           cell: ({ getValue }) => (
//             <MLTypography fontSize={14} fontWeight={400} lineHeight={"205%"}>
//               {dayjs(getValue() as string).format("DD MMM YYYY")}
//             </MLTypography>
//           ),
//         },
//         {
//           id: "actions",
//           header: "Actions",
//           meta: { isVirtual: false },
//           accessorFn: (row) => row,
//           cell: ({ getValue }) => {
//             const value = getValue() as Employee;
//             return (
//               <Stack direction="row" spacing={1.5}>
//                 <MLButton
//                   endIcon={<img src={editIcon} alt="edit" />}
//                   sx={{
//                     padding: "1px 35px",
//                     fontSize: "14px",
//                     fontWeight: 500,
//                   }}
//                   size="small"
//                   variant="outlined"
//                   onClick={() => navigate(`/employees/edit/${value.id}`)}
//                 >
//                   Edit
//                 </MLButton>
//                 <IconButton onClick={() => handleClickOpen(value.id)}>
//                   <img src={deleteIcon} alt="delete" />
//                 </IconButton>
//               </Stack>
//             );
//           },
//         }
//       ];

//       return [...baseColumns, ...remainingColumns];
//     },
//     [hasVendorAccess, navigate]
//   );

//   // Modify your useTable implementation to include the risk level filter
//   const {
//     refineCore: { tableQueryResult },
//     getHeaderGroups,
//     getRowModel,
//     getState,
//     setPageIndex,
//     setPageSize,
//   } = useTable({
//     columns,
//     refineCoreProps: {
//       resource: "employees",
//       syncWithLocation: true,
//       pagination: {
//         mode: "server",
//         pageSize: 10,
//       },
//       meta: {
//         populate: {
//           organization: true,
//           userID: { populate: ["role"] },
//         },
//       },
//       filters: {
//         permanent: [
//           {
//             field: "organization.id",
//             operator: "eq",
//             value: organizationIds,
//           },
//           {
//             field: "archive",
//             operator: "eq",
//             value: false,
//           },
//           {
//             field: "riskLevel",
//             operator: activeRiskFilter === "none" ? "null" : "eq",
//             value: activeRiskFilter === "null" ? null : activeRiskFilter,
//           }
//         ],
//       },
//       sorters: {
//         mode: "server",
//         initial: [{ field: "createdAt", order: "desc" }],
//       },
//       queryOptions: {
//         staleTime: 30 * 1000,
//         cacheTime: 5 * 60 * 1000,
//       },
//     },
//   });


//   // Simplify loading state
//   const tableIsLoading = tableQueryResult.isFetching ||
//     tableQueryResult.isLoading ||
//     isLoadingUserDetails ||
//     (hasVendorAccess && isLoadingVendorAssignment);

//   // Optimize data fetching with a single useEffect
//   useEffect(() => {
//     const shouldFetch = hasVendorAccess
//       ? !isLoadingVendorAssignment && vendorAssignmentData?.data && organizationIds.length > 0
//       : !isLoadingUserDetails && userDetails?.data?.organization?.id && organizationIds.length > 0;

//     if (shouldFetch) {
//       tableQueryResult.refetch().then((response) => {
//         // console.log('Employee Data:', response?.data?.data);
//       });
//     }
//   }, [
//     hasVendorAccess,
//     vendorAssignmentData?.data,
//     userDetails?.data,
//     isLoadingVendorAssignment,
//     isLoadingUserDetails,
//     organizationIds
//   ]);

//   // Update search handling with debounce
//   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     const value = e.target.value;
//     setSearchQuery(value);

//     // Clear any existing timer
//     if (debounceTimerRef.current) {
//       clearTimeout(debounceTimerRef.current);
//     }

//     // Set a new timer
//     debounceTimerRef.current = setTimeout(() => {
//       nameColumn?.setFilterValue(value);
//       setPageIndex(0);
//       tableQueryResult.refetch();
//     }, 500);
//   };

//   // Clear timeout on component unmount
//   useEffect(() => {
//     return () => {
//       if (debounceTimerRef.current) {
//         clearTimeout(debounceTimerRef.current);
//       }
//     };
//   }, []);

//   const handleClickOpen = (id: number) => {
//     setSelectedEmployeeId(id);
//     setOpen(true);
//   };

//   const handleClose = () => {
//     setOpen(false);
//     setSelectedEmployeeId(null);
//   };

//   const handleArchive = () => {
//     if (selectedEmployeeId) {
//       if (selectedEmployeeId) {
//         const employee = tableQueryResult.data?.data.find(
//           (emp) => emp.id === selectedEmployeeId
//         );

//         try {
//           updateEmployee({
//             resource: "employees",
//             id: selectedEmployeeId,
//             values: { archive: true },
//             mutationMode: "pessimistic",
//           });

//           if (employee?.userID?.id) {
//             updateUser({
//               resource: "users",
//               id: employee.userID.id!,
//               values: { archive: true },
//               mutationMode: "pessimistic",
//               successNotification: false,
//             });
//           }

//           handleClose();
//         } catch (error) {
//           // console.error('Archive operation failed:', error);
//         }
//       }
//     }
//   };

//   const handleRiskFilterChange = useCallback((filter: string | null) => {
//     setActiveRiskFilter(filter);
//     // setSearchQuery("");
//     // nameColumn?.setFilterValue("")  // if want to reset the filter bye name after clicking on a different risk level while searching by name.

//     // Reset pagination to first page when filter changes
//     setPageIndex(0);

//     // Refresh with both filters
//     tableQueryResult.refetch();
//   }, [setPageIndex, tableQueryResult]);

//   const handleImportResponse = (response: ImportResponseState) => {
//     setImportResponse(response);
//     setShowImportMessage(true);
//     setOpenImportEmployeeCsvDialog(false);
//     tableQueryResult.refetch(); // Refresh the employee list
//     setShowMoreErrors(false)
//   };

//   const downloadCSV = (data: NotInsertedType[]) => {
//     if (data.length === 0) return; // Prevent download if there are no data
//     // Extract headers from the first record
//     const headers = Object.keys(data[0].record);

//     // Generate CSV rows from record data only
//     const csvRows = data.map(error => {
//       // Extract values for the record
//       return Object.values(error.record).join(',');
//     });

//     // Combine headers and rows
//     const csvContent = [headers.join(','), ...csvRows].join('\n');

//     // Create a Blob from the CSV string
//     const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

//     // Create a link to download the Blob
//     const link = document.createElement('a');
//     if (link.download !== undefined) {
//       const url = URL.createObjectURL(blob);
//       link.setAttribute('href', url);
//       link.setAttribute('download', `notinserted.csv`);
//       link.style.visibility = 'hidden';
//       document.body.appendChild(link);
//       link.click();
//       document.body.removeChild(link);
//     }
//   };

//   const nameColumn = getHeaderGroups()[0]?.headers.find(
//     (header) => header.id === "name"
//   )?.column;

//   const renderMessageBaseOnResponseFromServer = () => {

//     if (!importResponse) return null;
//     const { success, inserted, notInserted, details } = importResponse;

//     if (details.inserted.length > 0 && details.notInserted.length === 0) {
//       return (
//         <Stack width="70%">
//           <CustomMessageBox status="success">
//             <Stack gap="16px">
//               <MLTypography
//                 sx={{
//                   fontSize: "16px",
//                   fontWeight: 600,
//                 }}
//               >
//                 Users imported successfully
//               </MLTypography>
//               <Box>
//                 <MLTypography >
//                   Number of users updated : {inserted}
//                 </MLTypography>
//                 <MLTypography >
//                   Last updated: 22/09/2024. 23:30:00
//                 </MLTypography>
//               </Box>
//             </Stack>
//           </CustomMessageBox>
//         </Stack>
//       )
//     }
//     // some inserted and some failed
//     if (details.inserted.length > 0 && details.notInserted.length > 0) {
//       return (
//         <Stack width="70%">
//           <CustomMessageBox status="warning">
//             <Stack gap="16px">
//               <Box>
//                 <MLTypography
//                   sx={{
//                     fontSize: "16px",
//                     fontWeight: 600,
//                   }}
//                 >
//                   {inserted} employees insert failed
//                 </MLTypography>
//                 <MLTypography
//                   sx={{
//                     fontSize: "16px",
//                     fontWeight: 600,
//                   }}
//                 >
//                   {notInserted} employees updated successfull
//                 </MLTypography>
//               </Box>
//               <Box>
//                 {details.notInserted.slice(0, 3).map((item, index) => (
//                   <MLTypography key={index}>
//                     {item.error === "This attribute must be unique"
//                       ? `The email address "${item.record.email}" is already registered. Please use a different email.`
//                       : item.error}
//                   </MLTypography>
//                 ))}
//                 {details.notInserted.length > 3 && (
//                   <Collapse in={showMoreErrors}>
//                     {details.notInserted.slice(3).map((item, index) => (
//                       index <= 4 && <MLTypography key={index + 3}>
//                         {item.error === "This attribute must be unique"
//                           ? `The email address "${item.record.email}" is already registered. Please use a different email.`
//                           : item.error}
//                       </MLTypography>
//                     ))}
//                   </Collapse>
//                 )}
//                 {details.notInserted.length > 3 && (
//                   <MLButton
//                     variant="text"
//                     onClick={() => setShowMoreErrors((prev) => !prev)}
//                     style={{ marginTop: 8 }}
//                   >
//                     {showMoreErrors ? "Show less" : `Show more (${details.notInserted.length - 3} more)`}
//                   </MLButton>
//                 )}
//                 {details.notInserted.length > 8 && (
//                   <MLButton variant="text" style={{ marginTop: 8 }} onClick={() => downloadCSV(details.notInserted)}>Download Not Inserted</MLButton>
//                 )}
//               </Box>
//             </Stack>
//           </CustomMessageBox>
//         </Stack>
//       )
//     }
//     //nothing is inserted
//     if (details.inserted.length === 0) {
//       return (
//         <Stack width="70%">
//           <CustomMessageBox status="error">
//             <Stack gap="16px">
//               <MLTypography
//                 sx={{
//                   fontSize: "16px",
//                   fontWeight: 600,
//                 }}
//               >
//                 Users import failed
//               </MLTypography>
//               <Box>
//                 {details.notInserted.slice(0, 3).map((item, index) => (
//                   <MLTypography key={index}>
//                     {item.error === "This attribute must be unique"
//                       ? `The email address "${item.record.email}" is already registered. Please use a different email.`
//                       : item.error}
//                   </MLTypography>
//                 ))}
//                 {details.notInserted.length > 3 && (
//                   <Collapse in={showMoreErrors}>
//                     {details.notInserted.slice(3).map((item, index) => (
//                       index <= 4 && <MLTypography key={index + 3}>
//                         {item.error === "This attribute must be unique"
//                           ? `The email address "${item.record.email}" is already registered. Please use a different email.`
//                           : item.error}
//                       </MLTypography>
//                     ))}
//                   </Collapse>
//                 )}
//                 {details.notInserted.length > 3 && (
//                   <MLButton
//                     variant="text"
//                     onClick={() => setShowMoreErrors((prev) => !prev)}
//                     style={{ marginTop: 8 }}
//                   >
//                     {showMoreErrors ? "Show less" : `Show more (${details.notInserted.length - 3} more)`}
//                   </MLButton>
//                 )}
//                 {details.notInserted.length > 8 && (
//                   <MLButton variant="text" style={{ marginTop: 8 }} onClick={() => downloadCSV(details.notInserted)}>Download Not Inserted</MLButton>
//                 )}
//               </Box>
//             </Stack>
//           </CustomMessageBox>
//         </Stack>
//       )
//     }
//     else {
//       return null
//     }
//   };

//   return (
//     <Stack
//       gap="30px"
//       sx={{
//         backgroundImage: "url('/background_waves/horizontal1.svg')",
//         backgroundSize: "cover",
//         backgroundAttachment: "fixed",
//         paddingX: {
//           lg: desktop.contentContainer.paddingX,
//           md: tablet.contentContainer.paddingX,
//           xs: tablet.contentContainer.paddingX,
//         },
//         paddingY: {
//           lg: desktop.contentContainer.paddingY,
//           md: tablet.contentContainer.paddingY,
//           xs: tablet.contentContainer.paddingY,
//         },
//       }}
//     >
//       <Stack direction={"row"} justifyContent={"space-between"}>
//         <MLTypography fontSize={{ xs: '24px', sm: '40px' }} marginLeft={{ xs: "47px", md: "0px" }} lineHeight="120%" variant="h1">
//           Employee directory
//         </MLTypography>
//         <Stack direction="row" gap={1.5} alignItems={"center"}>
//           <MLTextField
//             label="Search for employee name"
//             id="employee-filter-search-bar"
//             sx={{ borderRadius: "10px", minWidth: 300 }}
//             value={searchQuery}
//             size="small"
//             InputProps={{
//               endAdornment: (
//                 <MLInputAdornment position="end">
//                   <IconButton>
//                     <ErSearch />
//                   </IconButton>
//                 </MLInputAdornment>
//               ),
//             }}
//             onChange={handleSearchChange}
//           />
//           <MLButton
//             variant="outlined"
//             onClick={() => setOpenImportEmployeeCsvDialog(true)}
//           >
//             Import
//           </MLButton>
//           <Link to="/employees/create">
//             <MLButton endIcon={<Add />} variant="contained" color="secondary">
//               Create
//             </MLButton>
//           </Link>
//         </Stack>
//       </Stack>
//       <EmployeeStatus
//         allEmployees={employeeList ?? { data: [], total: 0 }} // Pass the full list here
//         onFilterChange={handleRiskFilterChange}
//         activeFilter={activeRiskFilter}
//       >
//         {showImportMessage && renderMessageBaseOnResponseFromServer()}
//       </EmployeeStatus>
//       {/* showing this message box for high risk employees are active filter */}
//       {activeRiskFilter === "high" &&
//         <CustomMessageBox status="error">
//           <Stack gap="16px">
//             <MLTypography
//               sx={{
//                 fontSize: "16px",
//                 fontWeight: 600,
//               }}
//             >
//               These employees are flagged as high risk. Consider scheduling an expert assessment
//             </MLTypography>
//           </Stack>
//         </CustomMessageBox>}
//       {/* <MLButton variant="outlined" onClick={() => downloadCSV(employeeData, 'employee_data.csv')}>Download me</MLButton> */}
//       <>
//         {tableIsLoading ? (
//           <Loading />
//         ) : (
//           <Stack gap={2} >
//             {getHeaderGroups().map((headerGroup) => (
//               <React.Fragment key={headerGroup.id}>

//                 <Stack display={{ xs: "none", md: "flex" }}>
//                   <Box sx={{
//                     display: "grid",
//                     gridTemplateColumns: "1.5fr 1.5fr 1fr 1fr 1fr 1fr 1fr 1fr",
//                     columnGap: "0.5rem",
//                     padding: "0 1rem"
//                   }}>
//                     {headerGroup.headers.map((header) => (
//                       <Box
//                         key={header.id}
//                         onClick={header.id === "actions" ? undefined : header.column.getToggleSortingHandler()}
//                         sx={{
//                           cursor: header.id === "actions" ? "default" : "pointer",
//                           textAlign: "left"
//                         }}
//                       >
//                         <Stack direction="row" alignItems="center" gap={0.5}>
//                           {header.id !== "actions" && (
//                             <>{header.column.getIsSorted() === "asc" ? <AscendingChevron /> :
//                               header.column.getIsSorted() === "desc" ? <DescendingChevron /> :
//                                 <DefaultChevron />}</>
//                           )}
//                           <MLTypography fontSize={14} fontWeight="medium">
//                             {flexRender(header.column.columnDef.header, header.getContext())}
//                           </MLTypography>
//                         </Stack>
//                       </Box>
//                     ))}
//                   </Box>
//                 </Stack>
//               </React.Fragment>
//             ))}
//             {/* Table Data */}
//             <Stack direction="column" gap={{ xs: "15px", md: "20px" }}>

//               {getRowModel().rows.length > 0 ? (
//                 getRowModel().rows.map((row) => {

//                   return (
//                     <Stack key={row.id}>
//                       {/* Mobile Card View */}
//                       <Stack display={{ xs: "flex", md: "none" }}>
//                         <MLCard sx={{ padding: { xs: "20px", md: "24px" } }}>
//                           <Stack gap="8px">
//                             <Stack direction="row" justifyContent="space-between" alignItems={'center'}>
//                               <MLTypography
//                                 variant="body1"
//                                 fontSize={{ xs: '17px', sm: "20px" }}
//                                 width={{ xs: '60px' }}
//                                 fontWeight={600}
//                                 lineHeight={1}
//                               >
//                                 <Link
//                                   to={`/employees/show/${row.original.id}`}
//                                   style={{ textDecoration: "none", color: "inherit" }}
//                                 >
//                                   {capitalizeWords(row.getValue('name'))}

//                                 </Link>
//                               </MLTypography>
//                               <MLTypography
//                                 variant="body1"
//                                 fontSize="12.5px"
//                                 fontWeight={400}
//                               >
//                                 Date joined: {dayjs(row.getValue('createdAt')).format("DD-MM-YYYY")}
//                               </MLTypography>
//                             </Stack>

//                             <Stack direction="row">
//                               <MLTypography
//                                 variant="body1"
//                                 fontSize="14px"
//                                 fontWeight={400}
//                               >
//                                 {row.getValue('email')}
//                               </MLTypography>
//                             </Stack>

//                             <Stack direction="row">
//                               <MLTypography
//                                 variant="body1"
//                                 fontSize="14px"
//                                 fontWeight={400}
//                               >
//                                 {row.getValue('role')}
//                               </MLTypography>
//                             </Stack>

//                             <Stack direction="row">
//                               <Box>
//                                 <MLTypography
//                                   variant="body1"
//                                   fontSize="14px"
//                                   fontWeight={400}
//                                 >
//                                   Assessment Date : {dayjs(row.getValue('assessmentDate')).format("DD MMM YYYY")}
//                                 </MLTypography>
//                                 <MLTypography
//                                   variant="body1"
//                                   fontSize="14px"
//                                   fontWeight={400}
//                                 >
//                                   Risk Level : {row.getValue('riskLevel')}
//                                 </MLTypography>
//                                 <MLTypography
//                                   variant="body1"
//                                   fontSize="14px"
//                                   fontWeight={400}
//                                 >
//                                   Contact no : {row.getValue('contact')}
//                                 </MLTypography>
//                               </Box>
//                             </Stack>


//                             <Stack sx={{
//                               justifyContent: 'space-between'
//                             }}>
//                               <Stack direction="row" spacing={1.5}>
//                                 <MLButton
//                                   endIcon={<img src={editIcon} alt="edit" />}
//                                   sx={{
//                                     padding: "1px 35px",
//                                     fontSize: "14px",
//                                     fontWeight: 500,
//                                   }}
//                                   size="small"
//                                   variant="outlined"
//                                   onClick={() => {
//                                     const value = row.getValue('actions') as Employee;
//                                     return navigate(`/employees/edit/${value.id}`)
//                                   }}
//                                 >
//                                   Edit
//                                 </MLButton>
//                                 <IconButton
//                                   onClick={() => {
//                                     const value = row.getValue('actions') as Employee;
//                                     return handleClickOpen(value.id)
//                                   }
//                                   }>

//                                   <img src={deleteIcon} alt="delete" />
//                                 </IconButton>
//                               </Stack>

//                             </Stack>
//                           </Stack>
//                         </MLCard>
//                       </Stack>

//                       {/* Desktop screen view  */}
//                       <Stack display={{ xs: "none", md: "flex" }}>
//                         <MLCard key={row.id}>
//                           <Box sx={{
//                             display: "grid",
//                             gridTemplateColumns: "1.5fr 1.5fr 1fr 1fr 1fr 1fr 1fr 1fr",
//                             columnGap: "0.5rem",

//                             alignItems: "center"
//                           }}>
//                             {row.getVisibleCells().map((cell) => (
//                               <Box key={cell.id} sx={{ overflow: "hidden", textOverflow: "ellipsis" }}>
//                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
//                               </Box>
//                             ))}
//                           </Box>
//                         </MLCard>
//                       </Stack>
//                     </Stack>
//                   )
//                 }


//                 )
//               ) : (
//                 <MLCard>
//                   <CardContent>
//                     <Stack
//                       py={2}
//                       direction="column"
//                       gap={2}
//                       alignItems={"center"}
//                     >
//                       <MLTypography variant="h3">
//                         No employees found!
//                       </MLTypography>
//                       <MLTypography>
//                         Consider trying different filter criteria
//                       </MLTypography>
//                       <MLButton onClick={() => { }}>Clear filters</MLButton>
//                     </Stack>
//                   </CardContent>
//                 </MLCard>
//               )}
//               <TablePagination
//                 component="div"
//                 count={tableQueryResult.data?.total ?? 0}
//                 page={getState().pagination.pageIndex}
//                 onPageChange={(e, newPage) => setPageIndex(newPage)}
//                 rowsPerPage={getState().pagination.pageSize}
//                 onRowsPerPageChange={(e) =>
//                   setPageSize(parseInt(e.target.value))
//                 }
//               />
//             </Stack>
//           </Stack>
//         )}
//       </>
//       <Dialog open={open} onClose={handleClose}>
//         <DialogTitle fontSize={26}>{"Delete Employee"}</DialogTitle>
//         <DialogContent>
//           <DialogContentText
//             fontSize={16}
//             fontFamily={"Work Sans"}
//             color={"rgba(0, 0, 0, 0.87);"}
//             fontWeight={500}
//           >
//             Are you sure you want to delete this employee?
//           </DialogContentText>
//         </DialogContent>
//         <DialogActions>
//           <MLButton
//             sx={{
//               mx: 0.5,
//             }}
//             onClick={handleArchive}
//             size="medium"
//             variant="contained"
//             color="secondary"
//           >
//             Delete
//           </MLButton>
//           <MLButton onClick={handleClose} color="primary">
//             Cancel
//           </MLButton>
//         </DialogActions>
//       </Dialog>
//       <Dialog
//         open={openImportEmployeeCsvDialog}
//         onClose={() => setOpenImportEmployeeCsvDialog(false)}
//         fullWidth
//       >
//         <DialogContent>
//           <ImportEmployeeCsv
//             handleCloseImportEmployeeCsvDialog={() => setOpenImportEmployeeCsvDialog(false)}
//             handleImportResponse={handleImportResponse}
//             organizationId={userDetails?.data.organization.id!}
//           />
//         </DialogContent>
//       </Dialog>
//     </Stack >
//   );
// };
// export default Employee;
