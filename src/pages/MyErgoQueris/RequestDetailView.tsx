import { Box, IconButton, Stack } from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import { useBack, useGetIdentity, useOne, useUpdate } from '@refinedev/core';
import Loading from '../Loading/Loading';
import { ErgoRequest } from './MyErgoQueris';
import MLTypography from '../../components/ui/MLTypography/MLTypography';
import { desktop, tablet } from '../../responsiveStyles';
import MLButton from '../../components/ui/MLButton/MLButton';
import { useContext, useState } from "react";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { API_URL } from '../../constants';
import PriorityDisplay from './components/PriorityDisplay';
import StatusBadge from './components/StatusBadge';
import { ErChevronleft } from '@mindlens/ergo-icons';
import { IIdentity } from '../AuthScreens/Profile/Profile';
import AssigneeSelector from './components/AssigneeSelector';
import User from '../../models/User';
import AttachmentPreviewModal from './components/AttachmentPreviewModal';
import MLBanner from '../../components/ui/MLBanner/MLBanner';
import MarkCompleteModal from './components/MarkCompleteModal';
import InitialMessage from './components/InitialMessage';
import ThreadMessage from './components/ThreadMessage';
import ResponseForm from './components/ResponseForm ';
import { UserRole } from '../../RoleBasedResources';
import MLContainer from '../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer';
import { ViewModeContext } from '../../contexts/ViewModeContext/ViewModeContext';


const RequestDetailView = () => {
    const { data: identity } = useGetIdentity<IIdentity>();
    const { isEmployeeView } = useContext(ViewModeContext);
    const { data: userDetails, isLoading: isLoadingUserDetails } = useOne<User>({
        resource: "users",
        id: identity?.id!,
        meta: {
            populate: ["employee", "role", "organization"]
        }
    });
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [previewOpen, setPreviewOpen] = useState(false);
    const [currentAttachments, setCurrentAttachments] = useState<Array<{ url: string; name: string; mime?: string }>>([]);
    const [initialPreviewIndex, setInitialPreviewIndex] = useState(0);

    // Add mutation hooks
    const { mutate: createThread, isLoading: loadingCreateThread } = useCreate();
    const { mutate: updateRequest, isLoading: loadingUpdateRequest } = useUpdate();
    const { mutate: uploadAttachments } = useCustomMutation();
    const back = useBack();
    const { id } = useParams();
    const navigate = useNavigate()

    const currentTime = new Date().toISOString();
    const role = userDetails?.data.role.name || "Employee"
    const isPrivilegedRole = role === "Ergonomist" || role === "Ergo ambassador" || role === "Administrator";

    const { data: requestData, isLoading, isError, refetch } = useOne<ErgoRequest>({
        resource: "ep-ergo-requests",
        id: id || "",
        meta: {
            populate: {
                sender: {
                    populate: '*'
                },
                'sender.user': {
                    populate: '*'
                },
                'sender.user.employee': {
                    populate: '*'
                },
                attachments: {
                    fields: ['name', 'url', 'mime', 'size']
                },
                assignedTo: "*",
                ergoRequestThread: {
                    populate: {
                        sender: {
                            populate: '*'
                        },
                        'sender.user': {
                            populate: '*'
                        },
                        'sender.user.employee': {
                            populate: '*'
                        },
                        attachments: {
                            fields: ['name', 'url', 'mime', 'size']
                        }
                    },
                    // Add sorting to ensure threads appear in order
                    sort: ['threadIndex:asc']
                }
            }
        },
        // queryOptions: {
        //     // Ensure data is always fresh
        //     staleTime: 100
        // }
    });

    const formatRelativeTime = (dateString: string) => {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
        const diffInMinutes = Math.floor(diffInSeconds / 60);
        const diffInHours = Math.floor(diffInMinutes / 60);
        const diffInDays = Math.floor(diffInHours / 24);

        if (diffInMinutes < 1) return 'just now';
        if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
        if (diffInHours < 24) return `${diffInHours} hours ago`;
        if (diffInDays < 7) return `${diffInDays} days ago`;

        // Format date like "26th Jan, 2025"
        const day = date.getDate();
        const month = date.toLocaleString('default', { month: 'short' });
        const year = date.getFullYear();

        // Add ordinal suffix to day
        const ordinal = (n: number) => {
            const s = ['th', 'st', 'nd', 'rd'];
            const v = n % 100;
            return n + (s[(v - 20) % 10] || s[v] || s[0]);
        };

        return `${ordinal(day)} ${month}, ${year}`;
    };

    const handleMarkAsComplete = () => {
        updateRequest({
            resource: "ep-ergo-requests",
            id: id!,
            values: {
                status: "completed",
                updatedAt: currentTime
            },
            successNotification: {
                message: 'Request marked as completed successfully!',
                type: 'success'
            },
            errorNotification: (error) => ({
                message: `Error updating request status: ${error?.message}`,
                type: 'error'
            })
        },
            {
                onSuccess: () => {
                    setShowSuccessModal(true);
                    refetch();
                }
            });
    };

    const handleOpenPreview = (attachments: any[], startIndex: number) => {
        setCurrentAttachments(attachments);
        setInitialPreviewIndex(startIndex);
        setPreviewOpen(true);
    };

    // Updated submit handler with sentAt and isErgoAmbassadorResponse
    const handleResponseSubmit = async (responseText: string, selectedFiles: File[]) => {
        // Transform response text to rich text block format
        const contentBlock = [{
            type: 'paragraph',
            children: [{
                type: 'text',
                text: responseText
            }]
        }];

        // Calculate thread index
        const nextThreadIndex = ergoRequestThread ? ergoRequestThread.length : 0;

        createThread(
            {
                resource: "ep-ergo-request-threads",
                values: {
                    content: contentBlock,
                    ergoRequest: id,
                    sender: userDetails?.data.id,
                    sentAt: currentTime,
                    isErgoAmbassadorResponse: isPrivilegedRole,
                    threadIndex: nextThreadIndex, // Add thread index
                    createdAt: currentTime,
                    updatedAt: currentTime
                }
            },
            {
                onSuccess: async (data) => {
                    // Handle file uploads if present
                    if (isPrivilegedRole && requestData?.data?.status === "pending") {
                        updateRequest(
                            {
                                resource: "ep-ergo-requests",
                                id: id!,
                                values: {
                                    status: "processing",
                                    updatedAt: currentTime
                                }
                            },
                            {
                                onSuccess: () => {
                                    refetch(); // This will update the UI with the latest request data
                                },
                                onError: (error) => {
                                    console.error("Error updating request status:", error);
                                }
                            }
                        );
                    }
                    if (selectedFiles.length > 0 && data?.data?.data?.id) {
                        const imageData = new FormData();
                        selectedFiles.forEach(file => {
                            imageData.append("files", file);
                        });

                        imageData.append("refId", JSON.stringify(data.data.data.id));
                        imageData.append("ref", "api::ep-ergo-request-thread.ep-ergo-request-thread");
                        imageData.append("field", "attachments");

                        uploadAttachments({
                            url: `${API_URL}/api/upload`,
                            method: "post",
                            values: imageData,
                            successNotification: () => ({
                                message: "Response submitted successfully!",
                                type: "success",
                            }),
                            errorNotification: (error: any) => ({
                                message: `Error uploading files: ${error.message}`,
                                type: "error",
                            }),
                            meta: {
                                fields: ["url", "name", "mime", "size"]
                            }
                        },
                            {
                                onSuccess: () => {
                                    // Only refetch after attachments are uploaded
                                    refetch();
                                },
                                onError: (error) => {
                                    console.error("Error uploading attachments:", error);
                                    // Still refetch to show the thread even if attachments failed
                                    refetch();
                                }
                            });
                    }
                    // Refetch the data to update the UI
                    refetch();
                },
                // errorNotification: (error) => ({
                //     message: `Error submitting response: ${error.message}`,
                //     type: "error",
                // }),
            }
        );
    };


    if (isLoading || isLoadingUserDetails) {
        return <Box height={640}> <Loading /> </Box>
    }

    if (isError || !requestData) {
        return (
            <Box p={3}>
                <MLTypography color="error">
                    Error loading request details. Please try again.
                </MLTypography>
            </Box>
        );
    }

    const {
        title,
        description,
        priority,
        status,
        sender,
        createdOn,
        updatedAt,
        attachments,
        ergoRequestThread
    } = requestData.data;

    return (
        <>
            {userDetails?.data?.role &&
                (userDetails?.data.role.name.toLowerCase() as UserRole) === "employee" ?
                <MLBanner isBackButton={true} backgroundColor='#E3DDFF' title={title} />
                :
                <>
                    <Stack
                        direction="row"
                        alignItems={"center"}
                        sx={{
                            // paddingX: {
                            //     lg: desktop.contentContainer.paddingX,
                            md: tablet.contentContainer.paddingX,
                            xs: tablet.contentContainer.paddingX,
                            // },
                            paddingY: {
                                lg: desktop.contentContainer.paddingY,
                                md: tablet.contentContainer.paddingY,
                                xs: tablet.contentContainer.paddingY,
                            },
                            mt: {
                                md: isEmployeeView ? '-22px' : '',
                                sm: isEmployeeView ? '-20px' : ''
                            },

                        }}
                    >
                        <IconButton size="large" onClick={() => back()}>
                            <ErChevronleft />
                        </IconButton>
                        <MLTypography variant="h1" fontWeight={700}
                            sx={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                maxWidth: { xs: "calc(100vw - 100px)" },
                            }}
                        >
                            {title}
                        </MLTypography>
                    </Stack>
                </>

            }

            <Stack
                direction={"column"}
                gap={1}
            >
                <Box >
                    {/* Initial message header */}
                    <Stack sx={{
                        paddingX: {
                            lg: desktop.contentContainer.paddingX,
                            md: tablet.contentContainer.paddingX,
                            xs: tablet.contentContainer.paddingX,
                        }
                    }}>
                        <MLContainer>
                            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
                                <Stack direction="row" spacing={2} alignItems="center">
                                    <PriorityDisplay priority={priority} />
                                    {isPrivilegedRole &&
                                        <AssigneeSelector
                                            requestId={parseInt(id!)}
                                            loggedUser={userDetails?.data}
                                            currentAssignee={requestData?.data?.assignedTo}
                                            buttonStyle="compact"
                                            status={status}
                                        />
                                    }
                                    <StatusBadge status={status} />
                                </Stack>
                                <Stack direction="row" gap={2}>
                                    {isPrivilegedRole && status !== "completed" && (
                                        <MLButton
                                            variant="contained"
                                            onClick={handleMarkAsComplete}
                                            sx={{
                                                bgcolor: '#7856FF',
                                                '&:hover': {
                                                    bgcolor: '#6745FF'
                                                }
                                            }}
                                        >
                                            Mark as Completed
                                        </MLButton>
                                    )}
                                    <MLButton
                                        type="submit"
                                        variant="contained"
                                        color="secondary"
                                        onClick={() => navigate("/requestAssessment")}
                                    >
                                        Request Ergo Assessment
                                    </MLButton>
                                </Stack>
                            </Stack>

                            {/* Timestamp */}
                            <MLTypography color="text.secondary" mb={4}>
                                Opened on {createdOn ? formatRelativeTime(createdOn) : "NA"} | Last updated {updatedAt ? formatRelativeTime(updatedAt) : "NA"}
                            </MLTypography>
                        </MLContainer>
                    </Stack>
                    <Stack >
                        {/* Initial Message */}
                        <Stack
                            sx={{
                                paddingX: {
                                    lg: desktop.contentContainer.paddingX,
                                    md: tablet.contentContainer.paddingX,
                                    xs: tablet.contentContainer.paddingX,
                                },
                            }}
                        >
                            <MLContainer>
                                <InitialMessage
                                    sender={sender}
                                    createdOn={createdOn}
                                    description={description}
                                    attachments={attachments}
                                    onOpenPreview={handleOpenPreview}
                                />
                                {ergoRequestThread.length === 0 && <Box borderBottom="0.5px solid #9C9C9C" />}
                            </MLContainer>
                        </Stack>
                        {/* thread Message */}
                        <Stack>
                            {ergoRequestThread?.map((thread, index) => (
                                <Stack
                                    key={thread.id}
                                    sx={{
                                        ...(thread.sender?.role?.name !== "Employee" && {
                                            background: 'var(--Colors-Coral-00, #FFE2E2)',
                                        }),
                                        paddingX: {
                                            lg: desktop.contentContainer.paddingX,
                                            md: tablet.contentContainer.paddingX,
                                            xs: tablet.contentContainer.paddingX,
                                        },

                                    }}
                                >
                                    <MLContainer>
                                        {
                                            ergoRequestThread[0].sender?.role.name === "Employee"
                                            && <Box borderBottom="0.5px solid #9C9C9C" />
                                        }
                                        <ThreadMessage
                                            key={thread.id}
                                            thread={thread}
                                            onOpenPreview={handleOpenPreview}
                                        />
                                        {
                                            index < ergoRequestThread.length - 1
                                            && (ergoRequestThread[index].sender?.role.name ===
                                                ergoRequestThread[index + 1].sender?.role.name)
                                            && <Box borderBottom="0.5px solid #9C9C9C" />
                                        }
                                    </MLContainer>
                                </Stack>
                            ))}
                            {/* <Box borderBottom="0.5px solid #9C9C9C" /> */}
                        </Stack>
                        {/* Reply Section */}
                        <Stack
                            sx={{
                                paddingX: {
                                    lg: desktop.contentContainer.paddingX,
                                    md: tablet.contentContainer.paddingX,
                                    xs: tablet.contentContainer.paddingX,
                                },
                            }}
                        >
                            <MLContainer>
                                {
                                    ergoRequestThread.length > 0
                                    && ergoRequestThread[ergoRequestThread.length - 1].sender?.role.name === "Employee"
                                    && <Box borderBottom="0.5px solid #9C9C9C" />
                                }
                                <ResponseForm
                                    userDetails={userDetails?.data}
                                    onSubmit={handleResponseSubmit}
                                    isSubmitting={loadingCreateThread}
                                />
                            </MLContainer>
                        </Stack>
                    </Stack>
                </Box>
                <MarkCompleteModal
                    open={showSuccessModal}
                    onClose={() => setShowSuccessModal(false)}
                />
                <AttachmentPreviewModal
                    open={previewOpen}
                    onClose={() => setPreviewOpen(false)}
                    attachments={currentAttachments}
                    initialIndex={initialPreviewIndex}
                />
            </Stack>
        </>
    );
};

export default RequestDetailView;