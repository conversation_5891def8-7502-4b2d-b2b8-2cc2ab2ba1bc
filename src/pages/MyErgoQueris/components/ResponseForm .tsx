import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Box, Badge, ImageList, ImageListItem, IconButton, styled } from '@mui/material';
import { Cancel, UploadFile } from '@mui/icons-material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../components/ui/MLButton/MLButton';
import MLInputbox from '../../../components/ui/MLInputbox/MLInputbox';
import UserAvatarWithRole from './UserAvatarWithRole';
import Loading from '../../Loading/Loading';
import { desktop, tablet } from '../../../responsiveStyles';

const PreviewGrid = styled(Box)({
    width: "100%",
    maxWidth: "700px",
    marginTop: "16px"
});

const RESPONSE_MAX_LENGTH = 2000;
const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes

interface ResponseFormProps {
    userDetails: any;
    onSubmit: (responseText: string, selectedFiles: File[]) => void;
    isSubmitting: boolean;
}

const ResponseForm = ({ userDetails, onSubmit, isSubmitting }: ResponseFormProps) => {
    const [responseText, setResponseText] = useState("");
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [previewUrls, setPreviewUrls] = useState<string[]>([]);
    const [isAttachmentLoading, setIsAttachmentLoading] = useState(false);
    const [errors, setErrors] = useState<{
        responseText?: string;
        file?: string;
    }>({});

    useEffect(() => {
        if (selectedFiles.length > 0) {
            setPreviewUrls(prev => {
                prev.forEach(url => URL.revokeObjectURL(url));
                return [];
            });

            const urls = selectedFiles.map(file => URL.createObjectURL(file));
            setPreviewUrls(urls);

            return () => {
                urls.forEach(url => URL.revokeObjectURL(url));
            };
        } else {
            setPreviewUrls(prev => {
                prev.forEach(url => URL.revokeObjectURL(url));
                return [];
            });
        }
    }, [selectedFiles]);

    const validateFile = (file: File): boolean => {
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
            setErrors(prev => ({
                ...prev,
                file: 'Only JPG, JPEG, PNG, or PDF files are allowed'
            }));
            return false;
        }

        if (file.size > MAX_FILE_SIZE) {
            setErrors(prev => ({
                ...prev,
                file: 'File size should be equal to or less than 5 MB'
            }));
            return false;
        }

        return true;
    };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            const newFiles = Array.from(event.target.files);

            // Validate files first
            const validFiles = newFiles.filter(file => validateFile(file));

            // Only proceed with loading if we have valid files
            if (validFiles.length > 0) {
                setIsAttachmentLoading(true);
                setErrors(prev => ({ ...prev, file: undefined }));

                const uniqueNewFiles = validFiles.filter(newFile =>
                    !selectedFiles.some(existingFile => existingFile.name === newFile.name)
                );

                // Only show loading if we have valid files to process
                if (uniqueNewFiles.length > 0) {
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    setSelectedFiles(prev => [...prev, ...uniqueNewFiles]);
                }

                setIsAttachmentLoading(false);
            }
        }
    };
    
    const handleResponseChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let value = e.target.value;
        
        // If text exceeds max length, truncate it
        if (value.length > RESPONSE_MAX_LENGTH) {
            value = value.slice(0, RESPONSE_MAX_LENGTH);
            setErrors(prev => ({
                ...prev,
                responseText: `Maximum ${RESPONSE_MAX_LENGTH} characters allowed`
            }));
        } else {
            setErrors(prev => ({
                ...prev,
                responseText: value.trim() === "" ? "Response cannot be empty" : undefined
            }));
        }

        setResponseText(value);
    };

    const handleRemoveFile = (index: number) => {
        setPreviewUrls(prev => {
            const newUrls = [...prev];
            newUrls.splice(index, 1);
            URL.revokeObjectURL(prev[index]);
            return newUrls;
        });

        setSelectedFiles(prev => {
            const newFiles = [...prev];
            newFiles.splice(index, 1);
            return newFiles;
        });
    };

    const handleSubmit = () => {
        onSubmit(responseText, selectedFiles);
        setResponseText("");
        setSelectedFiles([]);
        setPreviewUrls([]);
    };

    return (
        <Stack
            sx={{
                paddingY: {
                    lg: desktop.contentContainer.paddingY,
                    md: tablet.contentContainer.paddingY,
                    xs: tablet.contentContainer.paddingY,
                },
                gap: "30px"
            }}
        >
            <UserAvatarWithRole
                username={userDetails?.username}
                jobTitle={userDetails?.employee?.jobTitle}
            />
            <Stack flex={1}>
                <Stack sx={{ maxWidth: "700px" }}>
                    <MLInputbox
                        multiline
                        rows={4}
                        label=""
                        placeholder="Type your response..."
                        fullWidth
                        value={responseText}
                        onChange={handleResponseChange}
                        error={!!errors.responseText}
                        helperText={errors.responseText}
                    />
                    <MLTypography variant="caption" color="textSecondary" align="right" sx={{ mt: 1 }}>
                        {responseText.length}/{RESPONSE_MAX_LENGTH} characters
                    </MLTypography>
                </Stack>
                <Stack gap="30px">
                    <Box>
                        <MLButton
                            endIcon={<UploadFile />}
                            component="label"
                            htmlFor="attachment-input"
                        >
                            Add attachments ({selectedFiles.length})
                            <input
                                type="file"
                                multiple
                                hidden
                                id="attachment-input"
                                onChange={handleFileChange}
                                accept=".jpg,.jpeg,.png,.pdf,image/jpeg,image/png,application/pdf"
                            />
                        </MLButton>

                        {(selectedFiles.length > 0 || isAttachmentLoading) && (
                            <PreviewGrid>
                                <ImageList
                                    cols={3}
                                    rowHeight={165}
                                    gap={20}
                                    sx={{
                                        maxHeight: 164 * 2,
                                        overflow: "hidden",
                                        pt: 2,
                                        width: '650px'
                                    }}
                                >
                                    {previewUrls.map((url, index) => {
                                        const file = selectedFiles[index];
                                        if (!file) return null;

                                        return (
                                            <Badge
                                                key={index}
                                                badgeContent={
                                                    <IconButton
                                                        onClick={() => handleRemoveFile(index)}
                                                    >
                                                        <Cancel color="error" />
                                                    </IconButton>
                                                }
                                            >
                                                <ImageListItem>
                                                    {file.type === 'application/pdf' ? (
                                                        <object
                                                            data={url}
                                                            type="application/pdf"
                                                            style={{
                                                                width: '100%',
                                                                height: '165px',
                                                                borderRadius: '4px'
                                                            }}
                                                        >
                                                            <embed
                                                                src={url}
                                                                type="application/pdf"
                                                                style={{
                                                                    width: '100%',
                                                                    height: '100%'
                                                                }}
                                                            />
                                                        </object>
                                                    ) : (
                                                        <img
                                                            src={url}
                                                            alt={`Preview ${index}`}
                                                            style={{
                                                                width: '100%',
                                                                height: '100%',
                                                                objectFit: 'cover'
                                                            }}
                                                        />
                                                    )}
                                                </ImageListItem>
                                            </Badge>
                                        );
                                    })}

                                    {isAttachmentLoading && (
                                        <ImageListItem>
                                            <Box
                                                sx={{
                                                    width: '100%',
                                                    height: '165px',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center'
                                                }}
                                            >
                                                <Loading />
                                            </Box>
                                        </ImageListItem>
                                    )}

                                    {isSubmitting && (
                                        <ImageListItem>
                                            <Loading />
                                        </ImageListItem>
                                    )}
                                </ImageList>
                            </PreviewGrid>
                        )}
                        {errors.file && (
                            <MLTypography color="error" variant="caption" sx={{ mt: 1 }}>
                                {errors.file}
                            </MLTypography>
                        )}
                    </Box>
                    <Stack direction="row" spacing={2}>
                        <MLButton
                            variant="contained"
                            onClick={handleSubmit}
                            disabled={!responseText.trim() || isSubmitting}
                        >
                            Submit Response
                        </MLButton>
                        {/* <MLButton variant="outlined">
                            Save Draft
                        </MLButton> */}
                    </Stack>
                </Stack>
            </Stack>
        </Stack>
    );
};

export default ResponseForm;