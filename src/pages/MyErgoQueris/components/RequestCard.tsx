import { Box, Stack, useTheme, useMediaQuery } from '@mui/material';
import CustomRightArrow from '../../../assets/icons/CustomRightArrow';
import MLCard from '../../../components/ui/MLCard/MLCard';
import { Priority, Sender, Status } from '../MyErgoQueris';
import { Link } from 'react-router-dom';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import UserAvatarWithRole from './UserAvatarWithRole';
import PriorityDisplay from './PriorityDisplay';
import StatusBadge from './StatusBadge';
import User from '../../../models/User';
import AssigneeSelector from './AssigneeSelector';


interface RequestCardProps {
    id: number;
    title: string;
    description: string;
    priority: Priority;
    status: Status;
    responsesCount: number;
    attachmentsCount: number;
    lastUpdated: string;
    loggedUser?: User;
    sender?: Sender;
    assignedTo?: User;
}

interface OrganizationLogoProps {
    name?: string;
    logoUrl?: string;
}

const OrganizationLogo: React.FC<OrganizationLogoProps> = ({ name, logoUrl }) => (
    <Stack direction="row" alignItems="center" spacing={1}>
        <Box
            component="img"
            sx={{
                width: { xs: '36px', sm: '44px' },
                height: { xs: '36px', sm: '44px' },
                borderRadius: '50%',
                backgroundColor: '#9C9C9C',
                objectFit: 'contain',
            }}
            src={logoUrl || "/api/placeholder/24/24"}
            alt={name}
        />
        <MLTypography fontWeight={400} sx={{ fontSize: { xs: '14px', sm: '16px' } }}>
            {name}
        </MLTypography>
    </Stack>
);

const RequestCard: React.FC<RequestCardProps> = ({
    id,
    title,
    description,
    priority,
    status,
    responsesCount,
    attachmentsCount,
    lastUpdated,
    loggedUser,
    sender,
    assignedTo
}) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const role = loggedUser?.role.name || "employee";

    return (
        <MLCard>
            <Stack gap="15px">
                {/* Title and Arrow Section */}
                <Stack
                    direction="row"
                    justifyContent="space-between"
                    alignItems={isMobile ? "flex-start" : "center"}
                    spacing={1}
                >
                    <Stack
                        direction={isMobile ? "column" : "row"}
                        gap={isMobile ? "8px" : "15px"}
                        alignItems={isMobile ? "flex-start" : "center"}
                        sx={{ maxWidth: isMobile ? "85%" : "90%" }}
                    >
                        <MLTypography
                            variant="subtitle1"
                            fontFamily="Work Sans"
                            sx={{
                                fontWeight: 600,
                                fontSize: { xs: '18px', sm: '20px' },
                                lineHeight: { xs: "22px", sm: "24px" },
                                wordBreak: "break-word"
                            }}
                        >
                            {title}
                        </MLTypography>
                        <StatusBadge status={status} />
                    </Stack>

                    <Link
                        to={`/requestdetailview/${id}`}
                        state={{ role: role }}
                        style={{ alignSelf: isMobile ? "flex-start" : "center" }}
                    >
                        <CustomRightArrow />
                    </Link>
                </Stack>

                {/* Description Section */}
                <MLTypography
                    variant='body1'
                    fontFamily="Work Sans"
                    sx={{
                        fontWeight: 400,
                        fontSize: { xs: '14px', sm: '16px' },
                        lineHeight: 1.5,
                        textAlign: 'left',
                        overflowWrap: 'break-word',
                        maxWidth: "100%"
                    }}
                >
                    {description}
                </MLTypography>

                {/* Metadata Section */}
                <Stack
                    direction={isMobile ? "column" : "row"}
                    justifyContent="space-between"
                    alignItems={isMobile ? "flex-start" : "center"}
                    spacing={isMobile ? 1 : 0}
                >
                    {/* <Stack direction="row" alignItems="center" mb={isMobile ? 1 : 0}>
                        <PriorityDisplay priority={priority} />
                    </Stack> */}
                    <Stack
                        direction={isMobile ? "column" : "row"}
                        gap={isMobile ? "10px" : "20px"}
                        width={isMobile ? "100%" : "auto"}
                    >
                        <Box display="flex" gap={1} alignItems="center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                                <g clipPath="url(#clip0_13697_33012)">
                                    <path d="M8.5 0.500018C5.96 0.500018 3.62 1.87335 2.38 4.09335C1.14667 6.31335 1.21333 9.02669 2.55333 11.1867L0.5 15.5067L4.81333 13.4534C7.34 15.02 10.58 14.8267 12.8933 12.9667C15.2133 11.1067 16.1 7.98002 15.1067 5.18002C14.1267 2.36668 11.4733 0.493351 8.5 0.500018Z" stroke="#9C9C9C" strokeLinecap="round" strokeLinejoin="round" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_13697_33012">
                                        <rect width="16.0133" height="16" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <MLTypography
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontSize: { xs: '12px', sm: '14px' } }}
                            >
                                {responsesCount} responses
                            </MLTypography>
                        </Box>
                        <Box display="flex" gap={1} alignItems="center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                                <g clipPath="url(#clip0_13697_33019)">
                                    <path d="M0.513306 1.5C0.513306 0.946667 0.959972 0.5 1.51331 0.5H14.5133C15.0666 0.5 15.5133 0.946667 15.5133 1.5V14.5C15.5133 15.0533 15.0666 15.5 14.5133 15.5H1.51331C0.959972 15.5 0.513306 15.0533 0.513306 14.5V1.5Z" stroke="#9C9C9C" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M11.0133 7C12.12 7 13.0133 6.10667 13.0133 5C13.0133 3.89333 12.12 3 11.0133 3C9.90664 3 9.01331 3.89333 9.01331 5C9.01331 6.10667 9.90664 7 11.0133 7Z" stroke="#9C9C9C" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M2.65332 9.97399C4.03332 9.18733 5.68665 9.04066 7.18665 9.57399C8.68665 10.1073 9.87332 11.2607 10.4533 12.7407" stroke="#9C9C9C" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M9.59998 11.2809C10.6933 10.2809 12.36 10.2476 13.5 11.2009" stroke="#9C9C9C" strokeLinecap="round" strokeLinejoin="round" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_13697_33019">
                                        <rect width="16" height="16" fill="white" transform="translate(0.0133057)" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <MLTypography
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontSize: { xs: '12px', sm: '14px' } }}
                            >
                                {attachmentsCount} attachments
                            </MLTypography>
                        </Box>
                        <Stack direction="row" gap={1} alignItems="center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                                <g clipPath="url(#clip0_13991_91841)">
                                    <path d="M8.01354 15.4646C12.138 15.4646 15.4802 12.1224 15.4802 7.99792C15.4802 3.87347 12.138 0.53125 8.01354 0.53125C3.8891 0.53125 0.546875 3.87347 0.546875 7.99792C0.546875 12.1224 3.8891 15.4646 8.01354 15.4646Z" stroke="#9C9C9C" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M8.01343 7.99479V5.32812" stroke="#9C9C9C" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M8.01343 8L11.3485 11.3351" stroke="#9C9C9C" strokeLinecap="round" strokeLinejoin="round" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_13991_91841">
                                        <rect width="16" height="16" fill="white" transform="translate(0.0134277)" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <MLTypography
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontSize: { xs: '12px', sm: '14px' } }}
                            >
                                {lastUpdated}
                            </MLTypography>
                        </Stack>
                    </Stack>
                </Stack>

                {/* Admin/Ergonomist Section */}
                {(role === "Ergonomist" || role === "Ergo ambassador" || role === "Administrator") && (
                    <Box>
                        <Stack gap="15px">
                            <Box borderBottom="0.5px solid #9C9C9C" />
                            <Stack
                                direction={isMobile ? "column" : "row"}
                                alignItems={isMobile ? "flex-start" : "center"}
                                gap={isMobile ? 2 : 2}
                                spacing={0}
                            >
                                <UserAvatarWithRole
                                    username={sender?.username}
                                    jobTitle={sender?.employee.jobTitle}
                                />

                                {!isMobile && (
                                    <Box sx={{ height: '34px', borderLeft: '0.5px solid #9C9C9C', mx: 2 }} />
                                )}

                                {isMobile && (
                                    <Box sx={{ width: '100%', borderTop: '0.5px solid #9C9C9C', my: 1 }} />
                                )}

                                <OrganizationLogo
                                    name={sender?.organization.name}
                                    logoUrl={sender?.organization.logo.url}
                                />
                            </Stack>
                            <Box width="100%">
                                <MLTypography sx={{
                                    fontSize: { xs: "14px", sm: "16px" },
                                    fontWeight: 600
                                }}>
                                    Assign query
                                </MLTypography>
                                <Box mt={1} width="100%">
                                    <AssigneeSelector
                                        requestId={id}
                                        loggedUser={loggedUser}
                                        currentAssignee={assignedTo}
                                        status={status}
                                    />
                                </Box>
                            </Box>
                        </Stack>
                    </Box>
                )}
            </Stack>
        </MLCard>
    );
};

export default RequestCard;