import { Box, Stack } from "@mui/material";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";

interface UserAvatarWithInfoProps {
    username?: string;
    jobTitle?: string;
    avatarUrl?: string;
    showOnlineStatus?: boolean;
}

const UserAvatarWithRole: React.FC<UserAvatarWithInfoProps> = ({
    username = '',
    jobTitle,
    avatarUrl,
    showOnlineStatus = false,
}) => {
    // Get initials from username
    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 1);
    };

    // Generate a consistent color based on username
    const getAvatarColor = (name: string) => {
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
            '#AD9D9D', '#7856FF', '#6C63FF', '#FF9A9E'
        ];
        const index = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        return colors[index % colors.length];
    };

    return (
        <Stack direction="row" sx={{ gap: "15px", alignItems: "center" }}>
            <Stack position="relative">
                {avatarUrl ? (
                    <Box
                        component="img"
                        sx={{
                            backgroundColor: "#fcfcfc",
                            borderRadius: "50%",
                            overflow: "hidden",
                            width: "2rem",
                            height: "2rem",
                            objectFit: "cover",
                        }}
                        src={avatarUrl}
                        alt={username}
                    />
                ) : (
                    <Box
                        sx={{
                            borderRadius: "50%",
                            overflow: "hidden",
                            width: "2rem",
                            height: "2rem",
                            backgroundColor: getAvatarColor(username),
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <MLTypography
                            sx={{
                                color: "white",
                                fontWeight: 600,
                                lineHeight: 1,
                            }}
                        >
                            {getInitials(username)}
                        </MLTypography>
                    </Box>
                )}
                {showOnlineStatus && (
                    <Box
                        sx={{
                            position: "absolute",
                            border: "2px solid white",
                            backgroundColor: "#62E200",
                            borderRadius: "50%",
                            width: "0.60rem",
                            height: "0.60rem",
                            bottom: "0",
                            right: "0",
                        }}
                    />
                )}
            </Stack>
            <Stack>
                <MLTypography variant="subtitle1" fontWeight={400}>
                    {username || ""}
                </MLTypography>
                <MLTypography color="text.secondary" variant="body2">
                    {jobTitle || ""}
                </MLTypography>
            </Stack>
        </Stack>
    );
};

export default UserAvatarWithRole;