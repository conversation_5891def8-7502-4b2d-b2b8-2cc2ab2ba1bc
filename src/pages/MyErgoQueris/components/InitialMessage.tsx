import React from 'react';
import { Stack, Box } from '@mui/material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import { desktop, tablet } from '../../../responsiveStyles';
import { ParagraphNode } from '../MyErgoQueris';
import UserAvatarWithRole from './UserAvatarWithRole';
import { styled } from '@mui/system';

interface InitialMessageProps {
    sender: any;
    createdOn: string | null;
    description: ParagraphNode[];
    attachments?: Array<{ url: string; name: string; mime?: string }> | null;
    onOpenPreview: (attachments: any[], startIndex: number) => void;
}

const InitialMessage = ({
    sender,
    createdOn,
    description,
    attachments,
    onOpenPreview
}: InitialMessageProps) => {
    const messageFormatDate = (dateString: string | null) => {
        if (!dateString) return "NA";

        const date = new Date(dateString);
        const months = [
            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
        ];

        const weekday = date.toLocaleDateString('en-US', { weekday: 'long' });
        const month = months[date.getMonth()];
        const day = date.getDate();
        const year = date.getFullYear();

        let hours = date.getHours();
        const minutes = date.getMinutes();
        const ampm = hours >= 12 ? 'PM' : 'AM';

        hours = hours % 12;
        hours = hours ? hours : 12;

        const timeStr = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;

        return `${weekday}, ${month} ${day}, ${year} | Sent on ${timeStr}`;
    };

    const getDescriptionText = (paragraphs: ParagraphNode[]): string => {
        if (!paragraphs) return "";
        return paragraphs.map(p => p.children.map(c => c.text).join('')).join('\n');
    };

    const AttachmentPreviewGrid = styled(Box)({
        width: "50%",
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: '25px',
    });

    const renderAttachmentPreview = (attachment: any, index: number, attachments: any[]) => (
        <Box
            key={attachment.url}
            onClick={() => onOpenPreview(attachments, index)}
            sx={{
                cursor: 'pointer',
                width: '100%',
                height: '220px',
                position: 'relative',
                borderRadius: '4px',
                overflow: 'hidden'
            }}
        >
            {attachment.mime === 'application/pdf' ? (
                <object
                    data={attachment.url}
                    type="application/pdf"
                    style={{
                        width: '100%',
                        height: '220px',
                        borderRadius: '4px',
                        pointerEvents: 'none'
                    }}
                >
                    <embed
                        src={attachment.url}
                        type="application/pdf"
                        style={{
                            width: '100%',
                            height: '100%',
                            pointerEvents: 'none'
                        }}
                    />
                </object>
            ) : (
                <img
                    style={{
                        width: '100%',
                        height: '220px',
                        objectFit: 'cover'
                    }}
                    src={attachment.url}
                    alt={attachment.name}
                />
            )}
        </Box>
    );

    return (
        <Stack
            sx={{
                
                paddingBottom: "30px",
                gap: "30px"
            }}
        >
            <Stack direction="row" justifyContent="space-between" alignItems="center">
                <UserAvatarWithRole
                    username={sender?.username}
                    jobTitle={sender?.employee?.jobTitle}
                />
                <MLTypography color="text.secondary">
                    {messageFormatDate(createdOn)}
                </MLTypography>
            </Stack>

            <Stack direction="row" spacing={2}>
                <Stack flex={1} gap="30px">
                    <MLTypography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                        {getDescriptionText(description)}
                    </MLTypography>

                    {attachments && attachments.length > 0 && (
                        <Box>
                            <Stack direction="row" spacing={1} mb={2}>
                                <MLTypography variant="subtitle1" fontWeight={600}>
                                    {attachments.length} attachments
                                </MLTypography>
                            </Stack>
                            <AttachmentPreviewGrid>
                                {attachments.map((attachment, index) =>
                                    renderAttachmentPreview(attachment, index, attachments)
                                )}
                            </AttachmentPreviewGrid>
                        </Box>
                    )}
                </Stack>
            </Stack>
        </Stack>
    );
};

export default InitialMessage;