import React from 'react';
import { Box, Stack } from '@mui/material';
import { styled } from '@mui/material/styles';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import { Priority } from "../MyErgoQueris";

export const PriorityIcon = styled('svg')({
    width: '20px',
    height: '20px',
    marginRight: '8px',
});

// Priority Icons Component
export const PriorityIcons: Record<Priority, JSX.Element> = {
    high: (
        <PriorityIcon xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 27" fill="none">
            <path
                d="M14.5629 4.27472C14.4332 4.04002 14.243 3.84437 14.0121 3.70812C13.7812 3.57186 13.518 3.5 13.2499 3.5C12.9817 3.5 12.7185 3.57186 12.4876 3.70812C12.2567 3.84437 12.0665 4.04002 11.9369 4.27472L2.18685 22.2747C2.06076 22.5031 1.99642 22.7604 2.00015 23.0212C2.00389 23.282 2.07559 23.5374 2.20816 23.762C2.34074 23.9867 2.5296 24.1729 2.75613 24.3023C2.98265 24.4316 3.23899 24.4997 3.49985 24.4997H22.9999C23.2607 24.4997 23.5171 24.4316 23.7436 24.3023C23.9701 24.1729 24.159 23.9867 24.2916 23.762C24.4241 23.5374 24.4958 23.282 24.4996 23.0212C24.5033 22.7604 24.4389 22.5031 24.3129 22.2747L14.5629 4.27472Z"
                fill="#C40000"
            />
            <path d="M13.25 17V10.25" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M13.25 20.75C13.0429 20.75 12.875 20.5821 12.875 20.375C12.875 20.1679 13.0429 20 13.25 20" stroke="white" strokeWidth="1.5" />
            <path d="M13.25 20.75C13.4571 20.75 13.625 20.5821 13.625 20.375C13.625 20.1679 13.4571 20 13.25 20" stroke="white" strokeWidth="1.5" />
        </PriorityIcon>
    ),
    medium: (
        <PriorityIcon xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 27" fill="none">
            <path
                d="M14.5629 4.27472C14.4332 4.04002 14.243 3.84437 14.0121 3.70812C13.7812 3.57186 13.518 3.5 13.2499 3.5C12.9817 3.5 12.7185 3.57186 12.4876 3.70812C12.2567 3.84437 12.0665 4.04002 11.9369 4.27472L2.18685 22.2747C2.06076 22.5031 1.99642 22.7604 2.00015 23.0212C2.00389 23.282 2.07559 23.5374 2.20816 23.762C2.34074 23.9867 2.5296 24.1729 2.75613 24.3023C2.98265 24.4316 3.23899 24.4997 3.49985 24.4997H22.9999C23.2607 24.4997 23.5171 24.4316 23.7436 24.3023C23.9701 24.1729 24.159 23.9867 24.2916 23.762C24.4241 23.5374 24.4958 23.282 24.4996 23.0212C24.5033 22.7604 24.4389 22.5031 24.3129 22.2747L14.5629 4.27472Z"
                fill="#FF7A00"
            />
            <path d="M13.25 17V10.25" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M13.25 20.75C13.0429 20.75 12.875 20.5821 12.875 20.375C12.875 20.1679 13.0429 20 13.25 20" stroke="white" strokeWidth="1.5" />
            <path d="M13.25 20.75C13.4571 20.75 13.625 20.5821 13.625 20.375C13.625 20.1679 13.4571 20 13.25 20" stroke="white" strokeWidth="1.5" />
        </PriorityIcon>
    ),
    low: (
        <PriorityIcon xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 27" fill="none">
            <path
                d="M14.5629 4.27472C14.4332 4.04002 14.243 3.84437 14.0121 3.70812C13.7812 3.57186 13.518 3.5 13.2499 3.5C12.9817 3.5 12.7185 3.57186 12.4876 3.70812C12.2567 3.84437 12.0665 4.04002 11.9369 4.27472L2.18685 22.2747C2.06076 22.5031 1.99642 22.7604 2.00015 23.0212C2.00389 23.282 2.07559 23.5374 2.20816 23.762C2.34074 23.9867 2.5296 24.1729 2.75613 24.3023C2.98265 24.4316 3.23899 24.4997 3.49985 24.4997H22.9999C23.2607 24.4997 23.5171 24.4316 23.7436 24.3023C23.9701 24.1729 24.159 23.9867 24.2916 23.762C24.4241 23.5374 24.4958 23.282 24.4996 23.0212C24.5033 22.7604 24.4389 22.5031 24.3129 22.2747L14.5629 4.27472Z"
                fill="#FFC700"
            />
            <path d="M13.25 17V10.25" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M13.25 20.75C13.0429 20.75 12.875 20.5821 12.875 20.375C12.875 20.1679 13.0429 20 13.25 20" stroke="white" strokeWidth="1.5" />
            <path d="M13.25 20.75C13.4571 20.75 13.625 20.5821 13.625 20.375C13.625 20.1679 13.4571 20 13.25 20" stroke="white" strokeWidth="1.5" />
        </PriorityIcon>
    ),
};

// PriorityDisplay Component
interface PriorityDisplayProps {
    priority: Priority;
}

const PriorityDisplay: React.FC<PriorityDisplayProps> = ({ priority }) => {
    return (
        <Stack direction="row" alignItems="center">
            {PriorityIcons[priority]}
            <MLTypography>
                {priority.charAt(0).toUpperCase() + priority.slice(1)} Priority
            </MLTypography>
        </Stack>
    );
};

export default PriorityDisplay