import React, { useState, useEffect } from 'react';
import { Box, Popover, Stack } from '@mui/material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../components/ui/MLButton/MLButton';
import MLCheckbox from '../../../components/ui/MLCheckbox/MLCheckbox';
import ViewMoreDownIcon from '../../../assets/icons/ViewMoreDownIcon';
import CloseIcon from '../../../assets/icons/CloseIcon';
import { useList, useUpdate } from '@refinedev/core';
import User from '../../../models/User';

interface AssigneeSelectorProps {
    requestId: number;
    loggedUser?: User;
    currentAssignee?: User | null;
    buttonStyle?: "normal" | "compact";
    status: string
}

export const AssigneeSelector: React.FC<AssigneeSelectorProps> = ({
    requestId,
    loggedUser,
    currentAssignee: propCurrentAssignee,
    buttonStyle = "normal",
    status
}) => {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [currentAssignee, setCurrentAssignee] = useState<User | null>(null);
    const [tempSelectedAssignee, setTempSelectedAssignee] = useState<User | null>(null);
    const [isAssigning, setIsAssigning] = useState(false);
    const { mutate: updateRequest } = useUpdate();
    // Initialize or update state when props change
    useEffect(() => {
        if (propCurrentAssignee) {
            setCurrentAssignee(propCurrentAssignee);
            setTempSelectedAssignee(propCurrentAssignee);
        }
    }, [propCurrentAssignee]);

    const { data: usersData, isLoading: isLoadingUsers } = useList<User>({
        resource: "users",
        filters: [
            {
                field: "organization.id",
                operator: "eq",
                value: loggedUser?.organization?.id!
            },
            {
                field: "role.name",
                operator: "in",
                value: ["Ergonomist", "Ergo ambassador", "Administrator"]
            }
        ],
        pagination: {
            pageSize: 100
        },
        queryOptions: {
            enabled: !!loggedUser && loggedUser.role.name !== "Employee",
            staleTime: 0,
        }
    });

    const sortedUsers = !usersData?.data ? [] : [...usersData.data].sort((a, b) => {
        if (a.email === loggedUser?.email) return -1;
        if (b.email === loggedUser?.email) return 1;
        return a.username.localeCompare(b.username);
    });

    const handleAssign = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
        setTempSelectedAssignee(currentAssignee);
    };

    const handleClose = () => {
        setAnchorEl(null);
        setTempSelectedAssignee(currentAssignee);
    };

    const handleAssigneeSelect = (user: User) => {
        setTempSelectedAssignee(user);
    };

    const handleAssignAssignee = () => {
        if (tempSelectedAssignee) {
            setIsAssigning(true);
            setAnchorEl(null);

            updateRequest({
                resource: "ep-ergo-requests",
                id: requestId,
                values: {
                    assignedTo: tempSelectedAssignee.id,
                    status: "processing"  // Set to processing when assigned, pending when unassigned
                },
                successNotification: {
                    message: loggedUser?.email === tempSelectedAssignee.email
                        ? 'Request assigned to yourself successfully!'
                        : 'Request assigned successfully!',
                    type: 'success'
                },
                errorNotification: (error) => ({
                    message: `Error assigning request: ${error?.message}`,
                    type: 'error'
                })
            },
                {
                    onSuccess: () => {
                        setCurrentAssignee(tempSelectedAssignee);
                        setIsAssigning(false);
                    },
                    onError: () => {
                        setIsAssigning(false);
                    }
                });
        }
    };

    const open = Boolean(anchorEl);

    const getButtonText = () => {
        if (isLoadingUsers) return 'Loading...';
        if (isAssigning) return 'Assigning...';
        if (!currentAssignee) return 'Select';
        return loggedUser?.email === currentAssignee.email
            ? 'Assigned to yourself'
            : `Assigned to ${currentAssignee.username}`;
    };

    return (
        <>
            <MLButton
                endIcon={!isAssigning && status !== "completed"&& <ViewMoreDownIcon />}
                variant="outlined"
                onClick={handleAssign}
                disabled={isAssigning || status === "completed"}
                sx={{
                    ...(buttonStyle === "normal" ? { paddingX: 8 } : { minWidth: 'auto' })
                }}
            >
                {getButtonText()}
            </MLButton>

            <Popover
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                sx={{
                    "& .MuiPopover-paper": {
                        borderRadius: "10px",
                        padding: "20px 30px"
                    },
                }}
            >
                <Stack spacing={2} sx={{ minWidth: '300px' }}>
                    <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="center"
                    >
                        <MLTypography sx={{
                            fontSize: "16px",
                            fontWeight: 600
                        }}>
                            Assign query
                        </MLTypography>
                        <MLButton
                            onClick={handleClose}
                            sx={{
                                minWidth: 'auto',
                                p: 1
                            }}
                        >
                            <CloseIcon />
                        </MLButton>
                    </Stack>

                    <Box sx={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        gap: 1,
                        mt: 1,
                        maxHeight: '300px',
                        overflowY: 'auto'
                    }}>
                        {sortedUsers.map((user) => (
                            <Box
                                key={user.id}
                                onClick={() => handleAssigneeSelect(user)}
                                sx={{
                                    cursor: 'pointer',
                                    display: 'flex',
                                    alignItems: 'center',
                                    '&:hover': { backgroundColor: '#F8F8FF' },
                                    ...(tempSelectedAssignee?.id === user.id && {
                                        backgroundColor: '#F8F8FF',
                                    }),
                                    borderRadius: '8px',
                                    padding: '4px'
                                }}
                            >
                                <MLCheckbox
                                    name={`assignee-${user.id}`}
                                    checked={tempSelectedAssignee?.id === user.id}
                                    onChange={() => handleAssigneeSelect(user)}
                                />
                                <MLTypography>
                                    {loggedUser?.email === user.email ?
                                        "Assign to yourself"
                                        : user.username
                                    }
                                </MLTypography>
                            </Box>
                        ))}
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', mt: 2 }}>
                        <MLButton
                            variant="contained"
                            onClick={handleAssignAssignee}
                            sx={{ paddingX: 6 }}
                            disabled={!tempSelectedAssignee || isAssigning}
                        >
                            {isAssigning ? 'Assigning...' : 'Assign'}
                        </MLButton>
                    </Box>
                </Stack>
            </Popover>
        </>
    );
};

export default AssigneeSelector;