import React, { useEffect, useState } from 'react';
import { Box, IconButton, Dialog, Stack } from '@mui/material';
import { styled } from '@mui/material/styles';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../components/ui/MLButton/MLButton';
import { ArrowLeft, ArrowRight, Close, Download } from '@mui/icons-material';

// Styled components to match your CSS classes
const SliderContainer = styled(Box)({
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100vw',
    height: '100vh',
    backgroundColor: 'rgba(0, 0, 0, 0.553)',
    zIndex: 999999,
    display: 'flex',
    alignItems: 'center'
});

const SliderWrapper = styled(Box)({
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
});

const SliderImage = styled('img')({
    width: '50%',
    height: '80vh',
    objectFit: 'contain'
});

const PDFContainer = styled('object')({
    width: '50%',
    height: '80vh',
    backgroundColor: 'white'
});

const SliderContent = styled(Box)({
    width: '50%',
    height: '80vh',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    '& img, & iframe': {
        width: '100%',
        height: '100%',
        objectFit: 'contain',
        border: 'none',
        borderRadius: '4px',
        maxWidth: '100%',
        maxHeight: '100%'
    }
});

interface Attachment {
    url: string;
    name: string;
    mime?: string;
}

interface AttachmentPreviewModalProps {
    open: boolean;
    onClose: () => void;
    attachments: Attachment[];
    initialIndex: number;
}


const AttachmentPreviewModal = ({
    open,
    onClose,
    attachments,
    initialIndex
}: AttachmentPreviewModalProps) => {
    if (!open) return null;
    const [currentIndex, setCurrentIndex] = useState(initialIndex);
    const currentAttachment = attachments[currentIndex];


    const handleNext = (e: React.MouseEvent) => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev + 1) % attachments.length);
    };

    const handlePrevious = (e: React.MouseEvent) => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev - 1 + attachments.length) % attachments.length);
    };

    // const handleDownload = async () => {
    //     try {
    //         const response = await fetch(currentAttachment.url);
    //         const blob = await response.blob();
    //         const url = window.URL.createObjectURL(blob);
    //         const link = document.createElement('a');
    //         link.href = url;
    //         link.download = currentAttachment.name;
    //         document.body.appendChild(link);
    //         link.click();
    //         document.body.removeChild(link);
    //         window.URL.revokeObjectURL(url);
    //     } catch (error) {
    //         console.error('Error downloading file:', error);
    //     }
    // };
    const handleDownload = async () => {
        try {
            console.log('Fetching file from:', currentAttachment.url);
            const response = await fetch(currentAttachment.url, {
                method: 'GET',
            });

            console.log('response: ', response);
            if (!response.ok) {
                throw new Error(`Failed to fetch file. Status: ${response.status}`);
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = currentAttachment.name || 'download';  // Provide a fallback filename
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url); // Clean up after download

            console.log('Download initiated for file:', currentAttachment.name);
        } catch (error) {
            console.error('Error downloading file:', error);
            alert('There was an error downloading the file. Please try again later.');
        }
    };


    const isPDF = currentAttachment?.mime === 'application/pdf';

    return (
        <SliderContainer>
            {/* Header */}
            <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    p: 2,
                    zIndex: 2,
                    backgroundColor: 'rgba(0,0,0,0.2)'
                }}
            >
                <MLTypography color="white">
                    {currentIndex + 1} of {attachments.length}
                </MLTypography>
                <Stack direction="row" mr={2}>
                    <IconButton onClick={handleDownload} sx={{ color: 'white' }}>
                        <Download />
                    </IconButton>
                    <IconButton onClick={onClose} sx={{ color: 'white' }}>
                        <Close />
                    </IconButton>
                </Stack>
            </Stack>

            {/* Navigation buttons */}
            {attachments.length > 1 && (
                <>
                    <IconButton
                        onClick={handlePrevious}
                        sx={{
                            position: 'absolute',
                            left: 16,
                            color: 'white',
                            zIndex: 2
                        }}
                    >
                        <ArrowLeft />
                    </IconButton>
                    <IconButton
                        onClick={handleNext}
                        sx={{
                            position: 'absolute',
                            right: 16,
                            color: 'white',
                            zIndex: 2
                        }}
                    >
                        <ArrowRight />
                    </IconButton>
                </>
            )}

            {/* Content */}
            {/* <SliderWrapper onClick={(e) => e.stopPropagation()}>
                {isPDF ? (
                    <PDFContainer data={currentAttachment.url} type="application/pdf">
                        <embed
                            src={currentAttachment.url}
                            type="application/pdf"
                            style={{ width: '100%', height: '100%' }}
                        />
                    </PDFContainer>
                ) : (
                    <SliderImage
                        src={currentAttachment.url}
                        alt={currentAttachment.name}
                    />
                )}
            </SliderWrapper> */}
            <SliderWrapper onClick={(e) => e.stopPropagation()}>
                <Box
                    sx={{
                        width: '100%',
                        height: '80vh',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        '& img, & iframe': {
                            width: '100%',
                            height: '100%',
                            objectFit: 'contain',
                            border: 'none',
                            borderRadius: '4px',
                            maxWidth: '100%',
                            maxHeight: '100%'
                        }
                    }}
                >
                    {isPDF ? (
                        <iframe
                            src={`${currentAttachment.url}#toolbar=0`}
                            title={currentAttachment.name}
                            style={{
                                backgroundColor: 'white',
                            }}
                        />
                    ) : (
                        <img
                            src={currentAttachment.url}
                            alt={currentAttachment.name}
                        />
                    )}
                </Box>
            </SliderWrapper>

            {/* Footer */}
            <Box
                sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    p: 2,
                    textAlign: 'center',
                    backgroundColor: 'rgba(0,0,0,0.2)'
                }}
            >
                <MLTypography color="white">
                    {currentAttachment.name}
                </MLTypography>
            </Box>
        </SliderContainer>
    );
};

export default AttachmentPreviewModal;