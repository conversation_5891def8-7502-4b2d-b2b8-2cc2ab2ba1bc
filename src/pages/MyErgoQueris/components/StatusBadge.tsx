import React from 'react';
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import { Status } from '../MyErgoQueris';

// Styled Components
export const BadgeWrapper = styled(Box)({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '4px 7px',
    borderRadius: "5px",
    border: "0.5px solid #9c9c9c",
    fontSize: '14px',
    fontWeight: 500,
});


const StatusIcons = {
    pending: (
        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="16" viewBox="0 0 13 16" fill="none">
            <g clipPath="url(#clip0_13991_39801)">
                <path d="M2.10564 3.66541C2.49352 5.79874 3.84416 7.26021 6.23378 8.00134C8.62339 7.26021 9.98096 5.79874 10.3619 3.66541C10.4104 3.3745 10.3204 3.08359 10.1195 2.86195C9.91862 2.6403 9.63464 2.51562 9.32988 2.51562H3.13767C2.83291 2.51562 2.542 2.6403 2.34806 2.86195C2.1472 3.08359 2.06408 3.3745 2.10564 3.66541Z" stroke="#FF7A00" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M0.519531 0.519531H11.9481" stroke="#FF7A00" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M3.13767 13.4857C2.83291 13.4857 2.542 13.361 2.34806 13.1394C2.1472 12.9177 2.06408 12.6268 2.10564 12.3359C2.49352 10.2026 3.84416 8.74113 6.23378 8C8.62339 8.74113 9.98096 10.2026 10.3619 12.3359C10.4104 12.6268 10.3204 12.9177 10.1195 13.1394C9.91862 13.361 9.63464 13.4857 9.32988 13.4857H3.13767Z" stroke="#FF7A00" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M0.519531 15.4805H11.9481" stroke="#FF7A00" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6.23361 6.54059C6.98361 6.51412 7.6612 6.07471 8.00775 5.39706C8.0543 5.31765 8.0543 5.21176 8.00775 5.13235C7.9612 5.05294 7.87844 5 7.78534 5H4.68189C4.58878 5 4.50603 5.05294 4.45947 5.13235C4.41292 5.21706 4.41292 5.31765 4.45947 5.39706C4.80603 6.08 5.48361 6.51412 6.23361 6.54059Z" fill="#FF7A00" />
                <path d="M6.62151 9.69676C6.51806 9.58558 6.37841 9.52734 6.23358 9.52734C6.08875 9.52734 5.9491 9.59087 5.84565 9.69676L4.12841 11.682C3.99393 11.8356 3.96289 12.0579 4.04565 12.2485C4.12841 12.4391 4.31462 12.5609 4.51634 12.5609H7.96117C8.16289 12.5609 8.3491 12.4391 8.43186 12.2485C8.51462 12.0579 8.48358 11.8356 8.3491 11.682L6.62669 9.69676H6.62151Z" fill="#FF7A00" />
            </g>
            <defs>
                <clipPath id="clip0_13991_39801">
                    <rect width="12.4675" height="16" fill="white" />
                </clipPath>
            </defs>
        </svg>
    ),
    processing: (
        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
            <path d="M8.51471 3.97266C11.2805 3.97266 13.5251 6.21733 13.5251 8.98309C13.5251 11.7489 11.2805 13.9935 8.51471 13.9935C5.74895 13.9935 3.50427 11.7489 3.50427 8.98309" stroke="#FFC700" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M15.83 10.6992C15.6763 11.3606 15.4292 12.0019 15.1018 12.6032" stroke="#FFC700" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M13.7389 14.3789C13.2446 14.8465 12.6967 15.2541 12.1022 15.5881" stroke="#FFC700" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M9.18274 1.5C9.86416 1.56013 10.5322 1.71378 11.1669 1.95428" stroke="#FFC700" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M13.1177 3.04297C13.6588 3.45716 14.1398 3.95153 14.5406 4.49934" stroke="#FFC700" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M15.5961 6.47656C15.8299 7.1179 15.9702 7.78596 16.0036 8.46738" stroke="#FFC700" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M10.0178 16.344C5.94935 17.1724 1.98108 14.5469 1.15269 10.4851C0.324298 6.41662 2.94977 2.44167 7.01156 1.61328" stroke="#FFC700" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    ),
    completed: (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M9 17C13.416 17 17 13.416 17 9C17 4.584 13.416 1 9 1C4.584 1 1 4.584 1 9C1 13.416 4.584 17 9 17Z" fill="#31C100" stroke="#31C100" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M4.73364 9.86501L6.47586 12.3397C6.61098 12.5388 6.83142 12.6597 7.0732 12.6739C7.31498 12.681 7.54253 12.5743 7.69187 12.3823L13.267 5.32812" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    ),
};

const StatusBadge: React.FC<{ status: Status }> = ({ status }) => {
   return <BadgeWrapper>
        {StatusIcons[status]}
        <MLTypography component="span" sx={{ textTransform: 'capitalize' }}>
            {status}
        </MLTypography>
    </BadgeWrapper>
};

export default StatusBadge;