import React from 'react';
import { Stack, Box, styled } from '@mui/material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import { desktop, tablet } from '../../../responsiveStyles';
import { ParagraphNode, Thread } from '../MyErgoQueris';
import UserAvatarWithRole from './UserAvatarWithRole';

const AttachmentPreviewGrid = styled(Box)({
    width: "50%",
    display: 'grid',
    gridTemplateColumns: 'repeat(3, 1fr)',
    gap: '25px',
});

const ExpertResponseBadge = styled(Box)({
    borderRadius: '5px',
    background: 'var(--Colors-Coral-100, #FF6E6E)',
    padding: '5px 10px',
    color: 'white',
    fontSize: '12px',
    fontWeight: '500'
});

interface ThreadMessageProps {
    thread: Thread;
    onOpenPreview: (attachments: any[], startIndex: number) => void;
}

const ThreadMessage = ({ thread, onOpenPreview }: ThreadMessageProps) => {
    const messageFormatDate = (dateString: string) => {
        if (!dateString) return null;

        const date = new Date(dateString);
        const months = [
            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
        ];

        const weekday = date.toLocaleDateString('en-US', { weekday: 'long' });
        const month = months[date.getMonth()];
        const day = date.getDate();
        const year = date.getFullYear();

        let hours = date.getHours();
        const minutes = date.getMinutes();
        const ampm = hours >= 12 ? 'PM' : 'AM';

        hours = hours % 12;
        hours = hours ? hours : 12;

        const timeStr = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;

        return `${weekday}, ${month} ${day}, ${year} | Sent on ${timeStr}`;
    };

    const getDescriptionText = (paragraphs: ParagraphNode[]): string => {
        if (!paragraphs) return "";
        return paragraphs.map(p => p.children.map(c => c.text).join('')).join('\n');
    };

    const renderAttachmentPreview = (attachment: any, index: number, attachments: any[]) => (
        <Box
            key={attachment.url}
            onClick={() => onOpenPreview(attachments, index)}
            sx={{
                cursor: 'pointer',
                width: '100%',
                height: '220px',
                position: 'relative',
                borderRadius: '4px',
                overflow: 'hidden'
            }}
        >
            {attachment.mime === 'application/pdf' ? (
                <object
                    data={attachment.url}
                    type="application/pdf"
                    style={{
                        width: '100%',
                        height: '220px',
                        borderRadius: '4px',
                        pointerEvents: 'none'
                    }}
                >
                    <embed
                        src={attachment.url}
                        type="application/pdf"
                        style={{
                            width: '100%',
                            height: '100%',
                            pointerEvents: 'none'
                        }}
                    />
                </object>
            ) : (
                <img
                    style={{
                        width: '100%',
                        height: '220px',
                        objectFit: 'cover'
                    }}
                    src={attachment.url}
                    alt={attachment.name}
                />
            )}
        </Box>
    );

    return (
        <Stack gap="30px" sx={{
            paddingY: "30px",
        }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" gap={2}>
                <Stack direction="row" alignItems="center" gap="24px">
                    <UserAvatarWithRole
                        username={thread.sender?.username}
                        jobTitle={thread.sender?.employee?.jobTitle}
                    />
                    {thread.sender?.role?.name !== "Employee" && (
                        <ExpertResponseBadge>
                            Expert Response
                        </ExpertResponseBadge>
                    )}
                </Stack>
                <MLTypography color="text.secondary">
                    {messageFormatDate(thread.sentAt)}
                </MLTypography>
            </Stack>

            <MLTypography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                {getDescriptionText(thread.content)}
            </MLTypography>

            {thread.attachments && thread.attachments.length > 0 && (
                <Stack gap="18px">
                    <MLTypography variant="subtitle1" fontWeight={600}>
                        {thread.attachments.length} attachments
                    </MLTypography>
                    <AttachmentPreviewGrid>
                        {thread.attachments.map((attachment, index) =>
                            renderAttachmentPreview(attachment, index, thread.attachments || [])
                        )}
                    </AttachmentPreviewGrid>
                </Stack>
            )}
        </Stack>
    );
};

export default ThreadMessage;