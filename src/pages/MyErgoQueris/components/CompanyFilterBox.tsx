import {
    <PERSON>over,
    Stack,
    IconButton,
    Box,
    FormControl,
    FormGroup,
    FormControlLabel
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { ErFilter } from '@mindlens/ergo-icons';
import { useState } from 'react';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLCheckbox from '../../../components/ui/MLCheckbox/MLCheckbox';
import MLButton from '../../../components/ui/MLButton/MLButton';

interface Company {
    id: string;
    name: string;
}

interface CompanyFilterBoxProps {
    companies: Company[];
    selectedCompanies: string[];
    onCompaniesChange: (companies: string[]) => void;
    onClearFilters: () => void;
}

const CompanyFilterBox: React.FC<CompanyFilterBoxProps> = ({
    companies,
    selectedCompanies,
    onCompaniesChange,
    onClearFilters
}) => {
    const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
    const [stagedCompanies, setStagedCompanies] = useState<string[]>(selectedCompanies);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
        setStagedCompanies(selectedCompanies);
    };

    const handleClose = () => {
        setAnchorEl(null);
        setStagedCompanies(selectedCompanies);
    };

    const handleCompanySelection = (companyId: string) => {
        setStagedCompanies(prev =>
            prev.includes(companyId)
                ? prev.filter(id => id !== companyId)
                : [...prev, companyId]
        );
    };

    const handleSelectAll = () => {
        if (stagedCompanies.length === companies.length) {
            setStagedCompanies([]);
        } else {
            setStagedCompanies(companies.map(company => company.id));
        }
    };

    const handleApplyFilters = () => {
        onCompaniesChange(stagedCompanies);
        setAnchorEl(null);
    };

    const handleClear = () => {
        setStagedCompanies([]);
        onClearFilters();
    };

    const open = Boolean(anchorEl);
    const id = open ? 'company-filter-popover' : undefined;

    return (
        <Box>
            <Stack>
                <IconButton onClick={handleClick}>
                    <ErFilter />
                </IconButton>
                <Popover
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    onClose={handleClose}
                    anchorOrigin={{
                        vertical: "top",
                        horizontal: "right",
                    }}
                    sx={{
                        "& .MuiPopover-paper": {
                            borderRadius: "10px",
                            width: '400px',
                            p: "30px"
                        },
                    }}
                >
                    <Stack spacing={3}>
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <MLTypography
                                sx={{
                                    fontSize: "20px",
                                    fontWeight: 600
                                }}
                            >
                                Filter and sort
                            </MLTypography>
                            <IconButton onClick={handleClose} size="small">
                                <CloseIcon />
                            </IconButton>
                        </Stack>

                        <Stack gap="10px">
                            <MLTypography
                                sx={{
                                    fontSize: '16px',
                                    fontWeight: 500,
                                    lineHeight: '120%',
                                }}
                            >
                                Company
                            </MLTypography>
                            <FormControl component="fieldset" variant="standard">
                                <FormGroup>

                                    <Box sx={{
                                        display: 'grid',
                                        gridTemplateColumns: '1fr 1fr',
                                        gap: 1,
                                        mt: 1
                                    }}>
                                        {companies.map((company) => (
                                            <FormControlLabel
                                                key={company.id}
                                                control={
                                                    <MLCheckbox
                                                        checked={stagedCompanies.includes(company.id)}
                                                        onChange={() => handleCompanySelection(company.id)}
                                                        name={company.name}
                                                    />
                                                }
                                                label={company.name}
                                            />
                                        ))}
                                    </Box>
                                    <FormControlLabel
                                        control={
                                            <MLCheckbox
                                                checked={stagedCompanies.length === companies.length}
                                                onChange={handleSelectAll}
                                                name="selectAll"
                                            />
                                        }
                                        label="Select All"
                                    />
                                </FormGroup>
                            </FormControl>
                        </Stack>

                        <Stack direction="column" justifyContent="center" alignItems="center">
                            <Stack width="fit-content" direction="row" gap="23px">
                                <MLButton
                                    variant="outlined"
                                    onClick={handleClear}
                                >
                                    CLEAR FILTERS
                                </MLButton>
                                <MLButton
                                    variant="contained"
                                    onClick={handleApplyFilters}
                                >
                                    Show Results
                                </MLButton>
                            </Stack>
                        </Stack>
                    </Stack>
                </Popover>
            </Stack>
        </Box>
    );
}

export default CompanyFilterBox