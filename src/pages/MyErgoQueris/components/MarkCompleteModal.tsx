import { Dialog, Stack } from '@mui/material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../components/ui/MLButton/MLButton';

interface MarkCompleteModalProps {
    open: boolean;
    onClose: () => void;
}

const MarkCompleteModal = ({ open, onClose }: MarkCompleteModalProps) => {
    return (
        <Dialog
            open={open}
            onClose={onClose}
            fullWidth
            maxWidth="md"
        >
            <Stack
                direction="column"
                alignItems="center"
                gap={2}
                py={{ xs: 3, sm: 6 }}
                px={{ xs: 4, sm: 8 }}
                flexWrap="nowrap"
                style={{
                    backgroundImage: "url('/background_waves/horizontal1.svg')",
                    backgroundSize: "cover",
                    backgroundAttachment: "fixed",
                }}
            >
                <MLTypography
                    variant="h3"
                    sx={{
                        textAlign: "center"
                    }}
                >
                    Ergo query marked as completed
                </MLTypography>
                <MLTypography 
                    variant="body1" 
                    color="textSecondary" 
                    mb={4}
                >
                    This request has been successfully marked as completed.
                </MLTypography>
                <MLButton
                    variant="contained"
                    color="secondary"
                    onClick={onClose}
                >
                    OK
                </MLButton>
            </Stack>
        </Dialog>
    );
};

export default MarkCompleteModal;