import React, { useState, useMemo } from 'react';
import { TabContext, TabPanel } from '@mui/lab';
import MLTabs from '../../components/ui/MLTabs/MLTabs';
import { formatEnumString } from '../../utils/enumUtils';
import MLTab from '../../components/ui/MLTab/MLTab';
import {
    Box,
    Stack,
    ButtonBase,
} from '@mui/material';
import { desktop, tablet } from '../../responsiveStyles';
import AllQueries from './Tabs/AllQueries';
import { useGetIdentity, useList } from '@refinedev/core';
import { IIdentity } from '../AuthScreens/Profile/Profile';
import Loading from '../Loading/Loading';
import User from '../../models/User';
import MLBanner from '../../components/ui/MLBanner/MLBanner';
import { Organization } from '../../models/Organization';
import MLContainer from '../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer';
import { StatusMessage } from '../MyAssessments/myAssessmentUtils';
import MLButton from '../../components/ui/MLButton/MLButton';
import MLTypography from '../../components/ui/MLTypography/MLTypography';

export interface TextNode {
    text: string;
    type: string;
}

export interface ParagraphNode {
    type: string;
    children: TextNode[];
}
export type Status = 'pending' | 'processing' | 'completed';
export type Priority = 'high' | 'medium' | 'low';

export interface Attachment {
    url: string;
    name: string;
    alternativeText: string;
    caption: string
    mime: string
}
export interface Sender {
    id: number;
    username: string | undefined,
    employee: {
        jobTitle: string
    },
    organization: {
        id: Number,
        name: 'String'
        logo: {
            url: string
        }
    },
    role: {
        name: string
    }
}

export interface Thread {
    id: number;
    title: string;
    content: ParagraphNode[];
    threadIndex: number;
    sentAt: string;
    isErgoAmbassadorResponse: boolean;
    sender: Sender;
    attachments: Attachment[];
    createdOn: string;
    updatedAt: string;
    publishedAt: string;
}

export interface ErgoRequest {
    id: number;
    title: string;
    description: ParagraphNode[];
    priority: Priority;
    status: Status;
    createdOn: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    attachments: Attachment[] | null;
    sender: Sender;
    organization: Organization | null;
    ergoRequestThread: Thread[];
    assignedTo: User;
}

export enum TabTitle {
    ALL = "All Queries",
    ACTIVE = "Active",
    RESOLVED = "Resolved",
}

interface TabItem {
    title: TabTitle;
    count: number;
}

const MyErgoQueris = () => {
    const { data: identity, isLoading: isIdentityLoading, error: identityError } = useGetIdentity<IIdentity>();
    const [currentTab, setCurrentTab] = useState<TabTitle>(TabTitle.ALL);
    const { data: ergoRequests, isLoading: isErgoLoading, error: ergoError, refetch } = useList<ErgoRequest>({
        resource: "ep-ergo-requests",
        meta: {
            populate: "*",
            pagination: {
                pageSize: 1000 // Set a large number to get all records
            }
        },
        filters: [
            {
                field: "sender.id",
                operator: "eq",
                value: identity?.id
            }
        ],
        pagination: {
            pageSize: 1000 // Also set here for refine pagination
        },
        queryOptions: {
            enabled: !!identity?.id
        }
    });

    // Combined loading state
    const isLoading = isIdentityLoading || isErgoLoading;

    // Combined error state
    const error = identityError || ergoError;

    // Memoize filtered requests to avoid recalculation on every render
    const getFilteredRequests = useMemo(() => {
        const filterByTab = (tab: TabTitle) => {
            if (!ergoRequests?.data) return [];

            // Sort by creation date (newest first)
            const sortedData = [...ergoRequests.data].sort((a, b) => {
                return new Date(b.updatedAt || b.createdAt).getTime() - new Date(a.updatedAt || a.createdAt).getTime();
            });

            switch (tab) {
                case TabTitle.ACTIVE:
                    return sortedData.filter(req =>
                        req.status === 'pending' || req.status === 'processing'
                    );
                case TabTitle.RESOLVED:
                    return sortedData.filter(req =>
                        req.status === 'completed'
                    );
                default:
                    return sortedData;
            }
        };

        return {
            [TabTitle.ALL]: filterByTab(TabTitle.ALL),
            [TabTitle.ACTIVE]: filterByTab(TabTitle.ACTIVE),
            [TabTitle.RESOLVED]: filterByTab(TabTitle.RESOLVED)
        };
    }, [ergoRequests?.data]);

    // Memoize tab counts
    const tabCounts = useMemo(() => {
        return {
            [TabTitle.ALL]: getFilteredRequests[TabTitle.ALL]?.length || 0,
            [TabTitle.ACTIVE]: getFilteredRequests[TabTitle.ACTIVE]?.length || 0,
            [TabTitle.RESOLVED]: getFilteredRequests[TabTitle.RESOLVED]?.length || 0
        };
    }, [getFilteredRequests]);

    const tabs = useMemo(() =>
        Object.values(TabTitle).map((tab) => ({
            title: tab,
            count: tabCounts[tab],
        })),
        [tabCounts]);

    const handleTabChange = (event: React.SyntheticEvent, newTab: TabTitle) => {
        setCurrentTab(newTab);
    };

    const handleRetry = () => {
        refetch();
    };

    // Handle loading state
    if (isLoading) {
        return (
            <>
                <MLBanner backgroundColor='#E3DDFF' title={'My Ergo Queries'} subtitle='Track and manage your ergonomic support requests' />
                <Box height={640} display="flex" justifyContent="center" alignItems="center">
                    <Loading />
                </Box>
            </>
        );
    }

    // Handle authentication error
    if (!identity && !isIdentityLoading) {
        return (
            <>
                <MLBanner backgroundColor='#E3DDFF' title={'My Ergo Queries'} subtitle='Track and manage your ergonomic support requests' />
                <Box height={640} display="flex" justifyContent="center" alignItems="center">
                    <MLContainer>
                        <StatusMessage
                            title="Authentication Required"
                            message="You need to be logged in to view your ergo queries. Please log in and try again."
                            type="warning"
                        />
                    </MLContainer>
                </Box>
            </>
        );
    }

    // Handle API error
    if (error) {
        return (
            <>
                <MLBanner backgroundColor='#E3DDFF' title={'My Ergo Queries'} subtitle='Track and manage your ergonomic support requests' />
                <Box height={640} display="flex" justifyContent="center" alignItems="center">
                    <MLContainer>
                        <StatusMessage
                            title="Unable to Load Queries"
                            message="We encountered an error while loading your ergo queries. Please try again later."
                            type="error"
                        />
                        <Box display="flex" justifyContent="center" mt={3}>
                            <MLButton variant="contained" onClick={handleRetry}>
                                Retry
                            </MLButton>
                        </Box>
                    </MLContainer>
                </Box>
            </>
        );
    }

    return (
        <>
            <MLBanner backgroundColor='#E3DDFF' title={'My Ergo Queries'} subtitle='Track and manage your ergonomic support requests' />
            <Stack
                direction={"column"}
                sx={{
                    paddingY: {
                        md: tablet.contentContainer.paddingY,
                        xs: tablet.contentContainer.paddingY,
                    },
                }}
                fontFamily="syne"
            >
                <MLContainer>
                    <Stack gap="15px">
                        <TabContext value={currentTab}>
                            {/* Desktop tab headings - shown only on sm and above */}
                            <Stack
                                direction="row"
                                display={{ xs: "none", sm: "flex" }}
                                justifyContent="space-between"
                                alignItems="center"
                            >
                                <Stack sx={{
                                    overflowX: "auto", // Allow horizontal scrolling
                                    Width: "100%", // Ensure that the tabs container doesn't stretch beyond its width
                                }}>
                                    <MLTabs
                                        visibleScrollbar
                                        value={currentTab}
                                        onChange={handleTabChange}
                                        defaultValue={formatEnumString(TabTitle.ALL)}
                                    >
                                        {tabs.map((tab) => (
                                            <MLTab
                                                key={tab.title}
                                                value={tab.title}
                                                label={`${formatEnumString(tab.title)} (${tab.count})`}
                                                sx={{ pt: 0 }}
                                            />
                                        ))}
                                    </MLTabs>
                                </Stack>
                            </Stack>

                            {/* Mobile tab buttons - shown only on xs */}
                            <Stack
                                direction="row"
                                display={{ xs: "flex", sm: "none" }}
                                sx={{
                                    gap: "12px",
                                    alignItems: "center",
                                }}
                            >
                                {/* All Queries Tab */}
                                <ButtonBase
                                    component={Stack}
                                    sx={{
                                        border: currentTab === TabTitle.ALL ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                                        backgroundColor: currentTab === TabTitle.ALL ? "#E3DDFF" : "",
                                        borderRadius: "5px",
                                        padding: "8px",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        flex: 1,
                                        minWidth: 0,
                                        transition: "background-color 0.3s",
                                        "&:hover": {
                                            backgroundColor: currentTab === TabTitle.ALL ? "#D9D0FF" : "#F5F5F5",
                                        },
                                        gap: "4px",
                                    }}
                                    onClick={() => setCurrentTab(TabTitle.ALL)}
                                    disableRipple={false}
                                    centerRipple
                                >
                                    <Stack>
                                        <MLTypography
                                            variant="body1"
                                            fontSize={"14px"}
                                            fontWeight={600}
                                            textAlign={"center"}
                                        >
                                            {/* All ({tabCounts[TabTitle.ALL]}) */}
                                            All
                                        </MLTypography>
                                        <MLTypography
                                            variant="body1"
                                            fontSize={"14px"}
                                            fontWeight={600}
                                            textAlign={"center"}
                                        >
                                            ({tabCounts[TabTitle.ALL]})
                                        </MLTypography>
                                    </Stack>
                                </ButtonBase>

                                {/* Active Tab */}
                                <ButtonBase
                                    component={Stack}
                                    sx={{
                                        border: currentTab === TabTitle.ACTIVE ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                                        backgroundColor: currentTab === TabTitle.ACTIVE ? "#E3DDFF" : "",
                                        borderRadius: "5px",
                                        padding: "8px",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        flex: 1,
                                        minWidth: 0,
                                        transition: "background-color 0.3s",
                                        "&:hover": {
                                            backgroundColor: currentTab === TabTitle.ACTIVE ? "#D9D0FF" : "#F5F5F5",
                                        },
                                        gap: "4px",
                                    }}
                                    onClick={() => setCurrentTab(TabTitle.ACTIVE)}
                                    disableRipple={false}
                                    centerRipple
                                >
                                    <Stack>
                                        <MLTypography
                                            variant="body1"
                                            fontSize={"14px"}
                                            fontWeight={600}
                                            textAlign={"center"}
                                        >
                                            {/* Active ({tabCounts[TabTitle.ACTIVE]}) */}
                                            Active
                                        </MLTypography>
                                        <MLTypography
                                            variant="body1"
                                            fontSize={"14px"}
                                            fontWeight={600}
                                            textAlign={"center"}
                                        >
                                            ({tabCounts[TabTitle.ACTIVE]})
                                        </MLTypography>
                                    </Stack>
                                </ButtonBase>

                                {/* Resolved Tab */}
                                <ButtonBase
                                    component={Stack}
                                    sx={{
                                        border: currentTab === TabTitle.RESOLVED ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                                        backgroundColor: currentTab === TabTitle.RESOLVED ? "#E3DDFF" : "",
                                        borderRadius: "5px",
                                        padding: "8px",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        flex: 1,
                                        minWidth: 0,
                                        transition: "background-color 0.3s",
                                        "&:hover": {
                                            backgroundColor: currentTab === TabTitle.RESOLVED ? "#D9D0FF" : "#F5F5F5",
                                        },
                                        gap: "4px",
                                    }}
                                    onClick={() => setCurrentTab(TabTitle.RESOLVED)}
                                    disableRipple={false}
                                    centerRipple
                                >
                                    <Stack>
                                        <MLTypography
                                            variant="body1"
                                            fontSize={"14px"}
                                            fontWeight={600}
                                            textAlign={"center"}
                                        >
                                            {/* Resolved ({tabCounts[TabTitle.RESOLVED]}) */}
                                            Resolved
                                        </MLTypography>
                                        <MLTypography
                                            variant="body1"
                                            fontSize={"14px"}
                                            fontWeight={600}
                                            textAlign={"center"}
                                        >
                                            ({tabCounts[TabTitle.RESOLVED]})
                                        </MLTypography>
                                    </Stack>
                                </ButtonBase>
                            </Stack>

                            {/* All Tabs Content */}
                            <Stack>
                                {Object.values(TabTitle).map((tab) => (
                                    <TabPanel key={tab} sx={{ p: 0 }} value={tab}>
                                        {getFilteredRequests[tab].length > 0 ? (
                                            <AllQueries ergoRequests={getFilteredRequests[tab]} />
                                        ) : (
                                            <Box py={{ sx: 0, md: 4 }} display="flex" justifyContent="flex-start">
                                                <StatusMessage
                                                    title={`No ${tab === TabTitle.ALL ? '' : tab.toLowerCase()} queries found`}
                                                    message={
                                                        tab === TabTitle.ACTIVE
                                                            ? "You don't have any active ergo queries at the moment."
                                                            : tab === TabTitle.RESOLVED
                                                                ? "You don't have any resolved ergo queries yet."
                                                                : "You haven't submitted any ergo queries yet."
                                                    }
                                                    type="info"
                                                />
                                            </Box>
                                        )}
                                    </TabPanel>
                                ))}
                            </Stack>
                        </TabContext>
                    </Stack>
                </MLContainer>
            </Stack>
        </>
    );
};

export default MyErgoQueris;