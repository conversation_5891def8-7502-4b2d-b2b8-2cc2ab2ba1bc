import { Stack } from '@mui/material';
import RequestCard from '../components/RequestCard';
import { ErgoRequest, ParagraphNode } from '../MyErgoQueris';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import User from '../../../models/User';

interface AllQueriesProps {
    ergoRequests: ErgoRequest[] | undefined;
    loggedUser?: User;
}

export const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
        return `${diffInMinutes} minutes ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
        return `${diffInHours} hours ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
};

export const getDescriptionText = (description: ParagraphNode[]): string => {
    if (!description) return "";
    return description
        .map(paragraph => paragraph.children.map(child => child.text).join(''))
        .join(' ')
        .trim();
};

const AllQueries: React.FC<AllQueriesProps> = ({ loggedUser, ergoRequests }) => {

    if (!ergoRequests || ergoRequests?.length === 0) {
        return (
            <Stack gap="15px">
                <MLTypography color="text.secondary">
                    No queries available
                </MLTypography>
            </Stack>
        );
    }

    return (
        <Stack gap="15px">
            {ergoRequests?.map((request) => (
                <RequestCard
                    key={request.id}
                    id={request.id}
                    title={request.title}
                    priority={request.priority}
                    status={request?.status}
                    responsesCount={request.ergoRequestThread.length}
                    attachmentsCount={request.attachments?.length || 0}
                    description={getDescriptionText(request.description)}
                    lastUpdated={formatTimeAgo(request.updatedAt)}
                    sender={request.sender}
                    assignedTo={request.assignedTo}
                    loggedUser={loggedUser}
                />
            ))}
        </Stack>
    );
};

export default AllQueries;