import { FunctionComponent, useState } from "react";
import { Stack, Typography, Box, Button, IconButton } from "@mui/material";
import { ChevronLeft } from "@mui/icons-material";
import MLTypography from "../../components/ui/MLTypography/MLTypography";
import { useBack } from "@refinedev/core";
import MLTabs from "../../components/ui/MLTabs/MLTabs";
import { formatEnumString } from "../../utils/enumUtils";
import MLTab from "../../components/ui/MLTab/MLTab";
import { TabContext, TabPanel } from "@mui/lab";
import Videos from "./Videos";
import { desktop, tablet } from "../../responsiveStyles";
import MLBanner from "../../components/ui/MLBanner/MLBanner";
import MLContainer from "../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";

export enum TabTitle {
    VIDEOS = "videos",
    PRESENTATIONS = "presentations",
    HANDOUTS = "handouts",
}

export interface TabItem {
    title: TabTitle;
    count: number;
}

const Ergo101 = () => {
    const [currentTab, setCurrentTab] = useState<TabTitle>(TabTitle.VIDEOS);
    const [tabs, setTabs] = useState<TabItem[]>(
        Object.values(TabTitle).map((tabs) => ({
            title: tabs,
            count: 0,
        }) as TabItem,
        ),
    );
    const back = useBack();

    return (
        <>
            <MLBanner backgroundColor='#E3DDFF' title={'Ergo 101'} subtitle='Explore our guides for workplace ergonomics' />

            <Stack
                direction={"column"}
                sx={{
                    paddingX: {
                        lg: desktop.contentContainer.paddingX,
                        md: tablet.contentContainer.paddingX,
                        xs: tablet.contentContainer.paddingX,
                    },
                    paddingY: {
                        // lg: desktop.contentContainer.paddingY,
                        md: tablet.contentContainer.paddingY,
                        xs: tablet.contentContainer.paddingY,
                    },
                }}
                fontFamily="syne"
            >
                {/* <Stack direction="row" mb={1} alignItems={"center"} gap={1}>
                <IconButton size="small" onClick={() => back()}>
                    <ChevronLeft />
                </IconButton>
                <MLTypography variant="h1" fontWeight={700}>
                    Ergo 101
                </MLTypography>
            </Stack> */}
                <MLContainer>
                    <TabContext value={currentTab} >
                        <MLTabs sx={{
                            mb: 2
                        }}
                            value={currentTab}
                            onChange={(e, val) => setCurrentTab(val)}
                            defaultValue={formatEnumString(TabTitle.VIDEOS)}
                        >
                            {tabs.map((tab) => (
                                <MLTab
                                    key={tab.title}
                                    value={tab.title}
                                    label={`${formatEnumString(tab.title)}`}
                                />
                            ))}
                        </MLTabs>

                        <Stack
                            sx={{
                                padding: "30px",
                                border: "0.5px solid #929292",
                                borderRadius: "10px",
                            }}
                        >
                            <TabPanel value={TabTitle.VIDEOS}>
                                <Videos />
                            </TabPanel>
                            <TabPanel value={TabTitle.PRESENTATIONS}>PRESENTATIONS</TabPanel>
                            <TabPanel value={TabTitle.HANDOUTS}>HANDOUTS</TabPanel>
                        </Stack>
                    </TabContext>
                </MLContainer>
            </Stack>
        </>
    );
};

export default Ergo101;
