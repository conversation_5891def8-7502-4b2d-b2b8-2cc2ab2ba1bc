import { useState } from "react";
import { Stack } from "@mui/material";
import { useList } from "@refinedev/core";
import FrequentVideos from "./FrequentVideos";
import BrowseVideosByTopic from "./BrowseVideosByTopic";
import Loading from "../Loading/Loading";

export enum TabTitle {
    VIDEOS = "videos",
    PRESENTATIONS = "presentations",
    HANDOUTS = "handouts",
}

export interface TabItem {
    title: TabTitle;
    count: number;
}

const Videos = () => {
    const { data: frequentVideosList, isLoading, isError } = useList({
        resource: 'ergo101s',
        pagination: {
            pageSize: 10,
        },
        meta: {
            populate: '*',
        },
    });
    return (
        <>
            {isLoading ?
                <Loading /> :
                <Stack direction={"column"} gap="30px">
                    <FrequentVideos data={frequentVideosList?.data} />
                    <BrowseVideosByTopic data={frequentVideosList?.data} />
                </Stack>
            }
        </>

    );
};

export default Videos;
