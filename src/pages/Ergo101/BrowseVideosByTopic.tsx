import { FunctionComponent, useState } from "react";
import { <PERSON>, <PERSON>ack, <PERSON>po<PERSON>, <PERSON>ton, Dialog, useMediaQuery, useTheme } from "@mui/material";
import MLButton from "../../components/ui/MLButton/MLButton";
import VideoPopup from "./components/VideoPopup";
import PlayIcon from "../../assets/icons/PlayIcon";

export type BrowseVideosByTopicPropsType = {
  data: any;
};

const BrowseVideosByTopic: FunctionComponent<BrowseVideosByTopicPropsType> = ({ data }) => {
  const theme = useTheme();
  const isLaptop = useMediaQuery(theme.breakpoints.up('lg'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'lg'));
  const [open, setOpen] = useState(false);
  const [currentVideo, setCurrentVideo] = useState();
  const [duration, setDuration] = useState<string | null>(null);

  const handleLoadedMetadata = (event: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    const videoElement = event.target as HTMLVideoElement;
    const minutes = Math.floor(videoElement.duration / 60);
    const seconds = Math.floor(videoElement.duration % 60);
    setDuration(`${minutes}:${seconds < 10 ? '0' : ''}${seconds}`);
  };

  const browesByTopicsButtonsLabel = [
    { id: 1, label: "All", value: "all" },
    { id: 2, label: "Workplace", value: "workplace" },
    { id: 3, label: "Posture", value: "posture" },
    { id: 4, label: "Exercises", value: "exercises" },
    { id: 5, label: "Back", value: "back" },
    { id: 6, label: "Neck", value: "neck" },
    { id: 7, label: "Shoulders", value: "shoulders" },
  ]
  const [selectedTopicForBrowesVideos, setSelectedTopicForBrowesVideos] = useState([browesByTopicsButtonsLabel[0].value])

  const handleTopicClick = (value: string) => {
    // setSelectedTopicForBrowesVideos((prev) =>
    //   prev.includes(value) ? prev.filter((topic) => topic !== value) : [...prev, value]
    // );
    setSelectedTopicForBrowesVideos((prev) => {
      if (value === "all") {
        return ["all"];
      } 
      else {
        // If "all" is currently selected and another value is clicked, remove "all" and add the new value
        if (prev.includes("all")) {
          return [value];
        } else {
          // Toggle the selected value
          const newTopics = prev.includes(value) ? prev.filter((topic:any) => topic !== value) : [...prev, value];
          // If no topics are selected after toggling, return ["all"]
          return newTopics.length === 0 ? ["all"] : newTopics;
        }
      }
    });
  };

  const handleClickOpenVideoPopup = (video: any) => {
    setCurrentVideo(video);
    setOpen(true);
  };

  const handleCloseVideoPopup = () => {
    setOpen(false);
    // setCurrentVideo('');
  };


  return (
    <Stack gap="30px">
      <Stack direction="row" gap="30px" alignItems="center">
        <Typography
          sx={{
            fontSize: '30px',
            fontWeight: 600,
          }}
        >
          Browse by topics
        </Typography>
        <Typography
          sx={{
            fontSize: '16px',
            fontWeight: 600,
          }}          >
          (You may select more than one topic)
        </Typography>
      </Stack>
      <Stack gap="10px" alignItems="flex-start">
        <Stack direction="row" gap="15px">
          {
            browesByTopicsButtonsLabel.map((button, index) => (
              <MLButton
                key={button.id}
                variant={selectedTopicForBrowesVideos.includes(button.value) ? "contained" : "outlined"}
                onClick={() => handleTopicClick(button.value)}
              >
                {button.label}
              </MLButton>
            ))
          }
        </Stack>
        <MLButton variant="text">
          See more topics
        </MLButton>
      </Stack>
      {!data ?
        <Stack>
          <Typography>
            No videos available !
          </Typography>
        </Stack>
        :
        <Stack gap="30px">
          <Stack style={{
            display: "grid",
            gridTemplateColumns: isLaptop ? "repeat(3, 1fr)" : isTablet ? "repeat(2, 1fr)" : "repeat(1, 1fr)",
            gap: "26px",
          }}>
            {
              data.map((item: any, index: string) => (
                <Stack
                  key={item.id}
                  direction="column"
                  gap="20px"
                  width="100%"
                  textAlign="left"
                >
                  <Stack
                    width="auto"
                    // height="222px"
                    borderRadius="4px"
                    border="0.5px solid #9C9C9C"
                    sx={{ cursor: "pointer" }}
                    position="relative"
                    onClick={() => handleClickOpenVideoPopup(item)}
                  >
                    <Box
                      sx={{
                        position: "absolute",
                        top: "13px",
                        left: 0,
                        backgroundColor: "#E7E7E7",
                        py: "2px",
                        px: "8px",
                      }}
                      borderLeft="1px solid #9C9C9C"
                    >
                      <Typography variant="body2" >
                        Posture
                      </Typography>
                    </Box>
                    <Typography
                      sx={{
                        position: "absolute",
                        top: "8px",
                        right: "8px",
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        color: "#fff",
                        py: "2px",
                        px: "8px",
                        borderRadius: "4px",
                      }}
                    >
                      {duration || ""}
                    </Typography>
                    <Box position="absolute" top="35%" left="45%" color="inherit" onClick={() => handleClickOpenVideoPopup(item)} sx={{ cursor: "pointer" }}>
                      <PlayIcon />
                    </Box>
                    <video
                      src={item.video[0].url}
                      poster={item.video_thumbnail.url}
                      onLoadedMetadata={handleLoadedMetadata}
                      onClick={() => handleClickOpenVideoPopup(item)}
                      style={{
                        width: "100%",
                        // height: "100%",
                        borderRadius: "3px",
                        objectFit: "fill",
                        cursor: "pointer",
                      }}
                      disablePictureInPicture
                    />
                  </Stack>
                  <Box>
                    <Typography sx={{
                      fontSize: '20px',
                      fontWeight: 600,
                    }}
                    >
                      {item.title}
                    </Typography>
                    <Typography sx={{
                      fontSize: '14px',
                      fontWeight: 400,
                    }}
                    >
                      {item.description}
                    </Typography>
                  </Box>
                </Stack>
              ))
            }
          </Stack>
          <Box sx={{ width: "100%", display: "flex", justifyContent: "center", p: "8px" }}>
            <Button
              variant="outlined"
              sx={{
                borderColor: "text.secondary",
                display: "flex",
                alignItems: "center",
                gap: "4px",
              }}
            >
              Show More
            </Button>
          </Box>
        </Stack>
      }
      {/* Dialog for video player */}
      <Dialog open={open} onClose={handleCloseVideoPopup} maxWidth="lg" fullWidth>
        <VideoPopup videoData={currentVideo} relatedVideosData={data} onClose={handleCloseVideoPopup} />
      </Dialog>
    </Stack>

  );
};

export default BrowseVideosByTopic;
