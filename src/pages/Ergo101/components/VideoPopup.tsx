import { FunctionComponent, SyntheticEvent, useState } from "react";
import { Box, DialogContent, Stack, Typography, IconButton } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import VideoBox from "./VideoBox";
import MLButton from "../../../components/ui/MLButton/MLButton";

export type VideoDialogPropsType = {
    videoData: any;
    relatedVideosData: any;
    onClose: () => void;
};

const VideoPopup: FunctionComponent<VideoDialogPropsType> = ({ videoData, relatedVideosData, onClose }) => {
    const [currentVideo, setCurrentVideo] = useState(videoData?.video[0].url);
    const [playerKey, setPlayerKey] = useState(0);
    const [duration, setDuration] = useState<string | null>(null);

    const handleSetCurrentVideo = (event: SyntheticEvent, newVideo: any) => {
        setCurrentVideo(newVideo.video[0].url);
        setPlayerKey(prevKey => prevKey + 1); // Update the key to force re-render
    }

    const handleLoadedMetadata = (event: React.SyntheticEvent<HTMLVideoElement, Event>) => {
        const videoElement = event.target as HTMLVideoElement;
        const minutes = Math.floor(videoElement.duration / 60);
        const seconds = Math.floor(videoElement.duration % 60);
        setDuration(`${minutes}:${seconds < 10 ? '0' : ''}${seconds}`);
    };

    return (
        <DialogContent sx={{ padding: "30px 60px" }}>
            <Stack direction="row" justifyContent="space-between" alignItems="start">
                <Typography sx={{
                    fontSize: '32px',
                    fontWeight: 600,
                }}>
                    Sitting posture
                </Typography>
                <IconButton onClick={onClose}>
                    <CloseIcon />
                </IconButton>
            </Stack>
            <Stack>
                <Stack direction="row" gap="30px" height={"664px"} minWidth="1058px">
                    <Stack position="relative" flex={3} gap="23px">
                        <Box >
                            {/* <ReactPlayer key={playerKey} url={currentVideo} playing controls width="100%" height="100%" style={{ position: 'absolute', top: 0, left: 0 }} /> */}
                            <video
                                key={playerKey}
                                src={currentVideo}
                                poster={videoData.video_thumbnail.url}
                                onLoadedMetadata={handleLoadedMetadata}
                                style={{
                                    border: "0.5px solid #9C9C9C",
                                    width: "100%",
                                    height: "436px",
                                    borderRadius: "10px",
                                    objectFit: "fill"
                                }}
                                controls
                                autoPlay
                                disablePictureInPicture
                                controlsList="nodownload"
                            />
                            <Box
                                sx={{
                                    position: "absolute",
                                    top: "13px",
                                    backgroundColor: "#E7E7E7",
                                    py: "2px",
                                    px: "8px",
                                    borderLeft: "1px solid #9C9C9C"
                                }}
                            >
                                <Typography >
                                    Posture
                                </Typography>
                            </Box>
                            <Typography
                                sx={{
                                    position: "absolute",
                                    top: "8px",
                                    right: "8px",
                                    backgroundColor: "rgba(0, 0, 0, 0.6)",
                                    color: "#fff",
                                    py: "2px",
                                    px: "8px",
                                    borderRadius: "4px",
                                }}
                            >
                                {duration || ""}
                            </Typography>
                        </Box>
                        <Box>
                            <Typography
                                sx={{
                                    fontSize: '24px',
                                    fontWeight: 700,
                                }}
                            >
                                {videoData.title}
                            </Typography>
                            <Typography sx={{
                                fontSize: '16px',
                                fontWeight: 400,
                            }}>
                                {videoData.description}
                            </Typography>
                        </Box>
                    </Stack>
                    <Stack
                        flex={1}
                        gap="15px"
                    >
                        <Typography sx={{
                            fontSize: '20px',
                            fontWeight: 600,
                        }}>
                            Related videos
                        </Typography>
                        {! relatedVideosData ?
                            <Stack>
                                <Typography>
                                    No related videos available !
                                </Typography>
                            </Stack>
                            :
                            <Stack
                                // height="650px"
                                gap="30px"
                                overflow="auto"
                                sx={{
                                    "&::-webkit-scrollbar": {
                                        width: "7px",
                                        scrollBehavior: "smooth",
                                    },
                                    "&::-webkit-scrollbar-track": {
                                        boxShadow: "none",
                                        backgroundColor: "none",
                                    },
                                    "&::-webkit-scrollbar-thumb": {
                                        backgroundColor: "#EAEAEA",
                                        height: "184px",
                                        borderRadius: "4px",
                                    },
                                }}
                            >
                                {
                                    relatedVideosData.map((item: any) => (
                                        <VideoBox key={item.id} video={item} handleClickOpenVideoPopup={handleSetCurrentVideo} />
                                    ))
                                }
                            </Stack>
                        }
                    </Stack>
                </Stack>
                <MLButton
                    type="submit"
                    variant="contained"
                    color="secondary"
                    // onClick={}
                    sx={{ alignSelf: 'center' }}
                >
                    CONTINUE WITH ASSESSMENT
                </MLButton>
            </Stack>

        </DialogContent >
    );
};

export default VideoPopup;
