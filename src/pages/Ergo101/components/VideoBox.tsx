import { Box, Stack, Typography } from '@mui/material';
import React, { FunctionComponent, SyntheticEvent, useState } from 'react';
import PlayIcon from '../../../assets/icons/PlayIcon';

export type VideoBoxPropsType = {
    video: any;
    handleClickOpenVideoPopup: (event: SyntheticEvent<Element, Event>, video: any) => void;
};

const VideoBox: FunctionComponent<VideoBoxPropsType> = ({ video, handleClickOpenVideoPopup }) => {
    const [duration, setDuration] = useState<string | null>(null);

    const handleLoadedMetadata = (event: React.SyntheticEvent<HTMLVideoElement, Event>) => {
        const videoElement = event.target as HTMLVideoElement;
        const minutes = Math.floor(videoElement.duration / 60);
        const seconds = Math.floor(videoElement.duration % 60);
        setDuration(`${minutes}:${seconds < 10 ? '0' : ''}${seconds}`);
    };

    const handleTimeUpdate = () => {

    };

    return (
        <Stack
            direction="column"
            gap="8px"
            position="relative"
        >
            <Box
                sx={{
                    position: "absolute",
                    top: "13px",
                    left: 0,
                    backgroundColor: "#E7E7E7",
                    py: "2px",
                    px: "8px",
                    borderLeft: "1px solid #9C9C9C"
                }}
            >
                <Typography variant="body2">
                    Posture
                </Typography>
            </Box>
            <Stack
                // width="100%"
                // minHeight="180px"
                alignItems="center"
                justifyContent="center"
                position="relative"
            >
                <video
                    src={video.video[0].url}
                    poster={video.video_thumbnail.url}
                    onLoadedMetadata={handleLoadedMetadata}
                    onTimeUpdate={handleTimeUpdate}
                    onClick={(e) => handleClickOpenVideoPopup(e, video)}
                    style={{
                        width: "100%",
                        objectFit: "fill",
                        border: "1px solid #9C9C9C",
                        borderRadius: "8px",
                        cursor: "pointer"
                    }}
                    disablePictureInPicture
                // controls
                />
                <Box position="absolute" top="50" color="inherit" onClick={(e) => handleClickOpenVideoPopup(e, video)} sx={{cursor:"pointer"}}>
                    <PlayIcon />
                </Box>
            </Stack>
            <Box textAlign="left">
                <Typography
                    sx={{
                        fontSize: '16px',
                        fontWeight: 600,
                    }}
                >
                    {video.title}
                </Typography>
                <Typography
                    sx={{
                        fontSize: '14px',
                        fontWeight: 400,
                    }}
                >
                    {duration ? `${duration} mins` : 'NA'}
                </Typography>
            </Box>

        </Stack>
    );
};

export default VideoBox;



