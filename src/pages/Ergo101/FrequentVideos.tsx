import { FunctionComponent, SyntheticEvent, useState } from "react";
import { <PERSON>, Button, Dialog, DialogContent, Stack, Typography } from "@mui/material";
import VideoPopup from "./components/VideoPopup";
import VideoBox from "./components/VideoBox";
import MLTypography from "../../components/ui/MLTypography/MLTypography";

export type FrequentVideosPropsType = {
    data: any;
};

const FrequentVideos: FunctionComponent<FrequentVideosPropsType> = ({ data }) => {
    //  need to fix related videos data
    const [open, setOpen] = useState(false);
    const [currentVideo, setCurrentVideo] = useState({});

    const handleClickOpenVideoPopup = (event: SyntheticEvent, video: Object) => {
        event.stopPropagation()
        setCurrentVideo(video);
        setOpen(true);
    };

    const handleCloseVideoPopup = () => {
        setOpen(false);
        // setCurrentVideo({});
    };

    return (
        <Stack gap="30px" alignItems="start">
            <Typography
                sx={{
                    fontSize: '30px',
                    fontWeight: 600,
                }}
            >
                Frequently played videos
            </Typography>
            {!data ?
                <Stack>
                    <MLTypography>
                        No frequently played videos !
                    </MLTypography>
                </Stack>
                : <Stack gap="10px" width="100%" alignItems="flex-start">
                    <Stack direction="row" width="100%" gap="20px">
                        {
                            data.map((item: any, index: string) => (
                                // <Stack
                                //     key={item.id}
                                //     direction="column"
                                //     gap="8px"
                                //     position="relative"
                                //     width="auto"
                                //     height="250px"
                                // >
                                //     <Box
                                //         sx={{
                                //             position: "absolute",
                                //             top: "13px",
                                //             left: 0,
                                //             backgroundColor: "#E7E7E7",
                                //             py: "2px",
                                //             px: "8px",
                                //         }}
                                //         borderLeft="1px solid #9C9C9C"
                                //     >
                                //         <Typography variant="body2" >
                                //             Posture
                                //         </Typography>
                                //     </Box>
                                //     <Stack
                                //         width="auto"
                                //         height="180px"
                                //         borderRadius="8px"
                                //         border="1px solid #9C9C9C"
                                //         alignItems="center"
                                //         justifyContent="center"
                                //         sx={{ cursor: "pointer" }}
                                //         onClick={() => handleClickOpenVideoPopup(item)}
                                //     >

                                //         <Box
                                //             component="img"
                                //             src={item.video_thumbnail.url}
                                //             alt=""
                                //             loading="lazy"
                                //             sx={{
                                //                 width: '100%',
                                //                 height: '100%',
                                //                 objectFit: 'fill',
                                //                 cursor: 'pointer',  // Adding cursor pointer to indicate clickability
                                //             }}
                                //             onClick={() => handleClickOpenVideoPopup(item)} // Open modal on click
                                //         />
                                //     </Stack>
                                //     <Box textAlign="left">
                                //         <Typography
                                //             sx={{
                                //                 fontSize: '16px',
                                //                 fontWeight: 600,
                                //             }}
                                //         >
                                //             {item.title}
                                //         </Typography>
                                //         <Typography sx={{
                                //             fontSize: '14px',
                                //             fontWeight: 400,
                                //         }}>
                                //             2 mins
                                //         </Typography>
                                //     </Box>
                                // </Stack>
                                <Stack direction="row" width="20%" gap="20px">

                                    <VideoBox key={item.id} video={item} handleClickOpenVideoPopup={handleClickOpenVideoPopup} />
                                </Stack>
                                    ))
                        }
                    </Stack>
                    <Button variant="text" sx={{ display: "block" }}>
                        See more videos
                    </Button>
                </Stack>
            }
            {/* Dialog for video player */}
            <Dialog open={open} onClose={handleCloseVideoPopup} maxWidth="lg" fullWidth >
                <VideoPopup videoData={currentVideo} relatedVideosData={data} onClose={handleCloseVideoPopup} />
            </Dialog>
        </Stack>
    );
};

export default FrequentVideos;
