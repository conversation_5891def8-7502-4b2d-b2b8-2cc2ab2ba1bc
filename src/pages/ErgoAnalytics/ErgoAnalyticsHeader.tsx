import { useContext, useEffect, useState } from "react";
import MLTypography from "../../components/ui/MLTypography/MLTypography";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import { Stack } from "@mui/material";
import { ViewModeContext } from "../../contexts/ViewModeContext/ViewModeContext";

dayjs.extend(isoWeek);

const ErgoAnalyticsHeader = () => {
  const { isEmployeeView } = useContext(ViewModeContext);

  const [currentDateTime, setCurrentDateTime] = useState(
    dayjs().format("dddd, D MMMM YYYY, h:mm A"),
  );

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentDateTime(dayjs().format("dddd, D MMMM YYYY, h:mm A"));
    }, 10000); // Update every minute (60000 milliseconds)

    return () => clearInterval(interval); // Clean up interval on unmount
  }, []);

  return (
    <Stack
      sx={{
        marginLeft: !isEmployeeView ? { xs: "46px", md: "0px" } : 0,
      }}
    >
      <MLTypography
        sx={{
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
          maxWidth: { xs: "calc(100vw - 100px)" },
        }}
        variant="h1" fontWeight={700}>
        Ergo Analytics
      </MLTypography>
      <MLTypography fontWeight={400} fontSize="14px">
        {currentDateTime}
      </MLTypography>
    </Stack>
  );
};

export default ErgoAnalyticsHeader;
