import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { Box, Stack } from '@mui/material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLToggleButtonGroup from '../../../components/ui/MLToggleButtonGroup/MLToggleButtonGroup';
import MLToggleButton from '../../../components/ui/MLToggleButton/MLToggleButton';
import Loading from '../../Loading/Loading';

interface DataItem {
  month: string;
  low: number;
  medium: number;
  high: number;
}

type riskViewType = "Low" | "Medium" | "High" | "All";

interface RiskLevelProps {
  data: DataItem[];
  isLoading: boolean;
}

const RiskLevel = ({
  data,
  isLoading,
}: RiskLevelProps) => {
  const [selectedRiskView, setSelectedRiskView] = useState<riskViewType>("All");
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!data || !data.length || !svgRef.current) return;

    // Clear any existing SVG content
    d3.select(svgRef.current).selectAll("*").remove();

    // Set dimensions
    const margin = { top: 20, right: 30, bottom: 30, left: 40 };
    const width = 800 - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    // Create tooltip div
    const tooltip = d3.select("body").append("div")
      .attr("class", "tooltip")
      .style("opacity", 0)
      .style("position", "absolute")
      .style("background-color", "white")
      .style("border", "1px solid #ddd")
      .style("border-radius", "4px")
      .style("padding", "8px")
      .style("pointer-events", "none")
      .style("font-size", "12px")
      .style("box-shadow", "0 2px 4px rgba(0,0,0,0.1)");

    // Create SVG
    const svg = d3.select(svgRef.current)
      .attr("viewBox", `0 0 800 400`)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // X scale (months)
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const x = d3.scalePoint()
      .domain(months)
      .range([0, width]);

    // Y scale
    const y = d3.scaleLinear()
      .domain([0, 100])
      .range([height, 0]);

    // Create grid lines
    svg.selectAll('line.horizontal-grid')
      .data(d3.range(0, 101, 20))
      .enter()
      .append('line')
      .attr('class', 'horizontal-grid')
      .attr('x1', 0)
      .attr('x2', width)
      .attr('y1', d => y(d))
      .attr('y2', d => y(d))
      .attr('stroke', '#C1C1C1')
      .attr('stroke-dasharray', '6,6');

    // Add X axis
    svg.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(x))
      .style('font-size', '12px');

    // Add Y axis
    svg.append('g')
      .call(d3.axisLeft(y).ticks(5))
      .style('font-size', '12px');

    // Create line generator with proper typing
    const line = d3.line<{ month: string; value: number }>()
      .x(d => x(d.month) ?? 0)
      .y(d => y(d.value))
      .curve(d3.curveMonotoneX);

    // Define series and their colors
    const series = [
      { name: 'low', color: '#FFB800' },
      { name: 'medium', color: '#FF6B00' },
      { name: 'high', color: '#FF0000' }
    ].filter(item => {
      switch (selectedRiskView) {
        case "All":
          return true;
        case "Low":
          return item.name === 'low';
        case "Medium":
          return item.name === 'medium';
        case "High":
          return item.name === 'high';
        default:
          return true;
      }
    });

    // Add lines for each series
    series.forEach(({ name, color }) => {
      const seriesData = data.map(d => ({
        month: d.month,
        value: d[name as 'low' | 'medium' | 'high']
      }));

      svg.append('path')
        .datum(seriesData)
        .attr('fill', 'none')
        .attr('stroke', color)
        .attr('stroke-width', 2)
        .attr('d', line as any);

      // Add circles at data points with hover functionality
      svg.selectAll(`circle-${name}`)
        .data(seriesData)
        .enter()
        .append('circle')
        .attr('cx', d => x(d.month) ?? 0)
        .attr('cy', d => y(d.value))
        .attr('r', 4)
        .attr('fill', 'white')
        .attr('stroke', color)
        .attr('stroke-width', 2)
        // Add event listeners for hover
        .on("mouseover", function (event, d) {
          const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);
          tooltip.transition()
            .duration(200)
            .style("opacity", 0.9);
          tooltip.html(`${capitalizedName} Risk: <strong>${d.value}%</strong><br>Month: ${d.month}`)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 28) + "px");

          // Highlight the current point
          d3.select(this)
            .attr("r", 6)
            .attr("fill", color)
            .attr("stroke-width", 2);
        })
        .on("mouseout", function () {
          tooltip.transition()
            .duration(500)
            .style("opacity", 0);

          // Reset point to original style
          d3.select(this)
            .attr("r", 4)
            .attr("fill", "white")
            .attr("stroke-width", 2);
        });
    });

    svg.selectAll(".domain")
      .attr('stroke', '#C1C1C1')
      .attr('stroke-dasharray', '6,6');
    svg.selectAll(".tick line").remove();

    // Clean up the tooltip when component unmounts
    return () => {
      d3.select("body").selectAll(".tooltip").remove();
    };

  }, [data, selectedRiskView]);

  return (
    <Stack>
      <Stack direction={{xs: "column", sm: "row" }}
        sx={{
          marginBottom: "20px",
          justifyContent: "space-between",
          alignItems: { sm: "flex-start", md: "center" },
        }}
        gap={1}
      >
        <MLTypography
          variant="h1"
          fontSize={"24px"}
          fontWeight={700}
          lineHeight={1.2}
        >
          Risk Level
        </MLTypography>
        <MLToggleButtonGroup
          color="primary"
          exclusive
          size="small"
          value={selectedRiskView}
          onChange={(event, newValue: string | null) =>
            setSelectedRiskView(newValue as riskViewType)
          }
        >
          <MLToggleButton key={"All"} value={"All"}>
            All
          </MLToggleButton>
          <MLToggleButton key={"High"} value={"High"}>
            High
          </MLToggleButton>
          <MLToggleButton key={"Medium"} value={"Medium"}>
            Medium
          </MLToggleButton>
          <MLToggleButton key={"Low"} value={"Low"}>
            Low
          </MLToggleButton>
        </MLToggleButtonGroup>
      </Stack>
      <Stack
        sx={{
          border: "0.5px solid #9C9C9C",
          borderRadius: "10px",
          padding: "25px"
        }}
      >
        {isLoading ? <Loading /> :
          data.length == 0 ? (
            <Stack
              sx={{
                height: "150px",
                width: "100%",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <MLTypography variant="h1" fontSize={"24px"} fontWeight={600} lineHeight={1.2}>
                No data
              </MLTypography>
            </Stack>
          ) : (
            <>
              <svg ref={svgRef}></svg>
              <Stack direction={"row"} gap={"40px"}
                sx={{
                  marginTop: "15px",
                  justifyContent: "center",
                }}
              >
                <Stack
                  direction={"row"}
                  sx={{
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <Box
                    sx={{
                      border: "1.5px #FFB800 solid",
                      borderRadius: "4px",
                      backgroundColor: "#FFB800",
                      height: "25px",
                      width: "25px",
                    }}
                  />
                  <MLTypography
                    variant="body1"
                    fontSize={"16px"}
                    fontWeight={600}
                    lineHeight={1.2}
                  >
                    Low
                  </MLTypography>
                </Stack>
                <Stack
                  direction={"row"}
                  sx={{
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <Box
                    sx={{
                      border: "1.5px #FF6B00 solid",
                      borderRadius: "4px",
                      backgroundColor: "#FF6B00",
                      height: "25px",
                      width: "25px",
                    }}
                  />
                  <MLTypography
                    variant="body1"
                    fontSize={"16px"}
                    fontWeight={600}
                    lineHeight={1.2}
                  >
                    Medium
                  </MLTypography>
                </Stack>
                <Stack
                  direction={"row"}
                  sx={{
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <Box
                    sx={{
                      border: "1.5px #FF0000 solid",
                      borderRadius: "4px",
                      backgroundColor: "#FF0000",
                      height: "25px",
                      width: "25px",
                    }}
                  />
                  <MLTypography
                    variant="body1"
                    fontSize={"16px"}
                    fontWeight={600}
                    lineHeight={1.2}
                  >
                    High
                  </MLTypography>
                </Stack>
              </Stack>
            </>
          )
        }
      </Stack>
    </Stack>
  )
};

export default RiskLevel;