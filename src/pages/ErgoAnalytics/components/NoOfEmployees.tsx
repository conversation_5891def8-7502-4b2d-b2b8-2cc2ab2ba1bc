import { Box, Stack } from "@mui/material"
import MLTypography from "../../../components/ui/MLTypography/MLTypography"

interface NoOfEmployeesProps {
  totalEmployees: number;
  notStarted: number;
  started: number;
  completed: number;
}

const NoOfEmployees = ({
  totalEmployees,
  notStarted,
  started,
  completed,
}: NoOfEmployeesProps) => {
  const notStartedPercentage = totalEmployees === 0 ? 0 : (notStarted / totalEmployees) * 100;
  const startedPercentage = totalEmployees === 0 ? 0 : (started / totalEmployees) * 100;
  const completedPercentage = totalEmployees === 0 ? 0 : (completed / totalEmployees) * 100;

  return (
    <Stack
      sx={{
        padding: "20px",
        gap: "25px",
        border: "0.5px solid #9C9C9C",
        borderRadius: "10px",
        flex: 1
      }}
    >
      <MLTypography
        variant="h1"
        fontSize={"32px"}
        fontWeight={600}
        lineHeight={1.2}
      >
        No of employees
      </MLTypography>
      <Stack>
        <MLTypography
          variant="body1"
          fontSize={"20px"}
          fontWeight={600}
          lineHeight={1.2}
        >
          Total employees : {totalEmployees}
        </MLTypography>
        <Stack
          direction={"row"}
          sx={{
            marginTop: "13px",
            height: "60px",
            gap: "3.5px",
            borderLeft: "1px solid black",
            borderRight: "1px solid black",
            alignItems: "center",

          }}
        >
          {completedPercentage === 0 && startedPercentage === 0 && notStartedPercentage === 0 ? (
            <Stack
              sx={{
                height: "45px",
                width: "100%",
                border: "0.5px solid #333",
                backgroundColor: "#f5f5f5",
                borderRadius: "10px",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <MLTypography variant="h1" fontSize={"24px"} fontWeight={600} lineHeight={1.2}>
                No data
              </MLTypography>
            </Stack>
          ) : (
            <>
              <Stack
                sx={{
                  height: "45px",
                  width: `${completedPercentage}%`,
                  minWidth: "10%",
                  border: "0.5px solid #333",
                  backgroundColor: "#DFFF32",
                  borderRadius: "0px 10px 10px 0px",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MLTypography variant="h1" fontSize={"24px"} fontWeight={600} lineHeight={1.2}>{completed}</MLTypography>
              </Stack>
              <Stack
                sx={{
                  height: "45px",
                  width: `${startedPercentage}%`,
                  minWidth: "10%",
                  border: "0.5px solid #7856FF",
                  backgroundColor: "#C7BBFF",
                  borderRadius: "10px",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MLTypography variant="h1" fontSize={"24px"} fontWeight={600} lineHeight={1.2}>{started}</MLTypography>
              </Stack>
              <Stack
                sx={{
                  height: "45px",
                  width: `${notStartedPercentage}%`,
                  minWidth: "10%",
                  border: "0.5px solid #FF6E6E",
                  backgroundColor: "#FFC5C5",
                  borderRadius: "10px 0px 0px 10px",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MLTypography variant="h1" fontSize={"24px"} fontWeight={600} lineHeight={1.2}>{notStarted}</MLTypography>
              </Stack>
            </>
          )}
        </Stack>
      </Stack>
      <Stack direction={{ xs: "column", md: "row" }} gap={"20px"}>
        <Stack
          direction={"row"}
          sx={{
            alignItems: "center",
            gap: "10px",
          }}
        >
          <Box
            sx={{
              border: "1.5px black solid",
              borderRadius: "4px",
              backgroundColor: "#DFFF32",
              height: "25px",
              width: "25px",
            }}
          />
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
          >
            Completed
          </MLTypography>
        </Stack>
        <Stack
          direction={"row"}
          sx={{
            alignItems: "center",
            gap: "10px",
          }}
        >
          <Box
            sx={{
              border: "1.5px #7856FF solid",
              borderRadius: "4px",
              backgroundColor: "#E3DDFF",
              height: "25px",
              width: "25px",
            }}
          />
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
          >
            Started
          </MLTypography>
        </Stack>
        <Stack
          direction={"row"}
          sx={{
            alignItems: "center",
            gap: "10px",
          }}
        >
          <Box
            sx={{
              border: "1.5px #FF6E6E solid",
              borderRadius: "4px",
              backgroundColor: "#FFE2E2",
              height: "25px",
              width: "25px",
            }}
          />
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
          >
            Not Started
          </MLTypography>
        </Stack>
      </Stack>
    </Stack>
  );
}

export default NoOfEmployees;