import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import BreakdownProgressBar from "./BreakdownProgressBar";

interface DetailImplementationBreakdownProps {
  total: number;
  fully: number;
  nearly: number;
  good: number;
  minimal: number;
  started: number;
  noAction: number;
}

const DetailImplementationBreakdown = ({
  total,
  fully,
  nearly,
  good,
  minimal,
  started,
  noAction,
}: DetailImplementationBreakdownProps) => {

  return (
    <>
      <MLTypography
        variant="h1"
        fontSize={"24px"}
        fontWeight={700}
        lineHeight={1.2}
      >
        Detail Implementation Breakdown
      </MLTypography>
      <BreakdownProgressBar
        percentage={15}
        count={20}
        label={"Fully Implementation Breakdown"}
        helperText={"(100%)"}
        text={"Completed all recommendation"}
        color={"#DFFF32"}
        borderColor={"#333333"}
      />
      <BreakdownProgressBar
        percentage={25}
        count={35}
        label={"Nearly Complete"}
        helperText={"(75-99%)"}
        text={"Most recommendations implemented"}
        color={"#DFFF32"}
        borderColor={"#333333"}
      />
      <BreakdownProgressBar
        percentage={22}
        count={30}
        label={"Good Progress"}
        helperText={"(50-74%)"}
        text={"Most recommendations implemented"}
        color={"#C7BBFF"}
      />
      <BreakdownProgressBar
        percentage={22}
        count={15}
        label={"Minimal Progress"}
        helperText={"(25-49%)"}
        text={"Few recommendations implemented"}
        color={"#C7BBFF"}
      />
      <BreakdownProgressBar
        percentage={12}
        count={15}
        label={"Started"}
        helperText={"(1-24%)"}
        text={"Some recommendations implemented"}
        color={"#FF6E6E"}
      />
      <BreakdownProgressBar
        percentage={8}
        count={10}
        label={"No Action"}
        helperText={"(0%)"}
        text={"No recommendations implemented"}
        color={"#C40000"}
      />
    </>
  );
}

export default DetailImplementationBreakdown;