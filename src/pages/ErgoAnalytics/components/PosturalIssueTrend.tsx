import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { Box, FormControlLabel, Stack, Switch } from '@mui/material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import Loading from '../../Loading/Loading';


interface DataItem {
  month: string;
  identified: number;
  resolved: number;
}

interface PosturalIssueTrendProps {
  data: DataItem[];
  isLoading: boolean
}

const PosturalIssueTrend = ({
  data,
  isLoading
}: PosturalIssueTrendProps) => {
  const svgRef = useRef<SVGSVGElement>(null);

  const [isPercentageView, setIsPercentageView] = useState<boolean>(false);
  const yearlyIdentified = data.reduce((sum, val) => sum + val.identified, 0);
  const yearlyResolved = data.reduce((sum, val) => sum + val.resolved, 0);
  const maxIdentifiedValue = Math.max(...data.map(item => item.identified));
  const maxResolvedValue = Math.max(...data.map(item => item.resolved));
  const absoluteMaxValue = Math.max(maxIdentifiedValue, maxResolvedValue);

  useEffect(() => {
    if (!data || !data.length || !svgRef.current) return;

    // Clear any existing SVG content
    d3.select(svgRef.current).selectAll("*").remove();

    // Set dimensions
    const margin = { top: 20, right: 30, bottom: 30, left: 40 };
    const width = 800 - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    // Create tooltip div
    const tooltip = d3.select("body").append("div")
      .attr("class", "tooltip")
      .style("opacity", 0)
      .style("position", "absolute")
      .style("background-color", "white")
      .style("border", "1px solid #ddd")
      .style("border-radius", "4px")
      .style("padding", "8px")
      .style("pointer-events", "none")
      .style("font-size", "12px")
      .style("box-shadow", "0 2px 4px rgba(0,0,0,0.1)");

    // Create SVG
    const svg = d3.select(svgRef.current)
      // .attr('width', width + margin.left + margin.right)
      // .attr('height', height + margin.top + margin.bottom)
      .attr("viewBox", `0 0 800 400`)

      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // X scale (months)
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const x = d3.scalePoint()
      .domain(months)
      .range([0, width]);

    // Y scale
    const y = d3.scaleLinear()
      .domain([0, isPercentageView ? 100 : Math.ceil(absoluteMaxValue / 10) * 10])
      .range([height, 0]);

    // Create grid lines
    svg.selectAll('line.horizontal-grid')
      .data(d3.range(0, isPercentageView ? 101 : Math.ceil(absoluteMaxValue / 10) * 10 + 1, isPercentageView ? 20 : absoluteMaxValue <= 30 ? 5 : 10))
      .enter()
      .append('line')
      .attr('class', 'horizontal-grid')
      .attr('x1', 0)
      .attr('x2', width)
      .attr('y1', d => y(d))
      .attr('y2', d => y(d))
      .attr('stroke', '#C1C1C1')
      .attr('stroke-dasharray', '6,6');

    // Add X axis
    svg.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(x))
      .style('font-size', '12px');

    // Add Y axis
    svg.append('g')
      .call(d3.axisLeft(y).ticks(5))
      .style('font-size', '12px');

    // Create line generator with proper typing
    const line = d3.line<{ month: string; value: number }>()
      .x(d => x(d.month) ?? 0)
      .y(d => y(d.value))
      .curve(d3.curveMonotoneX);

    // Define series and their colors
    const series = [
      { name: 'identified', color: '#FF6E6E' },
      { name: 'resolved', color: '#6D4BF4' },
    ]

    // Add lines for each series
    series.forEach(({ name, color }) => {
      const seriesData = data.map(d => ({
        month: d.month,
        value: isPercentageView
          ? (name === "identified" ? yearlyIdentified : yearlyResolved) === 0
            ? 0
            : Math.round((d[name as 'identified' | 'resolved'] / (name === "identified" ? yearlyIdentified : yearlyResolved)) * 100)
          : d[name as 'identified' | 'resolved']
      }));

      svg.append('path')
        .datum(seriesData)
        .attr('fill', 'none')
        .attr('stroke', color)
        .attr('stroke-width', 2)
        .attr('d', line as any);

      // Add circles at data points
      svg.selectAll(`circle-${name}`)
        .data(seriesData)
        .enter()
        .append('circle')
        .attr('cx', d => x(d.month) ?? 0)
        .attr('cy', d => y(d.value))
        .attr('r', 4)
        .attr('fill', 'white')
        .attr('stroke', color)
        .attr('stroke-width', 2)
        .attr('stroke-width', 2)
        // Add event listeners for hover
        .on("mouseover", function (event, d) {
          const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);
          tooltip.transition()
            .duration(200)
            .style("opacity", 0.9);
          tooltip.html(`${capitalizedName} Risk: <strong>${d.value}${isPercentageView ? '%' : ''}</strong><br>Month: ${d.month}`)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 28) + "px");

          // Highlight the current point
          d3.select(this)
            .attr("r", 6)
            .attr("fill", color)
            .attr("stroke-width", 2);
        })
        .on("mouseout", function () {
          tooltip.transition()
            .duration(500)
            .style("opacity", 0);

          // Reset point to original style
          d3.select(this)
            .attr("r", 4)
            .attr("fill", "white")
            .attr("stroke-width", 2);
        });
    });

    svg.selectAll(".domain")
      .attr('stroke', '#C1C1C1')
      .attr('stroke-dasharray', '6,6');
    svg.selectAll(".tick line").remove();


  }, [data, isPercentageView]);

  return (
    <Stack>
      <Stack direction={"row"}
        sx={{
          marginBottom: "20px",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <MLTypography
          variant="h1"
          fontSize={"24px"}
          fontWeight={700}
          lineHeight={1.2}
        >
          Postural Issue Trend
        </MLTypography>
        <FormControlLabel
          control={<Switch inputProps={{ "aria-label": "controlled" }} />}
          label={<MLTypography variant="body1" fontSize={"16px"} fontWeight={500}>Switch view</MLTypography>}
          labelPlacement="start"
          value={isPercentageView}
          onChange={(e, value) => setIsPercentageView(value)}
          sx={{
            marginLeft: 0,
          }}
        />
      </Stack>
      <Stack
        sx={{
          border: "0.5px solid #9C9C9C",
          borderRadius: "10px",
          padding: "25px"
        }}
      >
        {isLoading ? <Loading /> :
          data.length == 0 ? (
            <Stack
              sx={{
                height: "150px",
                width: "100%",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <MLTypography variant="h1" fontSize={"24px"} fontWeight={600} lineHeight={1.2}>
                No data
              </MLTypography>
            </Stack>
          ) : (
            <>
              <svg ref={svgRef}></svg>
              <Stack direction={"row"} gap={"40px"}
                sx={{
                  marginTop: "15px",
                  justifyContent: "center",
                }}
              >
                <Stack
                  direction={"row"}
                  sx={{
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <Box
                    sx={{
                      border: "1.5px #FF6E6E solid",
                      borderRadius: "4px",
                      backgroundColor: "#FF6E6E",
                      height: "25px",
                      width: "25px",
                    }}
                  />
                  <MLTypography
                    variant="body1"
                    fontSize={"16px"}
                    fontWeight={600}
                    lineHeight={1.2}
                  >
                    Identified
                  </MLTypography>
                </Stack>
                <Stack
                  direction={"row"}
                  sx={{
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <Box
                    sx={{
                      border: "1.5px #6D4BF4 solid",
                      borderRadius: "4px",
                      backgroundColor: "#6D4BF4",
                      height: "25px",
                      width: "25px",
                    }}
                  />
                  <MLTypography
                    variant="body1"
                    fontSize={"16px"}
                    fontWeight={600}
                    lineHeight={1.2}
                  >
                    Resolved
                  </MLTypography>
                </Stack>
              </Stack>
            </>
          )
        }
      </Stack>
    </Stack>
  )
};

export default PosturalIssueTrend;