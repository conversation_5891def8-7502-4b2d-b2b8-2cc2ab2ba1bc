import { Box, Stack } from "@mui/material";
import { IRecommendationTypes, ITotalDiscomforts } from "../ErgoAnalytics";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import { useEffect, useRef } from "react";
import * as d3 from "d3";

interface DataItem {
  label: string;
  value: number;
  color: string;
}

interface IEscalationRequest {
  selfAssessment: number;
  employeeRequest: number;
}

interface EscalationRequestProps {
  responseTime: IEscalationRequest
}

const EscalationRequest = ({
  responseTime
}: EscalationRequestProps) => {
  const total = responseTime.selfAssessment + responseTime.employeeRequest;
  const isEmpty = total == 0 ? true : false;

  const getPercentage = (value: number) => total === 0 ? 0 : (value / total) * 100;

  const svgRef = useRef<SVGSVGElement | null>(null);

  useEffect(() => {
    // Clear any existing SVG content
    if (svgRef.current) {
      svgRef.current.innerHTML = '';
    }
    const selfAssessmentPercentage = Math.round(getPercentage(responseTime.selfAssessment));
    const employeeRequestPercentage = Math.round(getPercentage(responseTime.employeeRequest));

    let data;

    if (isEmpty) {
      data = [
        { label: "", value: 100, color: "#C1C1C1" },
      ];
    } else {
      data = [
        { label: "self assessment", value: selfAssessmentPercentage, color: "#7856FF" },
        { label: "employee request", value: employeeRequestPercentage, color: "#DFFF32" },
      ];
    }

    const width = 300;
    const height = 300;
    const margin = 0;
    const radius = Math.min(width, height) / 2 - margin;

    const svg = d3.select(svgRef.current)
      .attr('width', width)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${width / 2},${height / 2})`);

    const pie = d3.pie<DataItem>()
      .value(d => d.value)
      .sort(null);

    const arc = d3.arc<d3.PieArcDatum<DataItem>>()
      .innerRadius(radius * 0.55)  // Create donut hole
      .outerRadius(radius);

    // Create donut segments
    const arcs = svg.selectAll('path')
      .data(pie(data))
      .enter()
      .append('path')
      .attr('d', arc)
      .attr('fill', d => d.data.color);

    // Add percentage labels
    const labelArc = d3.arc<d3.PieArcDatum<DataItem>>()
      .innerRadius(radius * 0.8)
      .outerRadius(radius * 0.7);

    const labels = svg.selectAll('text')
      .data(pie(data))
      .enter()
      .append('text')
      .attr('transform', d => `translate(${labelArc.centroid(d)})`)
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .text(d => isEmpty ? "" : `${d.data.value}%`)
      .style('fill', 'black')
      .style('font-size', '16px')
      .style('font-weight', 'bold');
  }, [getPercentage, responseTime.selfAssessment, responseTime.employeeRequest, isEmpty]);

  return (
    <>
      <Stack
        sx={{
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
        }}
      >
        {isEmpty && (
          <Stack
            sx={{
              position: "absolute",
              alignItems: "center",
            }}
          >
            <MLTypography
              variant="h1"
              fontSize={"32px"}
              fontWeight={600}
              lineHeight={1.2}
            >
              0%
            </MLTypography>
          </Stack>
        )}
        <svg ref={svgRef}></svg>
        {!isEmpty && (
          <Stack direction={"row"} marginTop={"40px"} gap={"40px"}>
            <Stack
              direction={"row"}
              sx={{
                alignItems: "center",
                gap: "10px",
              }}
            >
              <Box
                sx={{
                  border: "1.5px #7856FF solid",
                  borderRadius: "4px",
                  backgroundColor: "#7856FF",
                  height: "25px",
                  width: "25px",
                }}
              />
              <MLTypography
                variant="body1"
                fontSize={"16px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                Self assessment
              </MLTypography>
            </Stack>
            <Stack
              direction={"row"}
              sx={{
                alignItems: "center",
                gap: "10px",
              }}
            >
              <Box
                sx={{
                  border: "1.5px #DFFF32 solid",
                  borderRadius: "4px",
                  backgroundColor: "#DFFF32",
                  height: "25px",
                  width: "25px",
                }}
              />
              <MLTypography
                variant="body1"
                fontSize={"16px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                Employee request
              </MLTypography>
            </Stack>
          </Stack>
        )}
      </Stack>
    </>
  );
}

export default EscalationRequest;
