import { Stack } from "@mui/material";
import * as d3 from 'd3';
import { useEffect, useRef } from "react";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";

interface DataItem {
  label: string;
  value: number;
}

interface IssueIdentifiedTypeProps {
  data: DataItem[];
}

const IssueIdentifiedType = ({
  data
}: IssueIdentifiedTypeProps) => {
  const svgRef = useRef<SVGSVGElement | null>(null);

  useEffect(() => {
    if (!svgRef.current) return;

    // Get only the top 10 issues sorted by value
    const topIssues = [...data]
      .sort((a, b) => b.value - a.value)
      .slice(0, 10);

    const margin = { top: 20, right: 30, bottom: 20, left: 140 };
    const width = 600 - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    // Clear any existing SVG
    d3.select(svgRef.current).selectAll("*").remove();

    // Create SVG
    const svg = d3.select(svgRef.current)
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Create scales
    const y = d3.scaleBand()
      .domain(topIssues.map(d => d.label))
      .range([0, height])
      .padding(0.3);

    const x = d3.scaleLinear()
      .domain([0, 100])
      .range([0, width]);

    // Add background bars
    svg.selectAll<SVGRectElement, DataItem>("rect.background")
      .data(topIssues)
      .join("rect")
      .attr("class", "background")
      .attr("y", d => y(d.label) ?? 0)
      .attr("height", y.bandwidth())
      .attr("x", 0)
      .attr("width", width)
      .attr("fill", "#f5f5f5");

    // Add bars
    svg.selectAll<SVGRectElement, DataItem>("rect.data")
      .data(topIssues)
      .join("rect")
      .attr("class", "data")
      .attr("y", d => y(d.label) ?? 0)
      .attr("height", y.bandwidth())
      .attr("x", 0)
      .attr("width", d => x(d.value))
      .attr("fill", "#FFC5C5");

    // Add percentage labels on bars
    svg.selectAll(".label")
      .data(topIssues)
      .join("text")
      .attr("class", "label")
      .attr("x", d => x(d.value) + 10)
      .attr("y", d => (y(d.label) ?? 0) + (y.bandwidth() / 2))
      .attr("dy", "0.35em")
      .text(d => `${d.value}%`)
      .attr("fill", "black")
      .style('font-size', '16px')
      .style('font-weight', 'bold');

    // Add y-axis with text wrapping
    svg.append("g")
      .call(d3.axisLeft(y))
      .selectAll("text")
      .style('font-size', '12px')
      .call(wrap, margin.left - 10);

    // Remove y-axis line
    svg.selectAll(".tick line").remove();
  }, [data]);

  // Text wrapping function
  function wrap(text: d3.Selection<d3.BaseType, unknown, d3.BaseType, unknown>, width: number) {
    text.each(function () {
      const text = d3.select(this);
      const words = text.text().split(/\s+/);
      let line: string[] = [];
      let lineNumber = 0;
      const lineHeight = 1.1; // ems
      const y = text.attr("y");
      const dy = parseFloat(text.attr("dy") || "0");
      let tspan = text.text(null).append("tspan")
        .attr("x", -10)
        .attr("y", y)
        .attr("dy", dy + "em");
      words.forEach(word => {
        line.push(word);
        tspan.text(line.join(" "));
        if ((tspan.node()?.getComputedTextLength() || 0) > width) {
          line.pop();
          tspan.text(line.join(" "));
          line = [word];
          tspan = text.append("tspan")
            .attr("x", -10)
            .attr("y", y)
            .attr("dy", ++lineNumber * lineHeight + dy + "em")
            .text(word);
        }
      });
    });
  }

  return (
    <Stack>
      <MLTypography
        variant="h1"
        fontSize={"24px"}
        fontWeight={700}
        lineHeight={1.2}
      >
        Top 10 issues identified
      </MLTypography>
      <svg ref={svgRef}></svg>
    </Stack>
  )
}

export default IssueIdentifiedType;