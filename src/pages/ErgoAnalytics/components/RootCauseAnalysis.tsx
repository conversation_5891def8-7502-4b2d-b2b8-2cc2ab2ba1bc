import { Divider, Stack } from "@mui/material";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import React from "react";

interface RootCauseAnalysisProps {
  data: { [key: string]: string | undefined };
}

const RootCauseAnalysis = ({
  data
}: RootCauseAnalysisProps) => {

  return (
    <>
      <MLTypography
        variant="h1"
        fontSize={"24px"}
        fontWeight={700}
        lineHeight={1.2}
        marginBottom={"25px"}
      >
        Root cause analysis
      </MLTypography>
      <Stack>
        {/** header */}
        <Stack direction={"row"}
          sx={{
            backgroundColor: "#E3DDFF",
            padding: "10px"
          }}
        >
          <MLTypography variant="body1" fontSize={"14px"} fontWeight={600} lineHeight={1.2} flex={1}>
            Body Part
          </MLTypography>
          <MLTypography variant="body1" fontSize={"14px"} fontWeight={600} lineHeight={1.2} flex={4}>
            Root Causes
          </MLTypography>
        </Stack>
        {/** row */}
        {Object.entries(data).map(([bodyPart, rootCause]) => (
          <React.Fragment key={bodyPart}>
            <Divider />
            <Stack direction={"row"} paddingY={"20px"} paddingX={"10px"}>
              <MLTypography variant="body1" fontSize={"14px"} fontWeight={600} lineHeight={1.2} flex={1}>
                {bodyPart}
              </MLTypography>
              <MLTypography variant="body1" fontSize={"14px"} fontWeight={400} lineHeight={1.2} flex={4}>
                {rootCause}
              </MLTypography>
            </Stack>
          </React.Fragment>
        ))}
      </Stack>
    </>
  );
}
export default RootCauseAnalysis;