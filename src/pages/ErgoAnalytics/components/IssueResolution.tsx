import { Grid, Stack } from "@mui/material";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";

interface IssueResolutionProps {
  issueIdentified: number;
  issueResolved: number;
  helpNeeded: number;
}

const IssueResolution = ({
  issueIdentified,
  issueResolved,
  helpNeeded,
}: IssueResolutionProps) => {
  const resolutionRate = (issueResolved / issueIdentified) * 100;

  return (
    <Stack>
      <MLTypography
        variant="h1"
        fontSize={"24px"}
        fontWeight={700}
        lineHeight={1.2}
        marginBottom={"30px"}
      >
        Issues and resolutions
      </MLTypography>
      <Grid container spacing={"20px"}>
        {/** Issue identified */}
        <Grid item xs={12} sm={6} md={6}>
          <Stack
            sx={{
              width: "100%",
              height: "185px",
              backgroundColor: "#FF6E6E",
              borderRadius: "10px",
              justifyContent: "center",
              padding: "20px",
              gap: "15px",
            }}
          >
            <Stack gap={"10px"}>
              <MLTypography
                variant="h1"
                fontSize={"40px"}
                fontWeight={700}
                lineHeight={1.2}
                color={"white"}
              >
                {issueIdentified}
              </MLTypography>
              <MLTypography
                variant="body1"
                fontSize={"16px"}
                fontWeight={600}
                lineHeight={1.2}
                color={"white"}
              >
                Issues identified
              </MLTypography>
            </Stack>
          </Stack>
        </Grid>

        {/** Issue resolved */}
        <Grid item xs={12} sm={6} md={6}>
          <Stack
            sx={{
              width: "100%",
              height: "185px",
              backgroundColor: "#6D4BF4",
              borderRadius: "10px",
              justifyContent: "center",
              padding: "20px",
              gap: "15px",
            }}
          >
            <Stack gap={"10px"}>
              <MLTypography
                variant="h1"
                fontSize={"40px"}
                fontWeight={700}
                lineHeight={0.8}
                color={"white"}
              >
                {issueResolved}
              </MLTypography>
              <MLTypography
                variant="body1"
                fontSize={"16px"}
                fontWeight={600}
                lineHeight={1.2}
                color={"white"}
              >
                Issues resolved
              </MLTypography>
            </Stack>
          </Stack>
        </Grid>

        {/** Help needed */}
        <Grid item xs={12} sm={6} md={6}>
          <Stack
            sx={{
              width: "100%",
              height: "185px",
              backgroundColor: "#E3DDFF",
              border: "0.5px solid #E3DDFF",
              borderRadius: "10px",
              justifyContent: "center",
              padding: "20px",
              gap: "15px",
            }}
          >
            <Stack gap={"10px"}>
              <MLTypography
                variant="h1"
                fontSize={"40px"}
                fontWeight={700}
                lineHeight={0.8}
              >
                {helpNeeded}
              </MLTypography>
              <MLTypography
                variant="body1"
                fontSize={"16px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                Help needed
              </MLTypography>
            </Stack>
          </Stack>
        </Grid>

        {/** Resolution rate */}
        <Grid item xs={12} sm={6} md={6}>
          <Stack
            sx={{
              width: "100%",
              height: "185px",
              backgroundColor: "#DFFF32",
              border: "0.5px solid #333333",
              borderRadius: "10px",
              justifyContent: "center",
              padding: "20px",
              gap: "15px",
            }}
          >
            <Stack gap={"10px"}>
              <MLTypography
                variant="h1"
                fontSize={"40px"}
                fontWeight={700}
                lineHeight={0.8}
              >
                {Number(resolutionRate.toFixed(1))}%
              </MLTypography>
              <MLTypography
                variant="body1"
                fontSize={"16px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                Resolution rate
              </MLTypography>
            </Stack>
          </Stack>
        </Grid>
      </Grid>
    </Stack>
  );
};

export default IssueResolution;