import { Box, Stack } from "@mui/material";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";

interface AssessmentTypeProps {
  totalAssessment: number;
  discomfort: number;
  general: number;
}

const AssessmentType = ({
  totalAssessment,
  discomfort,
  general,
}: AssessmentTypeProps) => {
  const discomfortPercentage = totalAssessment === 0 ? 0 : (discomfort / totalAssessment) * 100;
  const generalPercentage = totalAssessment === 0 ? 0 : (general / totalAssessment) * 100;

  return (
    <Stack
      sx={{
        padding: "20px",
        gap: "25px",
        border: "0.5px solid #9C9C9C",
        borderRadius: "10px",
        flex: 1
      }}
    >
      <MLTypography
        variant="h1"
        fontSize={"32px"}
        fontWeight={600}
        lineHeight={1.2}
      >
        Assessment type
      </MLTypography>
      <Stack>
        <MLTypography
          variant="body1"
          fontSize={"20px"}
          fontWeight={600}
          lineHeight={1.2}
        >
          Total assessment : {totalAssessment}
        </MLTypography>
        <Stack
          direction={"row"}
          sx={{
            marginTop: "13px",
            height: "60px",
            gap: "3.5px",
            borderLeft: "1px solid black",
            borderRight: "1px solid black",
            alignItems: "center",

          }}
        >
          {discomfortPercentage === 0 && generalPercentage === 0 ? (
            <Stack
              sx={{
                height: "45px",
                width: "100%",
                border: "0.5px solid #333",
                backgroundColor: "#f5f5f5",
                borderRadius: "10px",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <MLTypography variant="h1" fontSize={"24px"} fontWeight={600} lineHeight={1.2}>
                No data
              </MLTypography>
            </Stack>
          ) : (
            <>
              <Stack
                sx={{
                  height: "45px",
                  width: `${discomfortPercentage}%`,
                  minWidth: "10%",
                  border: "0.5px solid #FF6E6E",
                  backgroundColor: "#FFC5C5",
                  borderRadius: "0px 10px 10px 0px",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MLTypography variant="h1" fontSize={"24px"} fontWeight={600} lineHeight={1.2}>{discomfort}</MLTypography>
              </Stack>
              <Stack
                sx={{
                  height: "45px",
                  width: `${generalPercentage}%`,
                  minWidth: "10%",
                  border: "0.5px solid #7856FF",
                  backgroundColor: "#C7BBFF",
                  borderRadius: "10px 0px 0px 10px",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MLTypography variant="h1" fontSize={"24px"} fontWeight={600} lineHeight={1.2}>{general}</MLTypography>
              </Stack>
            </>
          )}
        </Stack>
      </Stack>
      <Stack direction={"row"} gap={"20px"}>
        <Stack
          direction={"row"}
          sx={{
            alignItems: "center",
            gap: "10px",
          }}
        >
          <Box
            sx={{
              border: "1.5px #FF6E6E solid",
              borderRadius: "4px",
              backgroundColor: "#FFE2E2",
              height: "25px",
              width: "25px",
            }}
          />
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
          >
            Discomfort
          </MLTypography>
        </Stack>
        <Stack
          direction={"row"}
          sx={{
            alignItems: "center",
            gap: "10px",
          }}
        >
          <Box
            sx={{
              border: "1.5px #7856FF solid",
              borderRadius: "4px",
              backgroundColor: "#E3DDFF",
              height: "25px",
              width: "25px",
            }}
          />
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
          >
            Preventive
          </MLTypography>
        </Stack>
      </Stack>
    </Stack >
  );
}

export default AssessmentType;