import { Stack } from "@mui/material";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";

interface IssueResolutionComplianceProps {
  total: number;
  highProgress: number;
  mediumProgress: number;
  needsAttention: number;
}

const IssueResolutionCompliance = ({
  total,
  highProgress,
  mediumProgress,
  needsAttention,
}: IssueResolutionComplianceProps) => {

  return (
    <Stack
      gap={"30px"}
      direction={{ sm: "column", md: "row" }}
    >
      {/** High progress */}
      <Stack
        sx={{
          width: "100%",
          // maxWidth: "380px",
          height: "185px",
          backgroundColor: "#DFFF32",
          border: "0.5px solid #333",
          borderRadius: "10px",
          justifyContent: "center",
          padding: "20px",
          gap: "15px",
        }}
      >
        <Stack gap={"8px"}>
          <Stack direction={"row"}
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <MLTypography
              variant="h1"
              fontSize={"40px"}
              fontWeight={700}
              lineHeight={1.2}
            >
              {highProgress}
            </MLTypography>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={24}
              height={24}
              fill="none"
            >
              <path
                fill="#31C100"
                stroke="#31C100"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M12 23c6.072 0 11-4.928 11-11S18.072 1 12 1 1 5.928 1 12s4.928 11 11 11Z"
              />
              <path
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="m6.137 13.191 2.395 3.403c.186.274.49.44.822.46.332.01.645-.137.85-.401l7.666-9.7"
              />
            </svg>
          </Stack>
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
          >
            High Progress
          </MLTypography>
          <MLTypography
            variant="body1"
            fontSize={"14px"}
            fontWeight={400}
            lineHeight={1.2}
          >
            75 to 100% Implemented
          </MLTypography>
        </Stack>
        <Stack >
          <MLTypography
            variant="body1"
            fontSize={"12px"}
            fontWeight={500}
            lineHeight={1.2}
          >
            {Math.round((highProgress / total) * 100)}% of employees
          </MLTypography>

        </Stack>
      </Stack>

      {/** Medium progress */}
      <Stack
        sx={{
          width: "100%",
          // maxWidth: "380px",
          height: "185px",
          backgroundColor: "#E3DDFF",
          border: "0.5px solid #7856FF",
          borderRadius: "10px",
          justifyContent: "center",
          padding: "20px",
          gap: "15px",
        }}
      >
        <Stack gap={"8px"}>
          <Stack direction={"row"}
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <MLTypography
              variant="h1"
              fontSize={"40px"}
              fontWeight={700}
              lineHeight={1.2}
            >
              {mediumProgress}
            </MLTypography>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={26}
              height={26}
              fill="none"
            >
              <path
                stroke="#7856FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M13.016 4.953a8.021 8.021 0 0 1 8.019 8.018 8.021 8.021 0 0 1-8.019 8.019 8.021 8.021 0 0 1-8.018-8.018M24.722 15.719a12.12 12.12 0 0 1-1.165 3.047M21.375 21.617a13.28 13.28 0 0 1-2.62 1.935M14.086 1c1.09.096 2.16.342 3.175.727M20.38 3.477a11.477 11.477 0 0 1 2.278 2.33M24.348 8.96A10.84 10.84 0 0 1 25 12.148"
              />
              <path
                stroke="#7856FF"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M15.422 24.761C8.91 26.087 2.56 21.886 1.235 15.385-.091 8.875 4.11 2.513 10.61 1.188"
              />
            </svg>
          </Stack>
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
          >
            Medium Progress
          </MLTypography>
          <MLTypography
            variant="body1"
            fontSize={"14px"}
            fontWeight={400}
            lineHeight={1.2}
          >
            25 to 74% Implemented
          </MLTypography>
        </Stack>
        <Stack >
          <MLTypography
            variant="body1"
            fontSize={"12px"}
            fontWeight={500}
            lineHeight={1.2}
          >
            {Math.round((mediumProgress / total) * 100)}% of employees
          </MLTypography>

        </Stack>
      </Stack>

      {/** Needs Attention */}
      <Stack
        sx={{
          width: "100%",
          // maxWidth: "380px",
          height: "185px",
          backgroundColor: "#C40000",
          border: "0.5px solid #C40000",
          borderRadius: "10px",
          justifyContent: "center",
          padding: "20px",
          gap: "15px",
        }}
      >
        <Stack gap={"8px"}>
          <Stack direction={"row"}
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <MLTypography
              variant="h1"
              fontSize={"40px"}
              fontWeight={700}
              lineHeight={1.2}
              color={"white"}
            >
              {needsAttention}
            </MLTypography>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={26}
              height={26}
              fill="none"
            >
              <path
                fill="#fff"
                d="M14.563 3.775a1.5 1.5 0 0 0-2.626 0l-9.75 18A1.5 1.5 0 0 0 3.5 24H23a1.5 1.5 0 0 0 1.313-2.225l-9.75-18Z"
              />
              <path
                stroke="#C40000"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M13.25 16.5V9.75"
              />
              <path
                stroke="#C40000"
                strokeWidth={1.5}
                d="M13.25 20.25a.375.375 0 0 1 0-.75M13.25 20.25a.375.375 0 0 0 0-.75"
              />
            </svg>
          </Stack>
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
            color={"white"}
          >
            Needs Attention
          </MLTypography>
          <MLTypography
            variant="body1"
            fontSize={"14px"}
            fontWeight={400}
            lineHeight={1.2}
            color={"white"}
          >
            0 to 24% Implemented
          </MLTypography>
        </Stack>
        <Stack >
          <MLTypography
            variant="body1"
            fontSize={"12px"}
            fontWeight={500}
            lineHeight={1.2}
            color={"white"}
          >
            {Math.round((needsAttention / total) * 100)}% of employees
          </MLTypography>

        </Stack>
      </Stack>
    </Stack>
  );
}
export default IssueResolutionCompliance;