import { Box, Stack, Tooltip } from "@mui/material";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";


interface BreakdownProgressBarProps {
  percentage: number;
  count: number;
  label: string;
  text: string;
  helperText: string;
  color: string;
  borderColor?: string;
}

const BreakdownProgressBar: React.FC<BreakdownProgressBarProps> = ({
  percentage,
  count,
  label,
  text,
  helperText,
  color,
  borderColor,
}) => {
  return (
    <Box
      sx={{
        position: "relative",
      }}
    >
      <Stack
        direction="row"
        marginBottom="10px"
        sx={{
          justifyContent: "space-between",
        }}
      >
        <Stack
          direction="row"
          sx={{
            alignItems: "center",
            gap: "5px",
          }}
        >
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
          >
            {label}
          </MLTypography>
          <MLTypography
            variant="body1"
            fontSize={"14px"}
            fontWeight={400}
            lineHeight={1.2}
          >
            {helperText}
          </MLTypography>
        </Stack>
        <Stack
          direction="row"
          sx={{
            alignItems: "center",
            gap: "5px",
          }}
        >
          <MLTypography
            variant="body1"
            fontSize={"16px"}
            fontWeight={600}
            lineHeight={1.2}
          >
            {count}
          </MLTypography>
          <MLTypography
            variant="body1"
            fontSize={"14px"}
            fontWeight={400}
            lineHeight={1.2}
          >
            ({percentage}%)
          </MLTypography>
        </Stack>
      </Stack>

      <Stack>
        <Box
          sx={{
            height: "16px",
            borderRadius: "20px",
            backgroundColor: "#F3F3F3",
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              width: `${percentage > 95 ? 100 : percentage}%`,
              height: "100%",
              borderRadius: "20px",
              border: borderColor ? `0.5px solid ${borderColor}` : "",
              backgroundColor: color,
              transition: "width 0.6s ease-in-out",
            }}
          />
        </Box>
      </Stack>

      <MLTypography
        marginTop={"5px"}
        variant="body1"
        fontSize={"12px"}
        fontWeight={500}
        lineHeight={1.2}
      >
        {text}
      </MLTypography>

    </Box>
  );
};

export default BreakdownProgressBar;