import { Box, Stack } from "@mui/material";
import { ITotalDiscomforts } from "./ErgoAnalytics";
import MLTypography from "../../components/ui/MLTypography/MLTypography";

interface DiscomfortAreaProps {
  totalDiscomforts: ITotalDiscomforts | undefined;
}

const DiscomfortArea = ({
  totalDiscomforts,
}: DiscomfortAreaProps) => {
  const total = Object.values(totalDiscomforts ?? {}).reduce((sum, value) => sum + value, 0);

  const getPercentage = (value: number) => total === 0 ? 0 : (value / total) * 100;

  const feetPercentage = Math.round(getPercentage(totalDiscomforts?.feet ?? 0));
  const headPercentage = Math.round(getPercentage(totalDiscomforts?.head ?? 0));
  const eyesPercentage = Math.round(getPercentage(totalDiscomforts?.eyes ?? 0));
  const neckPercentage = Math.round(getPercentage(totalDiscomforts?.neck ?? 0));
  const kneesPercentage = Math.round(getPercentage(totalDiscomforts?.knees ?? 0));
  const elbowsPercentage = Math.round(getPercentage(totalDiscomforts?.elbows ?? 0));
  const wristsPercentage = Math.round(getPercentage(totalDiscomforts?.wrists ?? 0));
  const fingersPercentage = Math.round(getPercentage(totalDiscomforts?.fingers ?? 0));
  const forearmsPercentage = Math.round(getPercentage(totalDiscomforts?.forearms ?? 0));
  const lowerBackPercentage = Math.round(getPercentage(totalDiscomforts?.lowerBack ?? 0));
  const shouldersPercentage = Math.round(getPercentage(totalDiscomforts?.shoulders ?? 0));

  return (
    <>
      <MLTypography
        variant="h1"
        fontSize={"24px"}
        fontWeight={700}
        lineHeight={1.2}
        marginBottom={"50px"}
      >
        Areas of discomfort
      </MLTypography>
      <Stack
        direction={{ sm: "column", md: "column", lg: "row" }}
        sx={{
          alignItems: "center",
        }}
        gap={{ md: "0px", lg: "100px" }}
      >
        <Stack position={"relative"}
          sx={{
            alignItems: "center",
          }}
        >
          {/** Head label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "20px",
              left: { xs: "123px", md: "153px" },
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${headPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: headPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "-8px",
              left: { xs: "155px", md: "185px" },
            }}
          >
            {headPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {headPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={headPercentage != 0 ? "" : "#9C9C9C"}
            >
              Head
            </MLTypography>
          </Stack>

          {/** Eyes label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "50px",
              left: { xs: "110px", md: "140px" },
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${eyesPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: eyesPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "35px",
              left: { xs: "35px", md: "65px" },
            }}
          >
            {eyesPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {eyesPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={eyesPercentage != 0 ? "" : "#9C9C9C"}
            >
              Eyes
            </MLTypography>
          </Stack>

          {/** Shoulders label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "125px",
              left: { xs: "180px", md: "210px" },
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${shouldersPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: shouldersPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "90px",
              left: { xs: "208px", md: "238px" },
            }}
          >
            {shouldersPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {shouldersPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={shouldersPercentage != 0 ? "" : "#9C9C9C"}
            >
              Shoulders
            </MLTypography>
          </Stack>

          {/** Elbow label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "187px",
              left: { xs: "56px", md: "86px" },
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${elbowsPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: elbowsPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "159px",
              left: { xs: "-30px", md: "0px" },
            }}
          >
            {elbowsPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {elbowsPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={elbowsPercentage != 0 ? "" : "#9C9C9C"}
            >
              Elbow
            </MLTypography>
          </Stack>

          {/** Wrist label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "242px",
              left: { xs: "216px", md: "246px" },
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${elbowsPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: wristsPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "228px",
              left: { xs: "245px", md: "285px" },
            }}
          >
            {wristsPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {wristsPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={wristsPercentage != 0 ? "" : "#9C9C9C"}
            >
              Wrist
            </MLTypography>
          </Stack>

          {/** Knee label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "360px",
              left: { xs: "85px", md: "115px" },
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${kneesPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: kneesPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "331px",
              left: { xs: "10px", md: "40px" },
            }}
          >
            {kneesPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {kneesPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={kneesPercentage != 0 ? "" : "#9C9C9C"}
            >
              Knee
            </MLTypography>
          </Stack>

          {/** Feet label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "487px",
              left: { xs: "170px", md: "200px" },
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${feetPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: feetPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "455px",
              left: { xs: "205px", md: "235px" },
            }}
          >
            {feetPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {feetPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={feetPercentage != 0 ? "" : "#9C9C9C"}
            >
              Feet
            </MLTypography>
          </Stack>

          <Stack marginLeft={{ xs: 0, md: "30px" }}>
            <svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" width="256" height="532" viewBox="0 0 355 705">
              <g id='outline-graph'>
                <g id='eyes-graph'>
                  <path
                    d="M165.56,55.4c2.86,0,5.72,1.04,7.96,3.11.2.18.2.5,0,.68-2.25,2.07-5.11,3.11-7.96,3.11s-5.71-1.04-7.96-3.11c-.2-.18-.2-.5,0-.68,2.25-2.07,5.11-3.11,7.96-3.11M165.56,54.4c-3.21,0-6.28,1.2-8.64,3.37-.3.27-.47.67-.47,1.07s.17.8.47,1.07c2.36,2.18,5.43,3.37,8.64,3.37s6.28-1.2,8.64-3.37c.3-.27.47-.67.47-1.07s-.17-.8-.47-1.07c-2.36-2.18-5.43-3.37-8.64-3.37h0Z" />
                  <path
                    d="M190.37,55.4c2.86,0,5.72,1.04,7.96,3.11.2.18.2.5,0,.68-2.25,2.07-5.11,3.11-7.96,3.11s-5.71-1.04-7.96-3.11c-.2-.18-.2-.5,0-.68,2.25-2.07,5.11-3.11,7.96-3.11M190.37,54.4c-3.21,0-6.28,1.2-8.64,3.37-.3.27-.47.67-.47,1.07s.17.8.47,1.07c2.36,2.18,5.43,3.37,8.64,3.37s6.28-1.2,8.64-3.37c.3-.27.47-.67.47-1.07s-.17-.8-.47-1.07c-2.36-2.18-5.43-3.37-8.64-3.37h0Z" />
                </g>
                <path id='body-graph'
                  d="M340.89,362.34c2.58,5.2,3.31,8.97,2.15,11.18-.36.69-.83,1.1-1.24,1.33,1.72,4.35,2.01,7.52.86,9.44-.38.64-.88,1.05-1.34,1.29.76,1.83,1.96,5.37.48,6.73-.49.44-1.06.67-1.65.67-.1,0-.21,0-.31-.02-2.26-.31-4.48-3.87-7.55-8.79-4.58-7.34-10.84-17.38-19.04-17.69-.1,0-.12.01-.14.04-.29.36-.56,1.8.9,7.39.79,3.07-.87,4.76-1.62,5.35-1.14.9-2.57,1.18-3.48.69-.58-.31-.89-.9-.83-1.61.08-1.05.05-5.05-3.58-12.55-.04-.05-.08-.1-.11-.16-1.38-2.84-1.9-7.42-2.44-12.27-.67-5.93-1.43-12.65-3.95-16.45-.02-.03-.04-.05-.05-.08-.83-1.19-10.56-15.18-18.56-24.7-.66-.79-1.53-1.74-2.53-2.83-6.27-6.85-17.94-19.59-21.38-39.96-1.51-8.92-4.22-17.74-8.07-26.2-1.93-4.24-3.71-8.47-5.35-12.67-1.99,6.91-8.34,29.31-9.66,38.86-.73,5.37,1.13,14.66,2.94,23.65,2.05,10.25,4,19.94,2.38,24.48-.13.34-.51.92-1.13,1.69.9,2.64,15.63,45.97,16.01,79.31.39,33.45-6,79.38-6.33,81.7.08.84.66,7.37.5,12.98-.1,3.56.41,7.04,1.53,10.35,3.86,11.4,9.55,35.71,4.31,70.35-7.06,46.64-3.19,68.53-1.73,74.42.13.15.2.34.18.56,0,.02-.01.05-.02.07.19.69.31,1.04.31,1.06.73,1.75,4.02,6.92,7.5,12.4,7.69,12.09,13.14,20.86,13.14,23.58,0,5.38-6.21,8.3-18.44,8.68-1.22.04-2.38.06-3.47.06-10.53,0-15.54-2.2-21.1-15.18-2.26-5.3-5.05-6.97-7.5-8.44-3.96-2.37-7.09-4.25-5.62-18.09,2.23-20.74,4.53-42.18-.35-59.18l-.19-.66c-4.78-16.64-13.68-47.6-9.76-81.8,0-.12.06-.23.13-.32-5.92-11.46-7.92-27.94-9.85-44.02-1.16-9.61-2.25-18.69-4.16-26.08-8.35-32.41-10.47-56.81-9.76-68.2-1.37.59-2.83.9-4.29.9-1.08,0-2.16-.18-3.2-.5.82,11.15-1.24,35.82-9.72,68.76-1.9,7.39-3,16.47-4.16,26.08-1.95,16.24-3.97,32.9-10.02,44.36,3.8,33.61-4.57,62.69-9.57,80.08l-.4,1.4c-4.88,17-2.58,38.44-.36,59.18,1.48,13.84-1.65,15.72-5.62,18.09-2.45,1.47-5.23,3.14-7.5,8.44-5.55,12.98-10.57,15.18-21.1,15.18-1.09,0-2.25-.02-3.47-.06-12.23-.38-18.43-3.3-18.43-8.68,0-2.72,5.44-11.49,13.14-23.59,3.48-5.47,6.76-10.64,7.49-12.39.07-.18,7.14-20.66-1.26-76.11-5.24-34.64.46-58.94,4.31-70.35,1.12-3.31,1.64-6.79,1.54-10.35-.05-1.45-.04-2.95,0-4.4,0-.04-.01-.07-.01-.11,0-.58.03-1.16.07-1.73.13-3.4.38-6.21.43-6.74-.33-2.32-6.71-48.25-6.33-81.7.38-32.61,14.47-74.79,15.94-79.08-.52-.77-.89-1.43-1.07-1.93-1.62-4.55.32-14.22,2.38-24.46,1.81-9,3.68-18.3,2.93-23.67-1.34-9.81-7.91-32.88-9.69-39.03-1.66,4.27-3.46,8.56-5.41,12.85-3.01,6.61-5.31,13.43-6.9,20.36,0,.15,0,.25,0,.29,0,.18-.07.33-.18.46-.37,1.69-.72,3.39-1.01,5.09-3.44,20.37-15.11,33.11-21.38,39.96-1,1.09-1.87,2.04-2.53,2.83-8.32,9.9-18.51,24.64-18.62,24.79,0,0-.02.02-.03.03-2.5,3.81-3.26,10.5-3.92,16.41-.55,4.85-1.06,9.43-2.45,12.27-.03.06-.07.12-.12.17-3.61,7.5-3.64,11.49-3.56,12.55.05.7-.25,1.29-.84,1.6-.9.49-2.33.2-3.48-.69-.74-.59-2.4-2.28-1.61-5.35,1.46-5.59,1.19-7.03.89-7.39-.02-.02-.03-.04-.13-.04-8.2.31-14.47,10.35-19.04,17.69-3.08,4.92-5.29,8.48-7.55,8.79-.11.02-.21.02-.32.02-.59,0-1.15-.23-1.64-.67-1.48-1.36-.29-4.89.48-6.73-.46-.24-.97-.65-1.35-1.29-1.14-1.92-.85-5.09.86-9.44-.4-.23-.88-.64-1.23-1.33-1.16-2.21-.44-5.98,2.14-11.18,2.46-4.97,6.28-10.83,9.96-16.49,4.41-6.77,8.96-13.76,11.07-19.05-.02-.16,0-.31.08-.44.57-1.48,10.82-27.95,14.97-45.82,5.12-21.99,18.13-34.42,20.16-36.25.04-35.28,6.8-44.68,8.3-46.34-1.09-16.21,1.58-38.35,20.19-52.55,10.58-8.06,21.43-9.9,23.03-10.13l32.38-22.67v-19.75c0-.26.14-.47.33-.61-6.5-8.12-7.38-16.53-7.49-18.92-1.73-.35-6.07-1.89-7.09-8.67-.81-5.46-.22-9.16,1.77-11.02,1.01-.94,2.14-1.17,2.92-1.19-.77-41.86,29.53-43.55,29.84-43.56h5.64c.33,0,30.63,1.69,29.85,43.56.77.02,1.91.25,2.92,1.19,1.99,1.85,2.58,5.56,1.76,11.02-1.01,6.78-5.36,8.32-7.08,8.67-.12,2.43-1.02,11.09-7.83,19.34.02.06.04.12.04.19v19.51h.01c.19.11.31.29.35.49l32.02,22.42c1.61.23,12.46,2.07,23.03,10.13,18.62,14.2,21.28,36.34,20.2,52.55,1.5,1.66,8.25,11.06,8.29,46.34.5.45,1.66,1.54,3.2,3.24.85.87,1.33,1.48,1.35,1.5.02.04.03.08.05.12,4.85,5.76,12.05,16.33,15.56,31.39,4.15,17.84,14.37,44.26,14.97,45.81.08.14.1.29.08.45,2.12,5.29,6.66,12.28,11.07,19.05,3.69,5.66,7.5,11.52,9.96,16.49ZM341.73,372.78c.5-1,.88-3.61-2.18-9.78-2.42-4.89-6.21-10.7-9.88-16.33-4.34-6.68-8.83-13.57-11.06-18.96l-18.93,9.08c2.37,4.13,3.08,10.37,3.76,16.41.52,4.61,1.01,8.97,2.22,11.6.04.05.08.1.11.17,2.61,5.36,4.03,10.4,3.81,13.49-.02.14.01.16.04.17.28.15,1.09.04,1.85-.55.64-.5,1.63-1.66,1.08-3.8-1.32-5.07-1.5-7.59-.61-8.7.32-.39.76-.6,1.29-.6h.08c8.99.34,15.5,10.78,20.25,18.39,2.53,4.06,4.92,7.89,6.48,8.11.18.02.43.01.75-.28.61-.57-.12-3.48-1.16-5.74-.1-.22-.09-.48.03-.69s.33-.35.57-.37c.02,0,.7-.11,1.17-.92.51-.9,1.03-3.24-1.27-8.75-.08-.21-.07-.45.04-.66.12-.2.32-.34.55-.37.01,0,.62-.13,1.01-.92ZM299.22,335.35l18.56-8.9c-4.09-3.66-9.19-4.71-13.26-2.65-3.91,1.98-5.86,6.25-5.3,11.55ZM317.34,324.18c-2.82-7.48-10.52-28.36-13.99-43.3-3.38-14.53-10.27-24.79-14.99-30.47l-32.35,13.84c.35,1.61.67,3.22.95,4.84,3.37,19.93,14.84,32.46,21.01,39.2,1.01,1.1,1.89,2.06,2.57,2.88,6.4,7.61,13.88,18.05,17.1,22.61-.03-5.1,2.22-9.31,6.2-11.32,4.16-2.1,9.22-1.39,13.5,1.72ZM255.95,262.64l31.39-13.42c-.31-.36-.62-.71-.91-1.02-2.25-2.23-6.8-5.8-12.87-5.8-2.41,0-5.05.56-7.87,1.98-7.88,3.97-9.43,14.68-9.74,18.26ZM283.15,243.8c-.06-19.41-2.22-30.59-4.03-36.56-1.67-5.51-3.36-7.7-3.87-8.27-11.91-.3-21.66-6.34-29.01-14.05,0,.08-.04.16-.08.24l-11.14,18.13.94,3.61c3.01,11.59,7.32,23.57,12.81,35.62,2.5,5.5,4.53,11.15,6.07,16.89.84-4.99,3.26-12.89,10.17-16.37,7.49-3.77,13.83-1.86,18.14.76ZM274.87,197.44c1.41-22.37-5.34-39.93-19.57-50.8-9.6-7.35-19.7-9.43-22.04-9.83-.79,1.51-3.33,7.46-1.36,19,1.06,6.28,5.52,16.59,13.08,25.48,6.07,7.14,16.15,15.64,29.89,16.15ZM270.49,685.93c0-2.49-7.46-14.22-12.91-22.78-3.67-5.77-6.84-10.75-7.61-12.63-.12-.29-.25-.65-.38-1.09-1.77-.21-6.84-.95-12.58-3.12-8.28-3.13-14.34-7.95-17.68-14.01-.49,6.89-1.23,13.89-1.98,20.82-1.37,12.89,1.26,14.46,4.9,16.65,2.52,1.51,5.65,3.38,8.11,9.13,5.79,13.53,10.68,14.6,23.14,14.21,7.75-.24,16.99-1.68,16.99-7.18ZM251.13,573.61c5.19-34.33-.44-58.36-4.25-69.64-.82-2.42-1.32-4.93-1.52-7.49-2.63,8.27-9.27,14.17-17.03,14.17-10.06,0-18.24-9.88-18.24-22.02s8.18-22.02,18.24-22.02c7.32,0,13.64,5.24,16.54,12.77.8-5.84,6.6-49.06,6.23-80.91-.36-31.17-13.37-71.22-15.64-77.96-9.56,11.1-46.09,46-50.73,50.43-.42.36-.86.68-1.31.96.03.1.04.2.04.31-.82,11.01,1.22,35.51,9.68,68.31,1.92,7.49,3.02,16.62,4.19,26.28,2.39,19.85,5.1,42.34,16.15,52.16.31.28.34.75.06,1.06-.14.17-.35.25-.56.25-.17,0-.35-.06-.5-.19-2.16-1.92-4.01-4.26-5.6-6.93-3.42,33.24,5.21,63.29,9.88,79.56l.19.66c3.08,10.72,3.35,23.11,2.58,35.95.02.03.05.05.06.09,5.96,14.26,24.64,17.76,29.58,18.45-2.02-8.5-4.37-32.45,1.96-74.25ZM244.88,184.37c.12-.19.32-.31.53-.34-8.71-9.52-13.86-21.27-14.99-27.96-.48-2.8-.69-5.27-.74-7.45-.03,0-.06.02-.09.02h-37.72c-4.52,0-8.88,1.33-12.59,3.86l-1.02.69s-.05.03-.08.04v45.92s.04.01.06.02l18.84,9.07c2.06,1.03,4.27,1.56,6.57,1.56h17.43c5.05,0,9.84-2.68,12.48-7l11.32-18.43ZM245.06,488.63c0-11.31-7.51-20.52-16.73-20.52s-16.74,9.21-16.74,20.52,7.51,20.52,16.74,20.52,16.73-9.21,16.73-20.52ZM230.91,269.13c1.52-10.99,9.58-38.64,10.27-40.99-2.66-7.04-4.9-14.02-6.68-20.86l-.6-2.34c-3.03,3.95-7.8,6.36-12.82,6.36h-17.43c-2.54,0-4.97-.58-7.23-1.71l-18.24-8.79v171c0,.1-.02.19-.06.28,2-.1,3.98-.85,5.6-2.25,20.53-19.58,51.5-49.85,52.58-52.87,1.48-4.16-.51-14.08-2.43-23.69-1.83-9.1-3.72-18.52-2.96-24.14ZM231.87,136.22l-31.35-21.96-21.38,36.56c3.8-2.4,8.18-3.68,12.73-3.68h37.72s.05.01.08.01c.07-6.09,1.5-9.58,2.2-10.93ZM214.14,64.43c.92-6.13-.19-8.65-1.29-9.68-1.12-1.06-2.46-.79-2.52-.78-.04,0-.08-.01-.13,0-.01,0-.03,0-.05,0s-.04-.02-.06-.02c-.07,0-.14-.02-.2-.05-.06-.02-.11-.05-.16-.08-.01-.01-.03-.01-.04-.02-.02-.02-.04-.05-.06-.08-.04-.04-.1-.08-.13-.13l-4.01-7.15c-6.42-11.48-16.6-18.34-27.23-18.34s-20.82,6.86-27.24,18.34l-3.95,7.04c-.05.13-.11.24-.22.32-.13.12-.3.16-.47.16h-.13s-.02,0-.04,0c-.01-.01-.18-.04-.44-.04-.51,0-1.34.12-2.08.82-1.09,1.03-2.2,3.55-1.28,9.68,1.08,7.24,6.3,7.49,6.36,7.49.41,0,.73.36.72.77,0,.05-.09,5.19,2.73,11.45,1.29,2.89,3.36,6.31,6.63,9.66,3.28,3.35,7.76,6.61,13.89,9.15,1.71.7,3.62,1.08,5.53,1.08s3.79-.37,5.53-1.09c12.27-5.08,17.93-13.03,20.53-18.8,2.81-6.26,2.73-11.4,2.73-11.45-.01-.41.31-.76.72-.77.21-.01,5.28-.31,6.36-7.49ZM206.8,45.72l2.65,4.72c-.17-38.43-27.22-40.01-28.39-40.06h-5.58c-1.15.05-28.17,1.62-28.38,40l2.61-4.66c6.69-11.96,17.36-19.11,28.55-19.11s21.85,7.15,28.54,19.11ZM177.45,150.73l21.88-37.41c-.05-.1-.08-.21-.08-.33v-18.68c-3.49,3.73-8.27,7.28-14.87,10.02-1.93.8-3.99,1.2-6.11,1.2s-4.22-.42-6.1-1.2c-7-2.9-11.96-6.72-15.5-10.7v18.96s.04.04.05.06l20.73,38.08ZM176.73,372.05c-.03-.08-.05-.16-.05-.25v-170.74l-17.04,8.53c-2.26,1.13-4.69,1.71-7.22,1.71h-17.43c-5.08,0-9.89-2.46-12.91-6.48l-.64,2.46c-1.78,6.82-4.01,13.77-6.64,20.78.06.08.11.17.14.27.09.3,8.63,29.36,10.19,40.78.78,5.62-1.12,15.06-2.95,24.18-1.92,9.59-3.92,19.5-2.44,23.65.16.45.51,1.06,1.01,1.8.07.07.12.15.16.23,4.75,6.81,22.67,24.84,50.4,50.64,1.55,1.43,3.46,2.25,5.42,2.44ZM176.68,199.38v-46.4l-.71-.48c-3.71-2.53-8.06-3.86-12.59-3.86h-36.98c-.05,2.18-.26,4.64-.74,7.43-1.13,6.69-6.28,18.44-14.99,27.96.21.04.4.15.51.34l11.33,18.43c2.64,4.32,7.42,7,12.48,7h17.43c2.3,0,4.5-.53,6.55-1.55l17.71-8.87ZM175.62,150.51l-19.86-36.49-31.59,22.12c.68,1.28,2.17,4.78,2.24,11h36.97c4.35,0,8.55,1.16,12.24,3.37ZM163.26,441.48c8.45-32.8,10.49-57.3,9.67-68.31-.01-.22.07-.42.21-.57-1.02-.47-1.99-1.09-2.85-1.89-16.12-15-42.04-39.61-49.82-49.83-2.58,7.75-15.16,46.96-15.51,77.59-.36,31.21,5.2,73.36,6.18,80.51,2.97-7.31,9.2-12.37,16.39-12.37,10.05,0,18.23,9.88,18.23,22.02s-8.18,22.02-18.23,22.02c-7.61,0-14.13-5.65-16.87-13.66-.22,2.38-.71,4.72-1.48,6.98-3.81,11.28-9.44,35.32-4.24,69.64,6.33,41.84,3.97,65.79,1.95,74.27,4.7-.64,23.57-4.05,29.65-18.33-.78-12.93-.52-25.4,2.57-36.18l.41-1.4c4.87-16.92,12.94-44.99,9.73-77.43-1.53,2.49-3.3,4.68-5.34,6.5-.15.13-.32.19-.5.19-.21,0-.41-.08-.56-.25-.28-.31-.25-.78.06-1.06,11.05-9.82,13.76-32.31,16.15-52.16,1.17-9.66,2.27-18.79,4.2-26.28ZM144.26,488.63c0-11.31-7.5-20.52-16.73-20.52s-16.54,8.99-16.72,20.12c0,.16-.01.31-.01.47.03,11.28,7.52,20.45,16.73,20.45s16.73-9.21,16.73-20.52ZM133.81,669.77c3.64-2.19,6.27-3.76,4.9-16.65-.73-6.85-1.47-13.77-1.96-20.6-3.37,5.96-9.38,10.7-17.56,13.79-5.91,2.24-11.1,2.96-12.72,3.14-.13.42-.26.79-.38,1.07-.77,1.88-3.94,6.86-7.61,12.63-5.44,8.56-12.9,20.29-12.9,22.78,0,5.5,9.23,6.94,16.98,7.18,12.47.4,17.35-.68,23.14-14.21,2.46-5.75,5.59-7.62,8.11-9.13ZM124.18,155.81c1.99-11.66-.59-17.57-1.37-19.02-2.03.33-12.37,2.34-22.19,9.85-14.22,10.87-20.97,28.44-19.57,50.81,13.83-.45,23.96-9,30.05-16.16,7.56-8.89,12.02-19.2,13.08-25.48ZM120.96,203.15l-11.05-17.99c-.05-.07-.07-.15-.08-.23-7.38,7.75-17.18,13.8-29.16,14.04-1.11,1.27-7.8,10.35-7.89,44.9,4.31-2.66,10.69-4.63,18.24-.83,6.82,3.44,9.26,11.16,10.14,16.17,1.53-5.67,3.54-11.25,6.01-16.69,5.49-12.04,9.8-24.02,12.82-35.62l.97-3.75ZM68.65,249.2l31.43,13.44c-.31-3.58-1.86-14.29-9.74-18.26-11.18-5.63-19.42,2.26-21.69,4.82ZM99.94,264.21l-32.35-13.83c-4.72,5.67-11.62,15.94-15.01,30.5-3.48,14.94-11.17,35.83-14,43.3,4.28-3.11,9.34-3.82,13.5-1.72,3.99,2.02,6.24,6.23,6.21,11.34,3.2-4.54,10.7-15.01,17.11-22.63.69-.82,1.56-1.78,2.57-2.88,6.17-6.74,17.65-19.27,21.01-39.2.28-1.63.61-3.26.96-4.88ZM56.71,335.35c.55-5.3-1.39-9.57-5.3-11.55-4.08-2.06-9.18-1.01-13.26,2.65l18.56,8.9ZM56.24,336.79l-18.93-9.08c-2.22,5.39-6.71,12.28-11.06,18.96-3.66,5.63-7.45,11.44-9.88,16.33-3.05,6.17-2.67,8.78-2.18,9.78.39.79,1.01.92,1.03.92.23.05.42.19.53.39s.13.43.04.64c-2.3,5.51-1.78,7.85-1.26,8.75.46.81,1.15.92,1.17.92.24.03.45.18.57.39.11.2.13.45.03.67-1.04,2.26-1.78,5.17-1.16,5.74.32.29.56.3.74.28,1.56-.22,3.95-4.05,6.48-8.11,4.76-7.61,11.26-18.05,20.26-18.39.55-.04,1.03.19,1.36.6.89,1.11.72,3.63-.61,8.71-.55,2.13.45,3.29,1.09,3.79.75.59,1.57.7,1.84.55.03-.01.06-.03.05-.17-.23-3.09,1.19-8.13,3.8-13.49.03-.07.07-.12.12-.18,1.21-2.62,1.7-6.98,2.22-11.59.68-6.04,1.38-12.28,3.75-16.41Z" />
              </g>
            </svg>
          </Stack>
        </Stack>
        <Stack position={"relative"}
          sx={{
            alignItems: "center",
          }}
        >
          {/** Neck label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "80px",
              left: "123px",
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${neckPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: neckPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "58px",
              left: "145px",
            }}
          >
            {neckPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {neckPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={neckPercentage != 0 ? "" : "#9C9C9C"}
            >
              Neck
            </MLTypography>
          </Stack>

          {/** lower back label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "225px",
              left: "125px",
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${lowerBackPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: lowerBackPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "200px",
              left: "145px",
            }}
          >
            {lowerBackPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {lowerBackPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={lowerBackPercentage != 0 ? "" : "#9C9C9C"}
            >
              Lower back
            </MLTypography>
          </Stack>

          {/** Forearms label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "220px",
              left: "40px",
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${forearmsPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: forearmsPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "145px",
              left: "4px",
            }}
          >
            {forearmsPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {forearmsPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={forearmsPercentage != 0 ? "" : "#9C9C9C"}
            >
              Forearms
            </MLTypography>
          </Stack>

          {/** Fingers label */}
          <Box
            position={"absolute"}
            sx={{
              border: "1.5px black solid",
              borderRadius: "10px",
              backgroundColor: "#E3DDFF",
              height: "10px",
              width: "10px",
              top: "275px",
              left: "15px",
            }}
          />
          <Stack
            position={"absolute"}
            sx={{
              border: `0.5px ${fingersPercentage != 0 ? "#E3DDFF" : "#9C9C9C"} solid`,
              borderRadius: "10px",
              backgroundColor: fingersPercentage != 0 ? "#E3DDFF" : "white",
              padding: "8px",
              top: "305px",
              left: "5px",
            }}
          >
            {fingersPercentage != 0 ? (
              <MLTypography
                variant="body1"
                fontSize={"20px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                {fingersPercentage}%
              </MLTypography>
            ) : (<></>)}
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1.2}
              color={fingersPercentage != 0 ? "" : "#9C9C9C"}
            >
              Fingers
            </MLTypography>
          </Stack>


          <svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" width="256" height="532" viewBox="0 0 355 705">
            <path d="M345.09,370.46c2.72,5.49,3.49,9.45,2.27,11.77-.36.7-.86,1.14-1.32,1.41,1.82,4.6,2.13,7.95.93,9.97-.41.68-.95,1.11-1.43,1.36.8,1.92,2.07,5.67.5,7.08-.51.47-1.08.71-1.69.71-.12,0-.23-.01-.34-.03-2.37-.33-4.72-4.08-7.96-9.27-4.84-7.75-11.48-18.36-20.17-18.71-.14,0-.16.03-.19.07-.31.38-.6,1.91.93,7.84.83,3.21-.9,4.98-1.68,5.6-1.21.94-2.71,1.24-3.66.73-.6-.32-.92-.93-.86-1.66.18-2.28-.74-7.01-3.88-13.44-1.44-2.94-2.17-7.7-2.95-12.73-.98-6.35-2.09-13.56-4.81-17.64,0,0,0-.02-.02-.04-.01-.01-.02-.01-.02-.02-.06-.1-5.63-10.44-17.64-25.25-.75-.91-1.74-2.01-2.9-3.29-6.39-7.04-18.28-20.14-21.79-40.79-1.67-9.86-4.75-19.56-9.18-28.81l-.14-.29c-1.73-3.64-3.21-7.45-4.42-11.35-1.57,6.09-7.71,30.21-9.12,40.42-.16,1.17-.22,2.63-.19,4.32.24.16.38.45.33.76-.03.2-.15.36-.3.47.19,5.41,1.14,12.43,2.07,19.25,1.54,11.23,2.98,21.83,1.43,26.7,1.21,3.51,16.12,47.64,16.51,81.66.4,34.51-6.2,81.89-6.54,84.25.08.85.68,7.6.52,13.38-.11,3.66.42,7.25,1.58,10.66,3.98,11.76,9.86,36.82,4.45,72.53-5.92,39.02-5.56,64.42-3.21,74.16.03.06.04.13.05.19.49,1.98,1.07,3.3,1.69,3.9,3.59,3.46,9.9,5.42,15.47,7.15,5.91,1.83,11.01,3.41,11.99,6.48.38,1.21.22,2.34-.46,3.28-2.01,2.74-7.79,3.06-10.19,3.07-.88.01-1.71.21-2.44.58l-.62.31c-2.56,1.31-7.88,4.02-17.7,3.74-5.76-.16-9.56,1.28-12.93,2.56-2.79,1.06-5.33,2.02-8.47,2.02-1.82,0-3.83-.32-6.2-1.14-7.12-2.46-6.39-9.68-5.07-22.78l.28-2.75c.21-2.14.43-4.27.64-6.39,0-.04,0-.07.01-.1,1.96-19.9,3.27-38.55-1.08-53.74l-.34-1.17c-4.84-16.82-13.84-48.09-9.99-82.79-4.49-10.65-7.05-27.64-9.52-44.08-1.62-10.77-3.15-20.93-5.12-28.57-8.45-32.73-10.97-64.73-10.06-76.99.01-.09.04-.17.07-.24-.63-.69-1.27-1.45-1.94-2.31-.7-.88-2.08-.86-2.74-.01-.76.99-1.57,1.91-2.38,2.79v.02c.86,11.56-1.85,43.01-10.56,76.74-1.96,7.63-3.49,17.8-5.11,28.56-2.47,16.39-5.02,33.32-9.47,43.98,3.88,34.67-5.12,65.97-9.96,82.8l-.37,1.27c-4.33,15.13-3.05,33.69-1.1,53.5.12.2.13.42.06.62.2,2.03.4,4.06.61,6.11l.27,2.67c1.33,13.15,2.06,20.4-5.06,22.86-2.37.82-4.39,1.14-6.2,1.14-3.14,0-5.68-.96-8.47-2.02-3.37-1.27-7.19-2.73-12.95-2.56-9.79.29-15.12-2.43-17.68-3.74l-.61-.31c-.76-.37-1.59-.57-2.47-.58-2.38-.01-8.16-.33-10.17-3.07-.68-.94-.84-2.07-.46-3.28.97-3.07,6.07-4.65,11.98-6.49,5.58-1.72,11.89-3.68,15.48-7.14,3.65-3.55,5.72-30.88-1.47-78.25-5.41-35.71.47-60.77,4.45-72.53,1.16-3.41,1.69-7,1.58-10.66-.16-5.78.44-12.52.52-13.38-.34-2.36-6.94-49.74-6.54-84.25.39-33.77,15.08-77.5,16.48-81.58-1.58-4.8.11-15.31,1.91-26.45,1.16-7.21,2.36-14.67,2.56-20.23.07-2,.02-3.6-.16-4.89-1.38-10.13-8.28-34.25-10.14-40.64-1.22,3.96-2.71,7.84-4.48,11.55l-.15.31c-3.03,6.3-7.08,16.42-9.17,28.8-3.51,20.65-15.4,33.75-21.79,40.79-1.16,1.28-2.16,2.38-2.9,3.29-12.02,14.81-17.6,25.15-17.66,25.25,0,0-.01,0-.02.02,0,.02,0,.03-.02.04-2.72,4.08-3.83,11.28-4.8,17.64-.77,5.03-1.5,9.79-2.94,12.73-3.14,6.41-4.07,11.15-3.88,13.44.06.73-.26,1.34-.86,1.66-.96.51-2.46.21-3.66-.73-.78-.62-2.51-2.39-1.68-5.6,1.53-5.92,1.24-7.46.93-7.84-.03-.04-.06-.06-.19-.07-8.69.35-15.33,10.96-20.17,18.7-3.25,5.2-5.6,8.95-7.97,9.28-.12.02-.23.03-.34.03-.62,0-1.19-.24-1.71-.71-1.57-1.41-.28-5.16.53-7.08-.49-.25-1.02-.68-1.43-1.36-1.21-2.02-.89-5.37.93-9.97-.46-.27-.96-.71-1.33-1.41-1.22-2.32-.46-6.28,2.26-11.77,2.6-5.24,6.64-11.42,10.54-17.39,4.78-7.33,9.72-14.91,11.88-20.55.02-.06.06-.1.09-.15,1.34-3.46,11.17-29.14,15.25-46.68,5.29-22.69,18.76-35.51,20.84-37.38.04-36.43,7.02-46.1,8.55-47.78-1.29-19.22,2.98-34.83,12.7-46.4,8.05-9.57,18.07-14.27,25.07-16.52,5.49-1.77,10.66-4.33,15.35-7.6l25.18-17.59,3.6-16.27c-.06-.04-.13-.07-.18-.12-9.69-9.12-11.3-21.14-11.55-24.03-1.74-.34-6.29-1.9-7.35-8.92-.84-5.62-.23-9.43,1.82-11.34,1.05-.98,2.24-1.21,3.04-1.24-.82-43.16,30.46-44.89,30.78-44.91h5.83c.35.02,31.63,1.75,30.82,44.91.8.03,1.98.26,3.03,1.24,2.05,1.9,2.66,5.72,1.82,11.34-1.06,7.02-5.62,8.58-7.36,8.92-.27,2.82-1.94,14.39-11.19,22.43-.06.05-.13.09-.21.12l4,18.07,24.89,17.39c4.69,3.27,9.85,5.83,15.35,7.6,6.99,2.25,17.02,6.95,25.07,16.52,9.71,11.57,13.98,27.18,12.7,46.4,1.52,1.68,8.47,11.31,8.55,47.53.23.28.36.45.37.47.05.06.07.14.09.21,3.12,2.92,15.39,15.54,20.38,36.95,4.11,17.66,14.04,43.57,15.28,46.76.01.02.03.04.04.07,2.16,5.62,7.08,13.17,11.85,20.47,3.91,6,7.96,12.21,10.57,17.47ZM346.06,381.49c.53-1.06.93-3.82-2.31-10.36-2.57-5.19-6.6-11.36-10.49-17.32-3.4-5.21-6.87-10.54-9.4-15.26-.07.08-.15.14-.25.19l-20.94,10.02c1.09,3.69,1.76,7.97,2.4,12.17.76,4.92,1.48,9.56,2.81,12.3,2.78,5.7,4.29,11.01,4.04,14.22-.02.17.03.2.07.22.32.17,1.19.06,2.02-.59.68-.54,1.74-1.78,1.15-4.05-1.38-5.35-1.56-8-.64-9.15.35-.43.85-.67,1.42-.62,9.49.37,16.36,11.37,21.38,19.41,2.68,4.29,5.22,8.34,6.89,8.58.2.03.47.02.83-.31.68-.61-.07-3.65-1.2-6.1-.1-.22-.09-.48.03-.69s.34-.35.58-.37c.02-.01.76-.11,1.25-.98.55-.96,1.11-3.44-1.33-9.29-.09-.21-.08-.46.04-.66.11-.2.31-.34.54-.37.02,0,.68-.14,1.11-.99ZM302.93,346.97l19.49-9.32c-4.28-4.03-9.74-5.23-14.07-3.04-4.15,2.1-6.15,6.69-5.42,12.36ZM322.38,335.62c-.31-.66-.6-1.3-.85-1.93-.03-.04-.06-.08-.08-.13l-.2-.5c-1.85-4.79-11.29-29.53-15.36-47.03-4.88-20.91-16.78-33.21-19.87-36.13l-30.46,13.48c.95,3.47,1.73,6.97,2.32,10.5,3.44,20.21,15.14,33.11,21.43,40.03,1.17,1.3,2.18,2.41,2.95,3.35,11.86,14.61,17.5,24.93,17.79,25.47.46.69.87,1.46,1.24,2.27-.05-5.28,2.26-9.65,6.39-11.73,4.54-2.3,10.12-1.35,14.7,2.35ZM285.02,248.71s-.01-.04-.01-.06v-.03c-.9-1.03-2.7-2.84-5.19-4.28-2.48-1.43-5-2.14-7.55-2.14-2.39,0-4.81.63-7.24,1.89-7.25,3.78-8.72,13.95-9.01,17.44l29-12.82ZM284.99,246.45c-.15-19.19-2.31-30.35-4.14-36.36-2.06-6.77-4.14-8.69-4.16-8.71-.17-.15-.27-.38-.25-.62,1.03-14.67-.48-50.07-36.71-61.72-4.92-1.59-9.59-3.79-13.9-6.56.02.29-.12.58-.39.72-5.4,2.83-10.99,5.16-16.7,7,0,.04.02.07.02.11v74.45c4.86,2.17,9.56,4.88,14.02,8.11,8.88,6.41,16.27,7.72,19.06,7.99-3.45-12.68-3.99-26.27-1.53-39.41l1.27-6.26c.08-.4.47-.66.88-.58s.67.48.59.88l-1.27,6.24c-2.45,13.12-1.87,26.7,1.67,39.33.21.13.35.34.36.6,0,.13-.03.24-.09.35,1.26,4.27,2.84,8.42,4.72,12.36l.14.3c2.42,5.06,4.44,10.26,6.05,15.54.51-4.47,2.43-13.66,9.71-17.45,9.67-5.04,17.29.42,20.65,3.69ZM277.72,682.36c.41-.55.49-1.19.25-1.94-.74-2.33-5.72-3.88-11-5.51-5.73-1.78-12.23-3.79-16.07-7.5-.64-.62-1.25-1.88-1.8-3.77-2.13,1.24-8.17,4.28-16.05,4.28-4.65,0-9.93-1.09-15.43-4.19-.17,1.75-.35,3.51-.53,5.28l-.28,2.74c-1.28,12.75-1.93,19.14,4.07,21.21,5.95,2.06,9.35.77,13.65-.86,3.49-1.33,7.45-2.83,13.51-2.66,9.43.29,14.33-2.23,16.97-3.58.23-.12.44-.22.63-.32.94-.47,1.98-.71,3.1-.73,4.45-.02,7.89-.96,8.98-2.45ZM251.93,587.85c5.36-35.39-.45-60.19-4.39-71.82-1.22-3.58-1.78-7.35-1.66-11.19.17-6.05-.51-13.2-.52-13.28,0-.05,0-.11.01-.17.07-.49,6.94-49.01,6.53-84.11-.38-33.4-14.88-76.67-16.38-81.06l-15.43,4.01c-14.16,3.66-28.73,5.49-43.29,5.49s-28.85-1.79-42.88-5.38l-16.37-4.19c-.06-.02-.11-.05-.17-.09-1.14,3.33-16.04,47.36-16.43,81.22-.41,35.1,6.46,83.62,6.53,84.11.01.06.01.12.01.17-.01.08-.69,7.23-.52,13.28.11,3.85-.45,7.61-1.66,11.19-3.94,11.63-9.75,36.43-4.39,71.82,6.09,40.13,5.55,64.03,3.23,74.22,14.95,8.87,28.37,1.65,30.93.1-1.96-19.92-3.24-38.6,1.16-53.96l.36-1.27c5.03-17.47,13.35-46.4,10.18-79.73-1.75,3.44-3.65,5.76-5.78,7.02-.12.07-.25.11-.38.11-.26,0-.51-.13-.65-.37-.21-.36-.09-.82.26-1.03,2.3-1.36,4.35-4.19,6.25-8.63-.02-.26.09-.51.28-.67,4.3-10.5,6.81-27.16,9.24-43.29,1.62-10.8,3.15-21,5.14-28.71,8.36-32.39,11.17-62.59,10.59-74.97-21.15,20.43-53.22,4.55-53.54,4.39-.37-.19-.52-.64-.33-1.01.18-.37.64-.52,1-.33.33.16,32.48,16.08,52.82-5.09.92-.96,1.81-1.99,2.66-3.08.61-.8,1.57-1.27,2.56-1.27s1.91.46,2.54,1.26c.82,1.05,1.58,1.93,2.33,2.71,20.32,21.74,52.87,5.63,53.2,5.47.37-.19.82-.04,1,.33.19.37.04.82-.33,1.01-.2.1-12.21,6.05-26.35,6.05-9.23,0-19.37-2.56-27.72-10.93-.69,12.86,1.9,43.78,10.08,75.46,1.99,7.71,3.53,17.91,5.15,28.72,2.42,16.09,4.93,32.71,9.22,43.21.25.14.4.42.36.72-.01.05-.01.09-.02.12,1.86,4.32,3.95,7.19,6.2,8.53.36.21.48.67.26,1.03-.14.24-.38.37-.64.37-.13,0-.26-.04-.38-.11-2.12-1.25-4-3.54-5.73-6.92-3.11,33.46,5.51,63.43,10.2,79.72l.34,1.18c4.4,15.34,3.12,34,1.17,53.89,15.08,8.92,28.55,1.49,30.93.03-2.33-10.15-2.88-34.07,3.22-74.28ZM232.07,273.9c1.51-10.97,8.41-37.75,9.4-41.56-3.15-.32-10.58-1.76-19.57-8.25-12.85-9.31-27.62-14.23-42.7-14.23h-4.98c-15.06,0-29.82,4.92-42.7,14.23-9.64,6.94-17.47,8.1-20.17,8.3,1.51,5.17,8.92,30.89,10.38,41.51.16,1.18.22,2.59.19,4.27,3.54-.5,7.11-1.69,10.55-3.56,12.95-6.95,28.34-10.63,44.5-10.63,7.88,0,15.59.87,22.92,2.57,7.72,1.8,14.98,4.51,21.58,8.06,3.38,1.83,6.87,3.01,10.38,3.52-.01-1.63.05-3.06.22-4.23ZM235.59,324.69c1.23-4.78-.21-15.34-1.61-25.58-.94-6.88-1.91-13.97-2.09-19.46-3.77-.52-7.51-1.76-11.13-3.72-6.48-3.49-13.62-6.15-21.21-7.92-7.22-1.68-14.82-2.53-22.58-2.53-15.92,0-31.06,3.62-43.79,10.45-3.66,1.98-7.46,3.24-11.32,3.76-.26,5.57-1.42,12.78-2.54,19.77-1.65,10.23-3.34,20.77-2.07,25.41.19-.16.43-.24.68-.18l16.36,4.19c27.96,7.15,57.49,7.12,85.42-.1l15.73-4.09s.1,0,.15,0ZM224.87,131.83c-.3-.19-.6-.39-.89-.59l-25.15-17.57s-.06.02-.09.02h-44.89l-25.12,17.55c-.44.3-.88.59-1.32.88l1.27.63c30.21,15.15,66.12,14.82,96.06-.88.04-.02.08-.02.13-.04ZM213.22,62.85c.95-6.32-.2-8.93-1.33-10-1.17-1.1-2.58-.82-2.64-.81-.22.05-.46-.01-.63-.16-.18-.15-.28-.37-.27-.59.48-17.59-4.43-30.81-14.22-38.22-7.39-5.6-15.05-5.97-15.12-5.98h-5.77s-7.7.38-15.09,5.98c-9.79,7.41-14.69,20.63-14.2,38.22.01.23-.09.45-.27.59-.18.15-.42.21-.64.16-.06-.01-1.47-.29-2.65.82-1.13,1.07-2.28,3.67-1.33,10,1.11,7.41,6.37,7.73,6.6,7.74.38.01.7.32.71.71.01.14.72,13.41,10.65,23.16l6.11-27.62c.31-1.66,1.79-2.89,3.51-2.89h19.67c1.7,0,3.18,1.22,3.52,2.91l5.76,26.05c9.49-8.6,10.31-21.48,10.31-21.61.03-.39.34-.7.73-.71.22,0,5.47-.33,6.59-7.75ZM207.26,214.09v-73.41c-10.22,3.13-20.82,4.71-31.42,4.71-9.96,0-19.92-1.4-29.57-4.17v72.84c8.94-3.76,18.39-5.7,27.95-5.7h4.98c9.61,0,19.1,1.95,28.06,5.73ZM198.32,112.19l-9.96-45.01c-.2-1-1.06-1.72-2.05-1.72h-19.67c-1,0-1.86.71-2.04,1.69l-9.97,45.04h43.69ZM144.77,214.72v-73.95c-5.72-1.73-11.33-3.95-16.76-6.67l-1.78-.89c-.09-.05-.17-.11-.23-.19-4.07,2.51-8.43,4.54-13.02,6.02-36.23,11.65-37.74,47.05-36.71,61.72.02.24-.08.47-.26.63,0,.01-2.1,1.93-4.15,8.7-1.84,6.04-4.01,17.28-4.15,36.66,3.19-3.21,10.98-9.19,20.96-3.99,6.92,3.6,8.99,12.08,9.62,16.75,1.59-5.09,3.55-10.09,5.83-14.84l.15-.31c1.99-4.17,3.64-8.55,4.92-13.03,3.6-12.65,4.2-26.35,1.73-39.61l-1.26-6.23c-.08-.4.18-.8.59-.88.4-.08.8.18.88.58l1.27,6.24c2.45,13.21,1.91,26.84-1.54,39.48,1.97-.1,9.97-.97,19.78-8.04,4.5-3.25,9.24-5.97,14.13-8.15ZM131.97,692.96c6-2.07,5.35-8.49,4.06-21.29l-.27-2.66c-.18-1.75-.35-3.48-.52-5.21-2.36,1.33-8.21,4.12-15.76,4.12-4.73,0-10.11-1.12-15.71-4.35-.55,1.93-1.17,3.21-1.82,3.84-3.85,3.71-10.34,5.72-16.07,7.5-5.28,1.63-10.27,3.18-11,5.5-.24.76-.16,1.4.25,1.95,1.09,1.49,4.53,2.43,8.97,2.45,1.11.02,2.15.26,3.11.73.19.1.4.2.63.32,2.64,1.35,7.55,3.87,16.95,3.58,6.04-.17,10.03,1.33,13.53,2.66,4.3,1.63,7.7,2.92,13.65.86ZM97.19,263.26l-30.41-13.45c-2.93,2.74-15.04,15.1-19.96,36.22-4.07,17.5-13.52,42.24-15.37,47.03l-.2.5c-.03.08-.07.14-.12.2-.15.38-.32.76-.49,1.15,4.38-3.1,9.53-3.79,13.77-1.64,4.55,2.3,6.91,7.38,6.29,13.42.54-1.47,1.18-2.81,1.94-3.96.21-.38,5.86-10.75,17.81-25.47.76-.94,1.78-2.05,2.95-3.35,6.28-6.92,17.99-19.82,21.42-40.03.6-3.54,1.4-7.09,2.37-10.62ZM67.94,248.68l29.05,12.85c-.29-3.49-1.76-13.66-9.01-17.44-5.01-2.61-9.99-2.52-14.79.25-2.54,1.47-4.37,3.33-5.25,4.34ZM49.98,348.89s-.08.01-.11.01c-.11,0-.22-.02-.33-.07l-20.85-9.99c-2.53,4.67-5.95,9.92-9.3,15.05-3.87,5.93-7.88,12.08-10.45,17.24-3.26,6.6-2.82,9.35-2.27,10.41.43.81,1.08.94,1.09.94.22.03.43.17.54.37s.13.45.04.66c-2.47,5.9-1.88,8.38-1.31,9.33.49.83,1.22.94,1.23.94.24.02.45.16.57.37s.13.47.03.69c-1.13,2.45-1.89,5.49-1.21,6.1.36.32.63.33.83.31,1.68-.24,4.22-4.3,6.91-8.59,5.02-8.03,11.9-19.03,21.38-19.4.57-.05,1.07.19,1.41.62.93,1.15.75,3.8-.64,9.15-.58,2.27.47,3.51,1.16,4.05.83.65,1.7.76,2.02.59.04-.02.09-.05.07-.22-.26-3.23,1.25-8.55,4.03-14.22,1.34-2.74,2.05-7.38,2.81-12.3.63-4.15,1.28-8.38,2.35-12.04ZM49.15,346.98c.73-5.67-1.27-10.27-5.42-12.37-4.33-2.19-9.78-.99-14.06,3.04l19.48,9.33Z" />
          </svg>
        </Stack>
      </Stack>
    </>
  );
}

export default DiscomfortArea;
