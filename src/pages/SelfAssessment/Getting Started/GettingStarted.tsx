import React, { useState } from 'react';
import { Box, Stack, useMediaQuery, useTheme } from "@mui/material";
import { desktop, tablet } from '../../../responsiveStyles';
import MLBanner from '../../../components/ui/MLBanner/MLBanner';
import AIIcon from './svgAssets/AIIcon';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import SurveyIcon from './svgAssets/SurveyIcon';
import MLButton from '../../../components/ui/MLButton/MLButton';
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import CheckedIcon from '../Work Setup/SvgIcon/CheckedIcon';
import { useGetIdentity, useOne } from '@refinedev/core';
import MLContainer from '../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer';
import IIdentity from '../models/IIdentity';
import { useNavigate } from 'react-router-dom';

type AssessmentType = "ai" | "survey" | null;

interface AssessmentBoxProps {
  type: AssessmentType;
  title: string;
  icon: React.ReactNode;
  isSelected: boolean;
  onClick: () => void;
}

interface MobileAssessmentBoxProps extends AssessmentBoxProps { }

interface DesktopAssessmentBoxProps extends AssessmentBoxProps {
  description: string;
  bulletPoints: string[];
}

const GettingStarted: React.FC = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { data: identity } = useGetIdentity<IIdentity>();

  const { data: userData } = useOne({
    resource: "users",
    id: identity?.id,
    queryOptions: {
      enabled: !!identity,
    },
    meta: {
      populate: {
        employee: {
          fields: ["id", "name", "countryCode", "contact", "email"],
        }
      },
    },
  });

  const [selectedAssessment, setSelectedAssessment] = useState<AssessmentType>(null);

  // AI assessment descriptions and bullet points (used for both desktop and mobile)
  const aiDescription = "Get instant ergonomics insights with AI. Upload a photo of your working posture, and get personalized recommendations.";
  const aiBulletPoints = [
    "AI-powered posture and workspace analysis",
    "Instant feedback with visual recommendations",
    "~2 minutes to complete"
  ];

  // Survey assessment descriptions and bullet points (used for both desktop and mobile)
  const surveyDescription = "Answer a few simple questions about your workspace and posture to receive tailored ergonomics recommendations.";
  const surveyBulletPoints = [
    "Suited for environments where photo uploads are not possible.",
    "Personalized insights based on your responses",
    "Takes ~3-5 minutes to complete"
  ];

  const handleBeginAssessment = () => {
    if (selectedAssessment === "ai") {
      navigate(`/self-assessment/ai`, {
        state: { fetchedUser: userData?.data }, // Pass fetchedUser data
      });
    }
    if (selectedAssessment === "survey") {
      navigate("/self-assessment/new");
      return window.location.reload();
    }
  }

  // Mobile version of AI Assessment Box
  const MobileAssessmentBox: React.FC<MobileAssessmentBoxProps> = ({ type, title, icon, isSelected, onClick }) => (
    <Stack
      sx={{
        padding: "15px",
        backgroundColor: isSelected ? "#E3DDFF" : "#FFFFFF",
        border: isSelected ? "1.5px solid #7856FF" : "1px solid #C1C1C1",
        borderRadius: "10px",
        width: "48%", // Make boxes take approximately half the width
        transition: "all 0.2s ease-in-out",
        '&:hover': {
          backgroundColor: "#E3DDFF",
          borderColor: "#7856FF",
          borderWidth: "1.5px",
          cursor: "pointer",
        },
      }}
      onClick={onClick}
    >
      <Stack
        gap={2}
      >
        <Stack direction="row" justifyContent="space-between" spacing={1} alignItems="flex-start">
          {icon}
          {isSelected && <CheckedIcon />}
        </Stack>
        <MLTypography
          fontSize="18px"
          variant="h2"
          fontWeight={600}
        >
          {title}
        </MLTypography>
      </Stack>
    </Stack>
  );

  // Desktop/Tablet version of Assessment Box
  const DesktopAssessmentBox: React.FC<DesktopAssessmentBoxProps> = ({ type, title, icon, isSelected, description, bulletPoints, onClick }) => (
    <Stack
      sx={{
        padding: "20px",
        backgroundColor: isSelected ? "#E3DDFF" : "",
        border: isSelected ? "1.5px solid #7856FF" : "1px solid #C1C1C1",
        borderRadius: "10px",
        gap: "20px",
        maxWidth: "610px",
        transition: "all 0.2s ease-in-out",
        '&:hover': {
          backgroundColor: "#E3DDFF",
          borderColor: "#7856FF",
          borderWidth: "1.5px",
          cursor: "pointer",
        },
      }}
      onClick={onClick}
    >
      <Stack direction="row" gap="16px"
        sx={{
          alignItems: "flex-start",
          justifyContent: "space-between",
        }}
      >
        <Stack direction="row" gap="16px"
          sx={{
            alignItems: "center",
          }}
        >
          {icon}
          <MLTypography
            fontSize="24px"
            // variant="h1"
            fontFamily="syne"
            fontWeight={700}
          >
            {title}
          </MLTypography>
        </Stack>
        <Stack>
          {isSelected ? <CheckedIcon /> : ""}
        </Stack>
      </Stack>

      <Stack gap="16px">
        <MLTypography
          fontSize="16px"
          variant="body1"
          fontWeight={600}
          sx={{ lineHeight: "130%" }}
        >
          {description}
        </MLTypography>

        <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
          {bulletPoints.map((point, index) => (
            <li key={index}>
              <MLTypography fontSize="16px" variant="body1" fontWeight={400}>{point}</MLTypography>
            </li>
          ))}
        </ul>
      </Stack>
    </Stack>
  );

  return (
    <>
      <MLBanner title={'Self Assessment'} subtitle='Evaluate your workspace ergonomics and identify areas for improvement' backgroundColor='#E3DDFF' />
      <Stack
        sx={{
          paddingBottom: "65px",
        }}
      >
        <MLContainer>
          <Stack
            direction="column"
            gap="30px"
            sx={{
              justifyContent: "center",
              alignItems: "center"
            }}
          >
            {isMobile ? (
              // Mobile layout
              <>
                <Stack
                  direction="row"
                  justifyContent="space-between"
                  sx={{ width: "100%" }}
                  gap="15px"
                >
                  <MobileAssessmentBox
                    type="ai"
                    title="AI based Assessment"
                    icon={<AIIcon />}
                    isSelected={selectedAssessment === "ai"}
                    onClick={() => setSelectedAssessment("ai")}
                  />
                  <MobileAssessmentBox
                    type="survey"
                    title="Survey based Assessment"
                    icon={<SurveyIcon />}
                    isSelected={selectedAssessment === "survey"}
                    onClick={() => setSelectedAssessment("survey")}
                  />
                </Stack>

                {selectedAssessment && (
                  <Stack gap="10px" sx={{ width: "100%" }}>
                    <MLTypography fontSize="24px" fontFamily="syne" fontWeight={700}>
                      {selectedAssessment === "ai" ? "AI based Assessment" : "Survey based Assessment"}
                    </MLTypography>

                    <Stack>
                      <MLTypography fontSize="16px" variant="body1" fontWeight={600} sx={{ lineHeight: "130%", mb: "16px" }}>
                        {selectedAssessment === "ai" ? aiDescription : surveyDescription}
                      </MLTypography>

                      <ul style={{ paddingLeft: "18px", marginBlock: 0, marginLeft: "8px" }}>
                        {(selectedAssessment === "ai" ? aiBulletPoints : surveyBulletPoints).map((point, index) => (
                          <li key={index}>
                            <MLTypography fontSize="16px" variant="body1" fontWeight={400}>
                              {point}
                            </MLTypography>
                          </li>
                        ))}
                      </ul>
                    </Stack>

                  </Stack>
                )}
              </>
            ) : (
              // Desktop/Tablet layout
              <Stack direction={{ sm: "column", md: "row" }} gap="50px"
                sx={{
                  justifyContent: "center",
                }}
              >
                <DesktopAssessmentBox
                  type="ai"
                  title="AI based assessment"
                  icon={<AIIcon />}
                  isSelected={selectedAssessment === "ai"}
                  description={aiDescription}
                  bulletPoints={aiBulletPoints}
                  onClick={() => setSelectedAssessment("ai")}
                />

                <DesktopAssessmentBox
                  type="survey"
                  title="Survey based assessment"
                  icon={<SurveyIcon />}
                  isSelected={selectedAssessment === "survey"}
                  description={surveyDescription}
                  bulletPoints={surveyBulletPoints}
                  onClick={() => setSelectedAssessment("survey")}
                />
              </Stack>
            )}

            <Stack
              sx={{
                alignItems: "center",
                marginTop: { xs: "10px", sm: "40px", md: "65px" },
                width: { xs: "100%", sm: "auto" }
              }}
            >
              <MLButton
                onClick={() => handleBeginAssessment()}
                variant="contained"
                color="secondary"
                endIcon={<ChevronRightIcon />}
                disabled={selectedAssessment === null}
                fullWidth={isMobile}
              >
                BEGIN ASSESSMENT
              </MLButton>
            </Stack>
          </Stack>
        </MLContainer>
      </Stack>
    </>
  );
};

export default GettingStarted;