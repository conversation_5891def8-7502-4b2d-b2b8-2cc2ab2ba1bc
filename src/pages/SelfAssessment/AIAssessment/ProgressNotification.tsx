import React from 'react';
import { CircularProgress, Stack } from "@mui/material";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";

interface ProgressNotificationProps {
    message: string;
}

/**
 * A simple progress notification component that shows a message with a circular progress indicator
 */
const ProgressNotification: React.FC<ProgressNotificationProps> = ({
    message = "Processing your request..."
}) => {
    return (
        <Stack
            direction="row"
            spacing={2}
            alignItems="center"
            sx={{
                py: 2,
                px: 3,
                backgroundColor: "#FFF8E1",
                borderRadius: 1,
                border: "1px solid #FFE082"
            }}
        >
            <CircularProgress size={20} thickness={5} />
            <MLTypography variant="body1" fontWeight={500}>
                {message}
            </MLTypography>
        </Stack>
    );
};

export default ProgressNotification;