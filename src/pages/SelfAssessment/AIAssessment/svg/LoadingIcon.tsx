const LoadingIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={25}
    fill="none"
    viewBox="0 0 20 25"
  >
    <g clipPath="url(#a)">
      <path
        stroke="#7856FF"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M3.377 5.726C4 9.06 6.166 11.343 10 12.501c3.834-1.158 6.011-3.441 6.623-6.775a1.468 1.468 0 0 0-.39-1.255 1.719 1.719 0 0 0-1.266-.541H5.033c-.49 0-.956.194-1.267.54-.322.347-.456.802-.389 1.256ZM.833.813h18.333"
      />
      <path
        stroke="#7856FF"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M5.033 21.071c-.49 0-.956-.194-1.267-.54a1.503 1.503 0 0 1-.389-1.256C4 15.942 6.166 13.658 10 12.5c3.834 1.158 6.011 3.442 6.623 6.775.077.454-.067.909-.39 1.255a1.719 1.719 0 0 1-1.266.541H5.033ZM.833 24.188h18.333"
      />
      <mask
        id="b"
        width={6}
        height={4}
        x={7}
        y={7}
        maskUnits="userSpaceOnUse"
        style={{
          maskType: "luminance",
        }}
      >
        <path
          fill="#fff"
          d="M10 10.22a3.348 3.348 0 0 0 2.846-1.787.41.41 0 0 0 0-.414.419.419 0 0 0-.357-.207H7.51a.419.419 0 0 0-.356.207.41.41 0 0 0 0 .414A3.331 3.331 0 0 0 10 10.22Z"
        />
      </mask>
      <g mask="url(#b)">
        <path fill="#7856FF" d="M17.053 3.672H2.947v10.687h14.106V3.672Z" />
      </g>
      <mask
        id="c"
        width={8}
        height={6}
        x={6}
        y={14}
        maskUnits="userSpaceOnUse"
        style={{
          maskType: "luminance",
        }}
      >
        <path
          fill="#fff"
          d="M10.622 15.148a.855.855 0 0 0-.622-.265c-.232 0-.456.1-.622.265L6.623 18.25a.828.828 0 0 0 .622 1.373h5.526a.828.828 0 0 0 .623-1.373l-2.763-3.102h-.009Z"
        />
      </mask>
      <g mask="url(#c)">
        <path fill="#7856FF" d="M17.733 10.75H2.26v13.012h15.474V10.75Z" />
      </g>
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h20v25H0z" />
      </clipPath>
    </defs>
  </svg>
)
export default LoadingIcon;
