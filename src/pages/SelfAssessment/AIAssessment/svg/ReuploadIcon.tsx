import * as React from "react"
const ReuploadIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={23}
    height={22}
    fill="none"
    viewBox="0 0 23 22"
  >
    <g
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      clipPath="url(#a)"
    >
      <path
        stroke="#333"
        d="M16.203 21.313c3.034 0 5.5-2.466 5.5-5.5 0-3.035-2.466-5.5-5.5-5.5a5.504 5.504 0 0 0-5.5 5.5c0 3.034 2.466 5.5 5.5 5.5Z"
      />
      <path
        stroke="#FF6E6E"
        d="M16.203 18.563v-5.5M16.203 13.063l-2.062 2.062M16.203 13.063l2.063 2.062"
      />
      <path
        stroke="#333"
        d="M7.953 18.563h-5.5c-.76 0-1.375-.615-1.375-1.375V2.063c0-.761.614-1.376 1.375-1.376h9.744c.367 0 .715.147.972.404l2.63 2.63c.258.257.404.606.404.972v2.87"
      />
      <path
        fill="#D9D9D9"
        stroke="#333"
        d="M5.204 5.148a.348.348 0 1 1 0 .697.348.348 0 0 1 0-.697Z"
      />
      <path
        stroke="#FF6E6E"
        d="m11.464 8.789-.76-1.229a.691.691 0 0 0-1.137-.01l-1.834 2.614-1.127-.908a.7.7 0 0 0-.532-.147.695.695 0 0 0-.468.303L3.828 13.06h4.125"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M.39 0h22v22h-22z" />
      </clipPath>
    </defs>
  </svg>
)
export default ReuploadIcon;
