import { useContext, useEffect, useRef, useState } from "react";
import { Box, CircularProgress, Dialog, Divider, IconButton, Menu, MenuItem, Modal, Stack } from "@mui/material";
import MLBanner from "../../../components/ui/MLBanner/MLBanner";
import { desktop, tablet } from "../../../responsiveStyles";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import MLCheckbox from "../../../components/ui/MLCheckbox/MLCheckbox";
import MLButton from "../../../components/ui/MLButton/MLButton";
import { ChevronLeft, ChevronRight, Warning } from "@mui/icons-material";
import Markdown from 'react-markdown'
import { useBack, useCreate, useCustomMutation, useList, useOne, useUpdate } from "@refinedev/core";
import MLContainer from "../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";
import { AI_API_BASE_URL, API_URL, ANALYSIS_STATUS_ENDPOINT, TOKEN_KEY } from "../../../constants";
import axios, { AxiosInstance } from 'axios';
import { RequestType } from "../../../models/Case";
import { generateReportData } from "../Report/reportUtils";
import MLToggleButtonGroup from "../../../components/ui/MLToggleButtonGroup/MLToggleButtonGroup";
import MLToggleButton from "../../../components/ui/MLToggleButton/MLToggleButton";
import UploadPhoto from "./components/UploadPhoto";
import CheckedIcon from "../Work Setup/SvgIcon/CheckedIcon";
import LoadingIcon from "./svg/LoadingIcon";
import { useLocation, useNavigate } from "react-router-dom";
import QRCodeSection, { STORAGE_KEY, StoredQRInfo } from "./GenerateQrCode";
import { AuthContext } from "../../../contexts/authContext/AuthContext";
import { ViewModeContext } from "../../../contexts/ViewModeContext/ViewModeContext";
import AssessmentForWhomBanner from "../../cases/create/components/AssessmentForWhomBanner";
import CameraAltIcon from '@mui/icons-material/CameraAlt';
import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary';
import { ErChevronleft } from "@mindlens/ergo-icons";
import ProgressNotification from "./ProgressNotification";
import { PhotoSection } from "./PhotoSection";
import Editpeference from "../../../assets/icons/Editpeference";
import DiscomfortSelector from "./DiscomfortDialog";

export type ImgType = "chair_only" | "desk_chair" | "standing_desk"
interface BodyPartConfig {
  id: number;
  bodyPart: string;
  bodyPartText: string;
  isFront: boolean;
}

interface OptionType {
  label: string;
  value: number;
}

interface IDiscomfort {
  isDiscomfortChecked?: boolean;
  specialConsiderations?: Record<string, boolean>;
  selectedCondition?: Record<string, {
    isSelected: boolean;
    affectsBodyPart: string;
    affectsBodyPartText: string;
  }>;
  reportGenerated?: Record<string, string>;
  [key: string]: any;
}

export enum ProgressStage {
  PRECHECKING = "Conducting prechecks",
  EXAMINE_WORKSETUP = "Examining work setup",
  ASSESSING_POSTURE = "Assessing posture",
  IDENTIFY_ERGORISK = "Identifying ergo risks",
  GENERATING_REPORT = "Creating recommendations",
}
export type ProgressStageType = ProgressStage.PRECHECKING |
  ProgressStage.EXAMINE_WORKSETUP |
  ProgressStage.ASSESSING_POSTURE |
  ProgressStage.IDENTIFY_ERGORISK |
  ProgressStage.GENERATING_REPORT;

export type ProgressStatusType = "not_started" | "ongoing" | "done" | "error";

// Camera/Gallery Menu component for mobile interface
interface CameraGalleryMenuProps {
  menuAnchor: null | HTMLElement;
  open: boolean;
  onClose: () => void;
  onCameraClick: () => void;
  onGalleryClick: () => void;
  isDisabled: boolean;
}

// Define section key type for type safety
type SectionKey = 'sitting' | 'working' | 'standing';

const CameraGalleryMenu: React.FC<CameraGalleryMenuProps> = ({
  menuAnchor,
  open,
  onClose,
  onCameraClick,
  onGalleryClick,
  isDisabled
}) => {
  return (
    <Menu
      anchorEl={menuAnchor}
      open={open}
      onClose={onClose}
    >
      <MenuItem onClick={onCameraClick} disabled={isDisabled}>
        <CameraAltIcon sx={{ mr: 1 }} /> Take Photo
      </MenuItem>
      <MenuItem onClick={onGalleryClick} disabled={isDisabled}>
        <PhotoLibraryIcon sx={{ mr: 1 }} /> Choose from Gallery
      </MenuItem>
    </Menu>
  );
};

export const loadQRInfo = (): StoredQRInfo | null => {
  try {
    const storedInfo = localStorage.getItem(STORAGE_KEY);
    if (!storedInfo) return null;

    const parsedInfo = JSON.parse(storedInfo) as StoredQRInfo;
    return parsedInfo;
  } catch (err) {
    console.error("Error loading token from localStorage:", err);
    return null;
  }
};

const AIAssessment = () => {
  const token = localStorage.getItem('strapi-jwt-token');
  const CHAIR_ANALYSIS_ENDPOINT = '/api/v1/ergonomic/analyze-chair/async';
  const DESK_ANALYSIS_ENDPOINT = '/api/v1/ergonomic/analyze-desk/async';
  const STANDING_ANALYSIS_ENDPOINT = '/api/v1/ergonomic/analyze-standing/async';
  const { userDetails: loggedUserDetails } = useContext(AuthContext);
  const { isEmployeeView } = useContext(ViewModeContext);

  const location = useLocation();
  const userDetails = location.state?.fetchedUser; // its coming from state from useNavigation()
  const loggedUserRoleIsEmployee = loggedUserDetails?.role?.name.toLowerCase() === "employee" || loggedUserDetails?.employeeView || isEmployeeView;

  const navigate = useNavigate();
  const { mutate: mutateUpdateAICase } = useUpdate();
  const { mutate: mutateCreateSelfAssessmentCase } = useCreate();
  const { mutate: mutateUpdateSelfAssessmentCase } = useUpdate();
  const { mutate: mutateCreateSelfAssessmentReport } = useCreate();
  const { mutate: mutateCustomUploadAICaseImage } = useCustomMutation(); // Move hook call to component level

  const [loadProgress, setLoadProgress] = useState<{
    [key in ProgressStageType]: ProgressStatusType
  }>({
    [ProgressStage.PRECHECKING]: "not_started",
    [ProgressStage.EXAMINE_WORKSETUP]: "not_started",
    [ProgressStage.ASSESSING_POSTURE]: "not_started",
    [ProgressStage.IDENTIFY_ERGORISK]: "not_started",
    [ProgressStage.GENERATING_REPORT]: "not_started",
  });

  const [isTermsAccepted, setIsTermsAccepted] = useState<boolean>(false);
  const [isTOCModalOpened, setIsTOCModalOpened] = useState<boolean>(false);

  const [isAIOverLoaded, setIsAIOverloaded] = useState<boolean>(false);
  const [isAiErrorModalOpened, setIsAiErrorModalOpened] = useState<boolean>(false);
  const [aiErrorMsg, setAiErrorMsg] = useState<string>("");

  const [deskChairImage, setDeskChairImage] = useState<File>();
  const [chairOnlyImage, setChairOnlyImage] = useState<File>();
  const [standingDeskImage, setStandingDeskImage] = useState<File>();
  const [deskChairPreview, setDeskChairPreview] = useState<string | null>(null);
  const [chairOnlyPreview, setChairOnlyPreview] = useState<string | null>(null);
  const [standingDeskPreview, setStandingDeskPreview] = useState<string | null>(null);

  const [isStandingDeskUploading, setIsStandingDeskUploading] = useState<boolean | null>(null); // special case to check if optional standing image is uploading, we prevent the analysis from starting

  const [isChairUploadSuccess, setIsChairUploadSuccess] = useState<boolean | null>(null);
  const [isDeskUploadSuccess, setIsDeskUploadSuccess] = useState<boolean | null>(null);
  const [isStandingUploadSuccess, setIsStandingUploadSuccess] = useState<boolean | null>(null);

  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [isProgressModalOpen, setIsProgressModalOpen] = useState<boolean>(false);

  const [showMobileUploadNotification, setShowMobileUploadNotification] = useState<boolean>(false);
  const [mobileUploadStatus, setMobileUploadStatus] = useState<string>("waiting"); // "waiting", "receiving", "processing"
  const [showQrCode, setShowQrCode] = useState<boolean>(false);

  const [workSetup, setWorkSetup] = useState({
    "chair": {
      "armRest": false,
      "seatDepth": false,
      "adjustableHeight": loggedUserRoleIsEmployee ? undefined : true
    },
    "sitStandDesk": loggedUserRoleIsEmployee ? undefined : true
  });

  const [deskChairImageErrMsg, setDeskChairImageErrMsg] = useState<string>("");
  const [chairOnlyImageErrMsg, setChairOnlyImageErrMsg] = useState<string>("");
  const [standingDeskImageErrMsg, setStandingDeskImageErrMsg] = useState<string>("");

  const [sortedImages, setSortedImages] = useState<{
    url: string | null;
    alt: string;
    aspectRatio?: number
  }[]>([]);

  const [isReportCreated, setIsReportCreated] = useState<boolean>(false);
  const [aiImageAnalysisResponse, setAiImageAnalysisResponse] = useState<any>();

  // File states
  const [photoFiles, setPhotoFiles] = useState<Record<SectionKey, File | undefined>>({
    sitting: undefined,
    working: undefined,
    standing: undefined
  });

  // State for managing expanded sections
  const [expandedSections, setExpandedSections] = useState<Record<SectionKey, boolean>>({
    sitting: false,
    working: false,
    standing: false
  });

  // Menu states for camera/gallery selection
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  const [discomfort, setDiscomfort] = useState<IDiscomfort>({
    isDiscomfortChecked: false,
    specialConsiderations: {},
    selectedCondition: {},
    reportGenerated: {}
  });
  const [hasDiscomfort, setHasDiscomfort] = useState<boolean | null>(null);
  const [isDiscomfortDialogOpen, setIsDiscomfortDialogOpen] = useState<boolean>(false);
  const [isMobileSubmissionComplete, setIsMobileSubmissionComplete] = useState<boolean>(false);
  // State for body parts
  const [bodyParts, setBodyParts] = useState<OptionType[]>([]);
  const [selectedBodyParts, setSelectedBodyParts] = useState<OptionType[]>([]);

  const deskChairInputRef = useRef<HTMLInputElement>(null);
  const chairOnlyInputRef = useRef<HTMLInputElement>(null);
  const standingDeskInputRef = useRef<HTMLInputElement>(null);

  const [hasRunGenerateReport, setHasRunGenerateReport] = useState<boolean>(false);
  // Initialize a reference to hold our axios instance
  const axiosRef = useRef<AxiosInstance | null>(null);
  const back = useBack();

  const { data: employeeData, isLoading: isEmployeeDataLoading } = useOne({
    resource: "users",
    id: loggedUserDetails?.id,
    meta: {
      populate: {
        employee: {
          fields: ["id", "name", "countryCode", "contactNo", "email"]
        }
      }
    },
  });

  const { data: bodyPartConfigsData, isLoading: isBodyPartsLoading, error: bodyPartsError } = useList<BodyPartConfig>({
    resource: "self-assessment-body-part-configs",
    pagination: { pageSize: 50 },
  });

  const { data: aiTermsAndConditionsData } = useList<{ text: string; textContentName: string }>({
    resource: "common-text-contents",
    filters: [
      {
        field: "textContentName",
        operator: "eq",
        value: "aiAssessment-termsAndConditions",
      }
    ]
  });
  const aiTermsAndConditions = aiTermsAndConditionsData ? aiTermsAndConditionsData.data[0] ? aiTermsAndConditionsData.data[0].text : "" : "";

  const { data: ergonomicScoreTextData } = useList<{ text: string; textContentName: string }>({
    resource: "common-text-contents",
    filters: [
      {
        field: "textContentName",
        operator: "contains",
        value: `ergonomicScore-`,
      }
    ],
    pagination: {
      pageSize: 3,
    }
  });
  const ergonomicScoreText = ergonomicScoreTextData ? ergonomicScoreTextData.data : [];

  // Add these lines to get existing cases data
  const { data: existingCasesData, isLoading: isLoadingCases } = useList({
    resource: "ai-cases",
    filters: [
      {
        field: "employee",
        operator: "eq",
        value: userDetails?.employee?.id,
      }
    ],
    queryOptions: {
      enabled: !!userDetails?.employee?.id, // Only run query if employeeId exists
    },
  });

  // Check if employee already has a case
  const existingCases = existingCasesData?.data || [];
  const existingCase = existingCases.length > 0 ? existingCases[0] : null;

  // runs ai service health check on component mount and every 1 min after that
  useEffect(() => {
    const runAIHealthCheck = async () => {
      console.log("running health check...");
      try {
        const aiBackendHealthStatus = await axios.get(`${AI_API_BASE_URL}/health`, { timeout: 30000 });
        if (aiBackendHealthStatus.status !== 200) {
          throw new Error("AI service not responding.");
        }
      } catch (e) {
        console.log("ai health check error = ", e);
        setIsAIOverloaded(true);
        setAiErrorMsg("Our AI services are currently overloaded. Please try again");
        setIsAiErrorModalOpened(true);
      }
    };

    runAIHealthCheck();

    const interval = setInterval(runAIHealthCheck, 60000);

    return () => clearInterval(interval);
  }, []);

  // Effect for polling AI case status with proper mobile/desktop detection
  useEffect(() => {    
    if (!token) return;

    const interval = setInterval(async () => {
      const aiCaseId = loadQRInfo()?.caseId;

      console.log("polling aiCaseId: ", aiCaseId);

      // Stop polling if mobile submission is complete and analysis is done
      if (isMobileSubmissionComplete && !isAnalyzing) {
        return;
      }

      // Check for upload failures first
      if (isChairUploadSuccess === false || isDeskUploadSuccess === false || isStandingUploadSuccess === false) {
        setAiErrorMsg("Image upload failed. Please try again");
        setIsAiErrorModalOpened(true);
        return;
      }

      try {
        // Fetch AI case data
        const aiCaseRes = await axios.get(`${API_URL}/api/ai-cases/${aiCaseId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          params: {
            fields: [
              "chairDeskImageProgress",
              "chairImageProgress",
              "standingImageProgress",
              "triggerAiAssessment",
              "chairImageAnalysisId",
              "chairDeskImageAnalysisId",
              "standingImageAnalysisId",
              "chairImageRawResponse",
              "chairDeskImageRawResponse",
              "standingImageRawResponse",
              "mobileWorkSetup",
              "discomfort",
              "qrValid"
            ],
            populate: {
              chairImage: {
                fields: ["url"]
              },
              chairDeskImage: {
                fields: ["url"]
              },
              standingImage: {
                fields: ["url"]
              }
            }
          }
        });

        const aiCase = aiCaseRes.data.data.attributes;

        // Handle mobile photo detection - only show notification if:
        // 1. Images exist AND
        // 2. We don't have them locally (meaning they came from mobile) AND  
        // 3. We haven't already loaded them
        const hasChairImage = aiCase.chairImage?.data;
        const hasChairDeskImage = aiCase.chairDeskImage?.data;

        // Show "receiving from mobile" only if images exist but we don't have them locally
        if ((hasChairImage && !chairOnlyImage) || (hasChairDeskImage && !deskChairImage)) {
          if (mobileUploadStatus === "waiting" && !aiCase.triggerAiAssessment) {
            console.log("Detected mobile image upload starting");
            setMobileUploadStatus("receiving");
            setShowMobileUploadNotification(true);
          }
        }

        // Handle mobile trigger for analysis
        if (aiCase.triggerAiAssessment && mobileUploadStatus !== "processing") {
          console.log("Mobile triggered AI assessment");
          setMobileUploadStatus("processing");
          setShowMobileUploadNotification(true);
          setIsMobileSubmissionComplete(true);

          // Start analysis after a short delay
          setTimeout(() => {
            setShowMobileUploadNotification(false);
            setIsProgressModalOpen(true);
            setIsAnalyzing(true);
          }, 3000);
        }

        // Fetch images from mobile if they exist and we don't have them locally
        if (!deskChairImage && hasChairDeskImage) {
          try {
            const response = await fetch(aiCase.chairDeskImage.data.attributes.url);
            const blob = await response.blob();
            const file = new File([blob], "deskChairImage", { type: "image/jpeg" });
            setDeskChairImage(file);
            console.log("Loaded desk chair image from mobile");
          } catch (error) {
            console.error("Error fetching desk chair image:", error);
          }
        }

        if (!chairOnlyImage && hasChairImage) {
          try {
            const response = await fetch(aiCase.chairImage.data.attributes.url);
            const blob = await response.blob();
            const file = new File([blob], "chairOnlyImage", { type: "image/jpeg" });
            setChairOnlyImage(file);
            console.log("Loaded chair only image from mobile");
          } catch (error) {
            console.error("Error fetching chair only image:", error);
          }
        }

        // Handle standing image if available
        if (!standingDeskImage && aiCase.standingImage?.data) {
          try {
            const response = await fetch(aiCase.standingImage.data.attributes.url);
            const blob = await response.blob();
            const file = new File([blob], "standingImage", { type: "image/jpeg" });
            setStandingDeskImage(file);
            console.log("Loaded standing image from mobile");
          } catch (error) {
            console.error("Error fetching standing image:", error);
          }
        }

        // Update work setup from mobile if available and not already set
        if (aiCase.mobileWorkSetup &&
          aiCase.mobileWorkSetup.chair &&
          aiCase.mobileWorkSetup.chair.adjustableHeight !== undefined &&
          aiCase.mobileWorkSetup.sitStandDesk !== undefined) {

          // Only update if current workSetup has undefined values (desktop hasn't been filled)
          if (workSetup.chair.adjustableHeight === undefined || workSetup.sitStandDesk === undefined) {
            console.log("Updating work setup from mobile", aiCase.mobileWorkSetup);
            setWorkSetup(aiCase.mobileWorkSetup);
          }
        }

        // Update discomfort from mobile if available
        if (aiCase.discomfort && hasDiscomfort === null) {
          console.log("Updating discomfort from mobile", aiCase.discomfort);
          setDiscomfort(aiCase.discomfort);
          setHasDiscomfort(!aiCase.discomfort.isDiscomfortChecked);
        }

        // Check analysis progress for chair image
        if (!hasRunGenerateReport && aiCase.chairImageProgress && aiCase.chairImageAnalysisId) {
          try {
            const chairStatusRes = await axios.get(`${AI_API_BASE_URL}${ANALYSIS_STATUS_ENDPOINT}${aiCase.chairImageAnalysisId}`);

            if (chairStatusRes.data.results && chairStatusRes.data.results.status === "error") {
              setAiErrorMsg(chairStatusRes.data.results.message);
              setIsAiErrorModalOpened(true);
              return;
            }

            if (aiCase.chairImageProgress === "FailedProcessing") {
              setAiErrorMsg("Sorry, AI server too busy. Please re-upload images");
              setIsAiErrorModalOpened(true);
              return;
            }
          } catch (err) {
            console.error("[ERROR] fetching chairStatusRes:", err);
          }
        }

        // Check analysis progress for desk image
        if (!hasRunGenerateReport && aiCase.chairDeskImageProgress && aiCase.chairDeskImageAnalysisId) {
          try {
            const deskStatusRes = await axios.get(`${AI_API_BASE_URL}${ANALYSIS_STATUS_ENDPOINT}${aiCase.chairDeskImageAnalysisId}`);

            if (deskStatusRes.data.results && deskStatusRes.data.results.status === "error") {
              setAiErrorMsg(deskStatusRes.data.results.message);
              setIsAiErrorModalOpened(true);
              return;
            }

            if (aiCase.chairDeskImageProgress === "FailedProcessing") {
              setAiErrorMsg("Sorry, AI server too busy. Please re-upload images");
              setIsAiErrorModalOpened(true);
              return;
            }

            // Update progress based on analysis status
            if (deskStatusRes.data.status === "completed") {
              setLoadProgress(prev => ({
                ...prev,
                [ProgressStage.PRECHECKING]: "done",
                [ProgressStage.EXAMINE_WORKSETUP]: "done",
                [ProgressStage.ASSESSING_POSTURE]: "done",
                [ProgressStage.IDENTIFY_ERGORISK]: "done",
              }));
            } else {
              const progress = deskStatusRes.data.progress || 0;
              setLoadProgress(prev => ({
                ...prev,
                [ProgressStage.PRECHECKING]: progress >= 0 ? (progress < 25 ? "ongoing" : "done") : "not_started",
                [ProgressStage.EXAMINE_WORKSETUP]: progress >= 25 ? (progress < 50 ? "ongoing" : "done") : "not_started",
                [ProgressStage.ASSESSING_POSTURE]: progress >= 50 ? (progress < 75 ? "ongoing" : "done") : "not_started",
                [ProgressStage.IDENTIFY_ERGORISK]: progress >= 75 ? (progress < 100 ? "ongoing" : "done") : "not_started",
              }));
            }
            if (deskStatusRes.data.progress >= 75 && deskStatusRes.data.progress < 100) {
              setLoadProgress({
                ...loadProgress,
                [ProgressStage.PRECHECKING]: "done",
                [ProgressStage.EXAMINE_WORKSETUP]: "done",
                [ProgressStage.ASSESSING_POSTURE]: "done",
                [ProgressStage.IDENTIFY_ERGORISK]: "ongoing",
              });
            }
          } catch (err) {
            console.error("[ERROR] fetching deskStatusRes:", err);
          }
        }

        if (aiCase.chairDeskImageProgress === "FailedProcessing") {
          // Fixed axios.put structure
          await axios.put(`${API_URL}/api/ai-cases/${aiCaseId}`, {
            chairDeskImageProgress: null,
            chairDeskImage: null
          }, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          setIsAiErrorModalOpened(true);
          return;
        }


        // Get standing analysis status
        if (!hasRunGenerateReport && aiCase.standingImageProgress != null && aiCase.standingImageAnalysisId != null && standingDeskImage) {
          try {
            const standingStatusRes = await axios.get(`${AI_API_BASE_URL}${ANALYSIS_STATUS_ENDPOINT}${aiCase.standingImageAnalysisId}`);
            console.log("standingStatusRes = ", standingStatusRes);

            if (standingStatusRes.data.results && standingStatusRes.data.results.status === "error") {
              setAiErrorMsg(standingStatusRes.data.results.message);
              setIsAiErrorModalOpened(true);
              return;
            }
            if (aiCase.standingImageProgress === "FailedProcessing") {
              setAiErrorMsg("Sorry, AI server too busy. Please re-upload images");
              setIsAiErrorModalOpened(true);
              return;
            }
          } catch (err) {
            console.error("[ERROR] fetching standingStatusRes:", err);
          }
        }

        // Generate report when both analyses are complete
        if (aiCase.chairDeskImageProgress === "Completed" &&
          aiCase.chairImageProgress === "Completed" &&
          aiCase.chairImageAnalysisId &&
          aiCase.chairDeskImageAnalysisId &&
          !hasRunGenerateReport) {

          console.log("Starting report generation...");
          setHasRunGenerateReport(true);

          // Use mobile work setup and discomfort if available, otherwise use desktop values
          const finalWorkSetup = aiCase.mobileWorkSetup &&
            aiCase.mobileWorkSetup.chair &&
            aiCase.mobileWorkSetup.chair.adjustableHeight !== undefined &&
            aiCase.mobileWorkSetup.sitStandDesk !== undefined
            ? aiCase.mobileWorkSetup
            : workSetup;

          const finalDiscomfort = aiCase.discomfort;

          await generateReport(aiCase, finalWorkSetup, finalDiscomfort);
        }

      } catch (err) {
        console.error("Error in AI Case polling:", err);
      }
    }, 7000);

    return () => clearInterval(interval);
  }, [
    token, mobileUploadStatus, chairOnlyImage, deskChairImage, standingDeskImage,
    hasRunGenerateReport, isChairUploadSuccess, isDeskUploadSuccess, isStandingUploadSuccess,
    workSetup, discomfort, hasDiscomfort, showMobileUploadNotification, isProgressModalOpen,
    isAnalyzing, isMobileSubmissionComplete
  ]);

  const generateReport = async (aiCase: any, finalWorkSetup: any, finalDiscomfort: any) => {
    try {
      const aiCaseId = loadQRInfo()?.caseId;

      const authToken = localStorage.getItem(TOKEN_KEY);
      const headers = { Authorization: `Bearer ${authToken || token}` };

      const uploadedImages = [];

      if (aiCase.chairImage && aiCase.chairImage.data !== null) {
        uploadedImages.push(aiCase.chairImage.data.attributes.url);
      }
      if (aiCase.chairDeskImage && aiCase.chairDeskImage.data !== null) {
        uploadedImages.push(aiCase.chairDeskImage.data.attributes.url);
      }
      if (aiCase.standingImage && aiCase.standingImage.data !== null) {
        uploadedImages.push(aiCase.standingImage.data.attributes.url);
      }
      // Extract AI analysis results
      const chairResultMappedAiResponse = aiCase.chairImageRawResponse?.mappedAiResponse || {};
      const deskResultMappedAiResponse = aiCase.chairDeskImageRawResponse?.mappedAiResponse || {};
      const standingDeskResultMappedAiResponse = aiCase.standingImageRawResponse ? aiCase.standingImageRawResponse.mappedAiResponse : {};

      const workHabit = {
        ...chairResultMappedAiResponse,
        ...deskResultMappedAiResponse,
        ...standingDeskResultMappedAiResponse,
      };

      // Prepare screens and laptops data
      let screens = {};
      const monitorCount = aiCase.chairDeskImageRawResponse?.detailed_results?.workstation_details?.monitor_count || 0;
      for (let i = 0; i < monitorCount; i++) {
        screens = { ...screens, [i + 1]: 0 };
      }

      let laptops = {};
      const laptopCount = aiCase.chairDeskImageRawResponse?.detailed_results?.workstation_details?.laptop_count || 0;
      for (let i = 0; i < laptopCount; i++) {
        laptops = { ...laptops, [i + 1]: 0 };
      }

      // Prepare final work setup
      const updatedWorkSetup = {
        ...finalWorkSetup,
        "mouse": aiCase.chairDeskImageRawResponse?.detailed_results?.workstation_details?.has_mouse || false,
        "keyboard": aiCase.chairDeskImageRawResponse?.detailed_results?.workstation_details?.has_keyboard || false,
        "laptops": laptops,
        "screens": screens,
      };

      setLoadProgress(prev => ({
        ...prev,
        [ProgressStage.GENERATING_REPORT]: "ongoing"
      }));

      // Create self assessment case
      let createSelfAssessmentCase: any;
      try {
        createSelfAssessmentCase = await axios.post(`${API_URL}/api/self-assessment-cases`, {
          data: {
            fullName: userDetails ? userDetails?.employee.name : employeeData?.data.employee.name,
            countryCode: userDetails ? userDetails?.employee.countryCode as number : employeeData?.data.employee.countryCode,
            contactNo: userDetails ? userDetails?.employee.contactNo as number : employeeData?.data.employee.contactNo,
            email: userDetails ? userDetails?.employee.email : employeeData?.data.employee.email,
            isCompleted: true,
            assessmentType: "office",
            currentPage: "Work habit",
            assessmentProgress: 100,
            discomfort: finalDiscomfort,
            workSetup: updatedWorkSetup,
            workHabit: workHabit,
            employee: userDetails ? userDetails?.employee.id : employeeData?.data.employee.id,
            isAiAssessment: true,
          }
        }, { headers });
      } catch (e) {
        console.error("createSelfAssessmentCase error:", e);
        setAiErrorMsg("An error occurred when creating self assessment case. Please try again");
        setIsAiErrorModalOpened(true);
        return;
      }

      const selfAssessmentCaseId = createSelfAssessmentCase.data.data.id;

      try {
        const updateAICase = await axios.put(`${API_URL}/api/ai-cases/${aiCaseId}`, {
          data: {
            selfAssessmentCase: selfAssessmentCaseId
          }
        }, {
          headers
        });
      } catch (e) {
        console.error("updateAICase error:", e);
        setAiErrorMsg("An error occurred when updating ai case. Please try again");
        setIsAiErrorModalOpened(true);
        return;
      }

      // Generate report data
      const reportData = await generateReportData(
        selfAssessmentCaseId,
        userDetails ? userDetails?.employee.name : employeeData?.data.employee.name,
        ergonomicScoreText,
        {}
      );

      // Update self assessment case with report data
      try {
        await axios.put(`${API_URL}/api/self-assessment-cases/${selfAssessmentCaseId}`, {
          data: { discomfort: reportData.newDiscomfort }
        }, { headers });
      } catch (e) {
        console.error("updateSelfAssessmentCase error:", e);
        setAiErrorMsg("An error occurred when updating self assessment case. Please try again");
        setIsAiErrorModalOpened(true);
        return;
      }

      // Create report if user is employee
      if (loggedUserRoleIsEmployee) {
        try {
          await axios.post(`${API_URL}/api/self-assessment-reports`, {
            data: {
              selfAssessmentCase: selfAssessmentCaseId,
              generatedReport: {
                ...reportData.newReportData,
                uploadedImages: uploadedImages,
              },
              assessmentType: RequestType.GENERAL_ASSESSMENT,
            }
          }, { headers });
        } catch (e) {
          console.error("createSelfAssessmentReport error:", e);
          setAiErrorMsg("An error occurred when creating new report. Please try again");
          setIsAiErrorModalOpened(true);
          return;
        }
      }

      // Update final progress
      setIsAnalyzing(false);
      setLoadProgress(prev => ({
        ...prev,
        [ProgressStage.GENERATING_REPORT]: "done",
      }));
      setIsReportCreated(true);

      console.log("Report generation completed successfully");

    } catch (error) {
      console.error("Error generating report:", error);
      setAiErrorMsg("We've received your case analysis but couldn't generate recommendations at this time. We'll follow up soon with your report results. Apologies for the inconvenience.");
      setIsAiErrorModalOpened(true);
      setIsAnalyzing(false);
    }
  };

  const getAspectRatio = (imageUrl: string | null) => {
    return new Promise<number>((resolve) => {
      if (!imageUrl) {
        resolve(0);
        return;
      }

      const img = new Image();
      img.onload = () => {
        const ratio = img.width / img.height;
        resolve(ratio);
      };
      img.src = imageUrl;
    });
  };

  // Process body part data when it loads - handle direct properties
  useEffect(() => {
    if (bodyPartConfigsData?.data) {
      try {
        // Map directly from the properties (not from attributes)
        const sortedOptions = bodyPartConfigsData.data
          .map((config) => ({
            label: config.bodyPartText,
            value: config.id,
          }))
          .sort((a, b) => a.label.toLowerCase().localeCompare(b.label.toLowerCase()));

        setBodyParts(sortedOptions);
      } catch (error) {
        console.error("Error processing body part data:", error);
        setBodyParts([]);
      }
    }
  }, [bodyPartConfigsData]);

  // Initialize axios instance
  useEffect(() => {
    axiosRef.current = axios.create({
      baseURL: API_URL,
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    return () => {
      axiosRef.current = null;
    };
  }, [token]);

  useEffect(() => {
    if (deskChairImage) {
      const objectUrl = URL.createObjectURL(deskChairImage);
      setDeskChairPreview(objectUrl);

      return () => URL.revokeObjectURL(objectUrl);
    }
  }, [deskChairImage]);

  useEffect(() => {
    if (chairOnlyImage) {
      const objectUrl = URL.createObjectURL(chairOnlyImage);
      setChairOnlyPreview(objectUrl);

      return () => URL.revokeObjectURL(objectUrl);
    }
  }, [chairOnlyImage]);

  useEffect(() => {
    if (standingDeskImage) {
      const objectUrl = URL.createObjectURL(standingDeskImage);
      setStandingDeskPreview(objectUrl);

      return () => URL.revokeObjectURL(objectUrl);
    }
  }, [standingDeskImage]);

  // sorting display order for scanning modal
  useEffect(() => {
    const sortImages = async () => {
      // Create array with image objects and their aspect ratios
      const imageData = [
        { url: chairOnlyPreview, alt: "Chair only photo" },
        { url: deskChairPreview, alt: "Desk and chair photo" },
      ];
      if (standingDeskPreview !== null) imageData.push({ url: standingDeskPreview, alt: "Standing desk photo" });

      // Calculate aspect ratios for all images
      const withRatios = await Promise.all(
        imageData.map(async (item) => {
          const ratio = await getAspectRatio(item.url);
          return { ...item, aspectRatio: ratio };
        })
      );

      // Sort images - those with ratio > 1 come first
      const sortedImages = withRatios.sort((a, b) => {
        // If both are > 1 or both are <= 1, keep original order
        if ((a.aspectRatio > 1 && b.aspectRatio > 1) ||
          (a.aspectRatio <= 1 && b.aspectRatio <= 1)) {
          return 0;
        }
        // If a > 1 and b <= 1, a comes first
        if (a.aspectRatio > 1 && b.aspectRatio <= 1) {
          return -1;
        }
        // Otherwise, b comes first
        return 1;
      });

      setSortedImages(sortedImages);
    };

    if (chairOnlyPreview && deskChairPreview) {
      sortImages();
    }
  }, [chairOnlyPreview, deskChairPreview, standingDeskPreview]);

  // This function connects the mobile photo states to the desktop states
  useEffect(() => {
    // Sync mobile photoFiles with desktop state variables when they change
    if (chairOnlyImage && !photoFiles.sitting) {
      setPhotoFiles(prev => ({
        ...prev,
        sitting: chairOnlyImage
      }));
    }

    if (deskChairImage && !photoFiles.working) {
      setPhotoFiles(prev => ({
        ...prev,
        working: deskChairImage
      }));
    }

    if (standingDeskImage && !photoFiles.standing) {
      setPhotoFiles(prev => ({
        ...prev,
        standing: standingDeskImage
      }));
    }
  }, [chairOnlyImage, deskChairImage, standingDeskImage, photoFiles]);

  const handleViewPersonalizedReport = () => {
    const aiCaseId = loadQRInfo()?.caseId;

    const path = !loggedUserRoleIsEmployee
      ? "/ai-assessment/prechecks"
      : "/self-assessment";

    if (path === `/ai-assessment/prechecks`) {
      navigate(`${path}/${aiCaseId}`, { state: { aiImageAnalysisResponse, userDetails } });
    } else {
      navigate(path);
    }
  };

  const photoSections: Array<{ id: SectionKey; title: string; subtitle: string }> = [
    {
      id: 'sitting',
      title: '1. Sitting in chair',
      subtitle: ''
    },
    {
      id: 'working',
      title: '2. Working on computer',
      subtitle: ''
    },
    {
      id: 'standing',
      title: '3. Standing',
      subtitle: '(Optional)'
    }
  ];

  // Instructions for each section
  const photoInstructions = {
    sitting: [
      "Full Side profile (Head to feet)",
      "Hands on thighs. Looking in front."
    ],
    working: [
      "Full Side profile (Head to feet)",
      "Hands on keyboard. Looking at screen"
    ],
    standing: [
      "Full Side profile (Head to feet)",
      "Standing. Hands on keyboard. Looking at screen"
    ]
  };

  // Sample photo URLs
  const samplePhotoUrls = {
    sitting: "https://app.synergopro.com/strapi/sitting_square_a136e1202a.png",
    working: "https://app.synergopro.com/strapi/working_square_797712d98b.png",
    standing: "https://app.synergopro.com/strapi/standing_square_101357bb4f.png"
  };

  // Get image URL for a section
  const getImageUrl = (section: string) => {
    const file = photoFiles[section as keyof typeof photoFiles];
    return file ? URL.createObjectURL(file) : undefined;
  };

  // Generic attach handler for all photo types
  const handleAttach = (section: string) => (event: React.MouseEvent<HTMLButtonElement>) => {
    setActiveMenu(section);
    setMenuAnchor(event.currentTarget);
  };

  // Toggle section expansion
  const toggleSection = (section: SectionKey) => {
    setExpandedSections({
      ...expandedSections,
      [section]: !expandedSections[section]
    });
  };

  // Update discomfort from dialog
  const handleUpdateDiscomfort = (newDiscomfort: IDiscomfort) => {
    setDiscomfort(newDiscomfort);
  };

  // Handle discomfort change
  const handleDiscomfortChange = (value: boolean) => {
    setHasDiscomfort(value);

    if (value) {
      // User selected "Yes" - open discomfort dialog
      setIsDiscomfortDialogOpen(true);

      // Initialize discomfort if needed
      if (discomfort.isDiscomfortChecked) {
        setDiscomfort({
          isDiscomfortChecked: false,
          selectedCondition: {},
          reportGenerated: {}
        });
        setSelectedBodyParts([]);
      }
    } else {
      // User selected "No"
      setDiscomfort({
        isDiscomfortChecked: true
      });
      setSelectedBodyParts([]);
    }
  };

  // Handle edit preferences
  const handleEditPreferences = () => {
    setIsDiscomfortDialogOpen(true);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setActiveMenu(null);
  };

  // Camera handler
  const handleCamera = (section: SectionKey) => () => {
    handleMenuClose();

    // Create input element dynamically
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.capture = 'environment'; // For camera on mobile

    // Handle file selection
    input.onchange = (e: Event) => {
      const target = e.target as HTMLInputElement;
      if (target.files && target.files[0]) {
        handleMobileFileChange(section, target.files[0]);
      }
    };

    // Trigger click
    input.click();
  };

  // Gallery handler
  const handleGallery = (section: SectionKey) => () => {
    handleMenuClose();

    // Create input element dynamically
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';

    // Handle file selection
    input.onchange = (e: Event) => {
      const target = e.target as HTMLInputElement;
      if (target.files && target.files[0]) {
        handleMobileFileChange(section, target.files[0]);
      }
    };

    // Trigger click
    input.click();
  };

  // Fixed mobile file change handler for mobile photos
  const handleMobileFileChange = async (section: SectionKey, file: File) => {
    const aiCaseId = loadQRInfo()?.caseId;

    // Basic validation
    if (file.size > 4900000) {
      if (section === 'sitting') setChairOnlyImageErrMsg("Image size must not be larger than 5 MB");
      else if (section === 'working') setDeskChairImageErrMsg("Image size must not be larger than 5 MB");
      else if (section === 'standing') setStandingDeskImageErrMsg("Image size must not be larger than 5 MB");
      return;
    }

    // Convert HEIC to JPEG if needed
    let processedFile = file;
    if (file.type === 'image/heic' || file.type === 'image/heif' ||
      file.name.toLowerCase().endsWith('.heic') || file.name.toLowerCase().endsWith('.heif')) {
      try {
        const heic2any = await import('heic2any').then(module => module.default);
        const jpegBlob = await heic2any({
          blob: file,
          toType: 'image/jpeg',
          quality: 0.8
        });

        const singleJpegBlob = Array.isArray(jpegBlob) ? jpegBlob[0] : jpegBlob;

        processedFile = new File(
          [singleJpegBlob],
          file.name.replace(/\.(heic|heif)$/i, '.jpg'),
          { type: 'image/jpeg' }
        );
      } catch (error) {
        console.error("Error converting HEIC image:", error);
        const errorMsg = "Error processing HEIC image. Please try another format.";
        if (section === 'sitting') setChairOnlyImageErrMsg(errorMsg);
        else if (section === 'working') setDeskChairImageErrMsg(errorMsg);
        else if (section === 'standing') setStandingDeskImageErrMsg(errorMsg);
        return;
      }
    }

    // Check image resolution
    const url = URL.createObjectURL(processedFile);
    const img = new Image();

    try {
      const isValidResolution = await new Promise<boolean>((resolve) => {
        img.onload = () => {
          URL.revokeObjectURL(url);

          if (img.width < 512 || img.height < 512) {
            const errorMsg = "Photo dimension must not be smaller than 512 x 512";
            if (section === 'sitting') setChairOnlyImageErrMsg(errorMsg);
            else if (section === 'working') setDeskChairImageErrMsg(errorMsg);
            else if (section === 'standing') setStandingDeskImageErrMsg(errorMsg);
            resolve(false);
            return;
          }

          const aspectRatio = img.width / img.height;
          if (!(aspectRatio >= 0.5 && aspectRatio <= 2)) {
            const errorMsg = "Aspect ratio of photo must not be larger than 2 or lesser than 0.5";
            if (section === 'sitting') setChairOnlyImageErrMsg(errorMsg);
            else if (section === 'working') setDeskChairImageErrMsg(errorMsg);
            else if (section === 'standing') setStandingDeskImageErrMsg(errorMsg);
            resolve(false);
            return;
          }

          resolve(true);
        };

        img.onerror = () => {
          URL.revokeObjectURL(url);
          resolve(false);
        };

        img.src = url;
      });

      if (!isValidResolution) return;

      // Clear error messages
      if (section === 'sitting') setChairOnlyImageErrMsg("");
      else if (section === 'working') setDeskChairImageErrMsg("");
      else if (section === 'standing') setStandingDeskImageErrMsg("");

      // Update photoFiles state for the mobile interface
      setPhotoFiles(prev => ({
        ...prev,
        [section]: processedFile
      }));

      // Sync with desktop state variables
      if (section === 'sitting') setChairOnlyImage(processedFile);
      else if (section === 'working') setDeskChairImage(processedFile);
      else if (section === 'standing') setStandingDeskImage(processedFile);

      const qrToken = loadQRInfo()?.token;

      // If there's an aiCaseId, upload the photo to the API
      if (aiCaseId && qrToken) {
        let photoType: ImgType;
        let setUploadSuccess: (isSuccess: boolean | null) => void;

        if (section === 'sitting') {
          photoType = "chair_only";
          setUploadSuccess = setIsChairUploadSuccess;
        } else if (section === 'working') {
          photoType = "desk_chair";
          setUploadSuccess = setIsDeskUploadSuccess;
        } else {
          photoType = "standing_desk";
          setUploadSuccess = setIsStandingUploadSuccess;
        }

        handleUploadPhotoToAPI(processedFile, aiCaseId, photoType, setUploadSuccess);
      } else {
        console.error("Unable to upload photo: Missing aiCaseId or qrToken");
        const errorMsg = "Error uploading photo. Please try again or use QR code option.";
        if (section === 'sitting') setChairOnlyImageErrMsg(errorMsg);
        else if (section === 'working') setDeskChairImageErrMsg(errorMsg);
        else if (section === 'standing') setStandingDeskImageErrMsg(errorMsg);
      }
    } catch (error) {
      console.error("Error processing image:", error);
      if (section === 'sitting') setChairOnlyImageErrMsg("Error processing image");
      else if (section === 'working') setDeskChairImageErrMsg("Error processing image");
      else if (section === 'standing') setStandingDeskImageErrMsg("Error processing image");
    }
  };

  // Fixed API upload function
  const handleUploadPhotoToAPI = async (
    photoFile: File,
    aiCaseId: number,
    photoType: ImgType,
    setIsUploadSuccess: (isSuccess: boolean | null) => void
  ) => {
    const qrToken = loadQRInfo()?.token;
    
    if (!photoFile || !aiCaseId || !qrToken) {
      console.error("Missing required parameters for upload");
      setIsUploadSuccess(false);
      return;
    }

    const ergonomicAnalyzeFormData = new FormData();
    ergonomicAnalyzeFormData.append("image", photoFile);
    ergonomicAnalyzeFormData.append("token", qrToken);

    let apiEndpoint;
    switch (photoType) {
      case "chair_only":
        apiEndpoint = CHAIR_ANALYSIS_ENDPOINT;
        break;
      case "desk_chair":
        apiEndpoint = DESK_ANALYSIS_ENDPOINT;
        break;
      case "standing_desk":
        apiEndpoint = STANDING_ANALYSIS_ENDPOINT;
        break;
    }

    let analysisIdField;
    switch (photoType) {
      case "chair_only":
        analysisIdField = "chairImageAnalysisId";
        break;
      case "desk_chair":
        analysisIdField = "chairDeskImageAnalysisId";
        break;
      case "standing_desk":
        analysisIdField = "standingDeskImageAnalysisId";
        break;
    }

    try {
      // Show some indication that upload is in progress
      setIsUploadSuccess(null); // null indicates "in progress"

      try {
        const aiBackendHealthStatus = await axios.get(`${AI_API_BASE_URL}/health`, { timeout: 30000 });
        if (aiBackendHealthStatus.status !== 200) {
          throw new Error("AI service not responding.");
        }
      } catch (e) {
        console.log("ai health check error = ", e);
        setIsAIOverloaded(true);
        setAiErrorMsg("Our AI services are currently overloaded. Please try again");
        setIsAiErrorModalOpened(true);
        setIsUploadSuccess(false);
        return;
      }

      const analyzeRes = await axios.post(`${AI_API_BASE_URL}${apiEndpoint}`, ergonomicAnalyzeFormData);
      const success = analyzeRes.status === 200;

      if (!success) {
        console.error("Upload failed with status:", analyzeRes.status);
        setIsUploadSuccess(false);
        return;
      }

      // Update the AI case with the analysis ID
      mutateUpdateAICase(
        {
          resource: "ai-cases",
          values: {
            [analysisIdField]: analyzeRes.data.analysis_id
          },
          id: aiCaseId,
        },
        {
          onSuccess: (res) => {
            console.log("[SUCCESS] update ai case:", res.data.data);

            // Upload the image to the API for storage
            let fieldPhotoType;
            switch (photoType) {
              case "chair_only":
                fieldPhotoType = "chairImage";
                break;
              case "desk_chair":
                fieldPhotoType = "chairDeskImage";
                break;
              case "standing_desk":
                fieldPhotoType = "standingImage";
                break;
            }

            const aiCaseUploadFormData = new FormData();
            aiCaseUploadFormData.append("files", photoFile, `aiCase_${aiCaseId}_${photoType}_image`);
            aiCaseUploadFormData.append("refId", aiCaseId.toString());
            aiCaseUploadFormData.append("ref", "api::ai-case.ai-case");
            aiCaseUploadFormData.append("field", fieldPhotoType);

            mutateCustomUploadAICaseImage(
              {
                url: `${API_URL}/api/upload`,
                method: "post",
                values: aiCaseUploadFormData,
              },
              {
                onSuccess: (uploadRes) => {
                  console.log("[SUCCESS] upload photo to AI Case:", uploadRes);
                  setIsUploadSuccess(true);
                },
                onError: (err) => {
                  console.error("[ERROR] upload photo to AI Case:", err);
                  // We don't set isUploadSuccess to false here because the main analysis
                  // upload already succeeded. This is just for storing the image.
                  setIsUploadSuccess(true);
                },
              }
            );
          },
          onError: (error) => {
            console.error("[ERROR] update ai case:", error);
            setIsUploadSuccess(false);
          },
        }
      );
    } catch (e) {
      console.error("Analyze upload error:", e);
      setIsUploadSuccess(false);
    }
  };

  return (
    <Stack>
      {/* Mobile Upload Dialog */}
      {showMobileUploadNotification && (
        <Dialog
          open={showMobileUploadNotification}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              padding: 3,
              borderRadius: 2
            }
          }}
        >
          <Stack spacing={3} alignItems="center">
            {mobileUploadStatus === "receiving" ? (
              <>
                <CircularProgress size={50} thickness={4} />
                <MLTypography variant="h6" align="center" fontWeight={600}>
                  Receiving photos from mobile device
                </MLTypography>
              </>
            ) : (
              <>
                <Box sx={{
                  width: 50,
                  height: 50,
                  borderRadius: '50%',
                  backgroundColor: '#4CAF50',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <CheckedIcon />
                </Box>
                <MLTypography variant="h6" align="center" fontWeight={600}>
                  Photos received successfully!
                </MLTypography>
                <MLTypography variant="body1" align="center">
                  Starting analysis...
                </MLTypography>
              </>
            )}
          </Stack>
        </Dialog>
      )}

      {loggedUserRoleIsEmployee &&
        <MLBanner isBackButton title={'AI Based Assessment'} subtitle='' backgroundColor='#E3DDFF' />
      }
      <Stack
        sx={{
          paddingBottom: {
            lg: desktop.contentContainer.paddingY,
            md: tablet.contentContainer.paddingY,
            xs: tablet.contentContainer.paddingY,
          },
        }}
      >

        <MLContainer>
          {!loggedUserRoleIsEmployee &&
            <Stack
              direction="row"
              alignItems="center"
              sx={{
                paddingY: {
                  lg: desktop.contentContainer.paddingY,
                  md: tablet.contentContainer.paddingY,
                  xs: tablet.contentContainer.paddingY,
                },
              }}
            >
              <IconButton size="large" onClick={() => back()}>
                <ErChevronleft />
              </IconButton>
              <MLTypography
                variant="h1"
                fontWeight={700}
                sx={{
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "wrap",
                  fontSize: { md: '46px', sm: '38px', xs: '23px' },
                  maxWidth: { xs: "calc(90vw - 100px)" },
                }}
              >
                AI based assessment: {userDetails.username}
              </MLTypography>
            </Stack>
          }
          {/* Side Angle upload */}
          <Stack gap={"25px"}>
            <Box
              sx={{
                display: "flex",
                flexDirection: { sm: "column", md: "row" },
                justifyContent: "space-between",
                gap: { sm: 5, md: 5 }
              }}
            >
              <Stack flex={1}>
                <Stack direction={"column"} width={'100%'} gap={'6px'}
                  sx={{
                    alignItems: "baseline",
                  }}
                >
                  <MLTypography variant="h1" fontSize={"31px"} fontWeight={700} lineHeight={1.2}>Upload photos of your office posture</MLTypography>
                  <MLTypography variant="body1" fontSize={"16px"} mb={1.5} fontWeight={600} lineHeight={1.2}>Max 5MB each. JPG, HEIC, PNG Only</MLTypography>
                </Stack>
              </Stack>

              {/* Right side - QR Code Component */}
              <MLButton
                variant="outlined"
                onClick={() => setShowQrCode(true)}
                startIcon={
                  <svg width="18" height="29" viewBox="0 0 18 29" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clipPath="url(#clip0_17612_114464)">
                      <path d="M0.899902 4.53125C0.899902 2.52542 2.5079 0.90625 4.4999 0.90625H13.4999C15.4919 0.90625 17.0999 2.52542 17.0999 4.53125V24.4688C17.0999 26.4746 15.4919 28.0938 13.4999 28.0938H4.4999C2.5079 28.0938 0.899902 26.4746 0.899902 24.4688V4.53125Z" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M0.899902 22.6562H17.0999" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </g>
                    <defs>
                      <clipPath id="clip0_17612_114464">
                        <rect width="18" height="29" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>

                }
                sx={{
                  alignSelf: 'flex-start',
                  paddingX: "16px",
                  paddingY: "14px",
                  display: { xs: "none", sm: 'none', md: showQrCode ? "none" : "flex" },
                }}
              >
                Upload photos with your phone
              </MLButton>
              <Stack flex={1} display={{ xs: "none", sm: 'none', md: showQrCode ? "flex" : "none" }}>
                <QRCodeSection
                  employeeRole={loggedUserRoleIsEmployee}
                  employeeId={userDetails ? userDetails?.employee?.id : employeeData?.data.employee.id}
                  isAnalyzing={isAnalyzing}
                  setShowQrCode={setShowQrCode}
                />
              </Stack>
            </Box>


            <Stack display={{ xs: 'none', sm: 'flex' }} gap={"32px"}>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: { xs: "1fr", md: "repeat(3, 1fr)" },
                  gap: "38px",
                  width: "100%",
                }}
              >
                <Box sx={{ display: "flex", height: "100%" }}>
                  <UploadPhoto
                    inputRef={chairOnlyInputRef}
                    imgType={"chair_only"}
                    imgUrl={chairOnlyPreview ?? ""}
                    imageFile={chairOnlyImage}
                    imgErrMsg={chairOnlyImageErrMsg}
                    samplePhoto={"https://app.synergopro.com/strapi/sitting_square_a136e1202a.png"}
                    setImageErrMsg={setChairOnlyImageErrMsg}
                    setImage={setChairOnlyImage}
                    setIsUploadSuccess={setIsChairUploadSuccess}
                    setAiErrorMsg={setAiErrorMsg}
                    setIsAiErrorModalOpened={setIsAiErrorModalOpened}
                  />
                </Box>
                <Box sx={{ display: "flex", height: "100%" }}>
                  <UploadPhoto
                    inputRef={deskChairInputRef}
                    imgType={"desk_chair"}
                    imgUrl={deskChairPreview ?? ""}
                    imageFile={deskChairImage}
                    imgErrMsg={deskChairImageErrMsg}
                    samplePhoto={"https://app.synergopro.com/strapi/working_square_797712d98b.png"}
                    setImageErrMsg={setDeskChairImageErrMsg}
                    setImage={setDeskChairImage}
                    setIsUploadSuccess={setIsDeskUploadSuccess}
                    setAiErrorMsg={setAiErrorMsg}
                    setIsAiErrorModalOpened={setIsAiErrorModalOpened}
                  />
                </Box>
                <Box sx={{ display: "flex", height: "100%" }}>
                  <UploadPhoto
                    inputRef={standingDeskInputRef}
                    imgType={"standing_desk"}
                    imgUrl={standingDeskPreview ?? ""}
                    imageFile={standingDeskImage}
                    imgErrMsg={standingDeskImageErrMsg}
                    samplePhoto={"https://app.synergopro.com/strapi/standing_square_101357bb4f.png"}
                    setImageErrMsg={setStandingDeskImageErrMsg}
                    setImage={setStandingDeskImage}
                    setIsUploadSuccess={setIsStandingUploadSuccess}
                    setAiErrorMsg={setAiErrorMsg}
                    setIsAiErrorModalOpened={setIsAiErrorModalOpened}
                    setIsUploading={setIsStandingDeskUploading}
                  />
                </Box>
              </Box>

              {/* Discomfort Question */}
              <Box sx={{ display: 'flex', flexDirection: 'column', }} >
                <MLTypography variant="h2" sx={{ fontSize: '24px', fontWeight: 700 }}>
                  Have any special conditions or discomfort?
                </MLTypography>

                <Stack flexDirection={'row'} gap={4} alignItems={'flex-start'} justifyContent={'flex-start'}>
                  <MLToggleButtonGroup
                    value={hasDiscomfort}
                    exclusive
                    onChange={(_e, newValue) => {
                      if (newValue === null) return;      // no-op if they de‑select
                      setHasDiscomfort(newValue);
                      handleDiscomfortChange(newValue);
                    }}
                    sx={{ mt: 1.5 }}
                  >
                    <MLToggleButton value={true}>Yes</MLToggleButton>
                    <MLToggleButton value={false}>No</MLToggleButton>
                  </MLToggleButtonGroup>

                  {hasDiscomfort && (
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                      mt: 1
                    }}>
                      <Editpeference />
                      <MLButton sx={{
                        textTransform: 'capitalize'
                      }} onClick={handleEditPreferences}>
                        Edit preferences
                      </MLButton>
                    </Box>
                  )}
                </Stack>

                {/* Discomfort Selection Dialog */}
                <DiscomfortSelector
                  open={isDiscomfortDialogOpen}
                  onClose={() => setIsDiscomfortDialogOpen(false)}
                  discomfort={discomfort}
                  setDiscomfort={handleUpdateDiscomfort}
                  bodyPartOptions={bodyParts}
                  selectedBodyParts={selectedBodyParts}
                  setSelectedBodyParts={setSelectedBodyParts}
                />
              </Box>

              {
                loggedUserRoleIsEmployee &&
                <Stack gap={"12px"}>
                  <MLTypography variant="h1" fontSize={"24px"} fontWeight={700} lineHeight={1.2} display={"inline-block"}>
                    Is your chair and desk height adjustable?
                  </MLTypography>
                  <Stack gap={{ xs: "20px", sm: "60px" }} direction={{ xs: "column", sm: "row" }}>
                    <Stack gap={"10px"}>
                      <MLTypography variant="body1" fontSize={"16px"} fontWeight={400} lineHeight={1.2} display={"inline-block"}>
                        Chair height
                      </MLTypography>
                      <MLToggleButtonGroup
                        value={workSetup.chair.adjustableHeight}
                        exclusive
                        onChange={(event, value) => {
                          if (value != null) {
                            setWorkSetup({
                              ...workSetup,
                              chair: {
                                ...workSetup.chair,
                                adjustableHeight: value
                              }
                            });
                          }
                        }}
                      >
                        <MLToggleButton value={true}>Yes</MLToggleButton>
                        <MLToggleButton value={false}>No</MLToggleButton>
                      </MLToggleButtonGroup>
                    </Stack>
                    <Stack gap={"10px"}>
                      <MLTypography variant="body1" fontSize={"16px"} fontWeight={400} lineHeight={1.2} display={"inline-block"}>
                        Desk height
                      </MLTypography>
                      <MLToggleButtonGroup
                        value={workSetup.sitStandDesk}
                        exclusive
                        onChange={(event, value) => {
                          if (value != null) {
                            setWorkSetup({
                              ...workSetup,
                              sitStandDesk: value
                            });
                          }
                        }}
                      >
                        <MLToggleButton value={true}>Yes</MLToggleButton>
                        <MLToggleButton value={false}>No</MLToggleButton>
                      </MLToggleButtonGroup>
                    </Stack>
                  </Stack>
                </Stack>
              }
            </Stack>

            {/* mobile view - start */}
            <Stack display={{ xs: 'flex', sm: 'none' }} gap={"32px"} >
              <Stack
                direction={{ xs: "column", md: "row" }}
                gap={"22px"}
              >
                {/* Render photo sections using the reusable component */}
                {photoSections.map(section => (
                  <PhotoSection
                    key={section.id}
                    title={section.title}
                    subtitle={section.subtitle}
                    instructions={photoInstructions[section.id as keyof typeof photoInstructions]}
                    samplePhotoUrl={samplePhotoUrls[section.id as keyof typeof samplePhotoUrls]}
                    imageFile={photoFiles[section.id as keyof typeof photoFiles]}
                    getImageUrl={() => getImageUrl(section.id)}
                    onAttachClick={handleAttach(section.id)}
                    isExpanded={expandedSections[section.id as keyof typeof expandedSections]}
                    onToggle={() => toggleSection(section.id)}
                    isAnalyzing={isAnalyzing}
                    hasImage={Boolean(photoFiles[section.id as keyof typeof photoFiles])}
                  />
                ))}

                {/* Camera/Gallery Menu */}
                <CameraGalleryMenu
                  menuAnchor={menuAnchor}
                  open={Boolean(menuAnchor)}
                  onClose={handleMenuClose}
                  onCameraClick={handleCamera(activeMenu as SectionKey || 'sitting')}
                  onGalleryClick={handleGallery(activeMenu as SectionKey || 'sitting')}
                  isDisabled={isAnalyzing}
                />
              </Stack>

              {/* Discomfort Question */}
              {
                loggedUserRoleIsEmployee && <Box sx={{ display: 'flex', flexDirection: 'column', }} >
                  <MLTypography variant="h2" sx={{ fontSize: '24px', fontWeight: 700, mb: 1, }}>
                    Do you experience any discomfort in your body?
                  </MLTypography>

                  <Stack flexDirection={'row'} gap={4} alignItems={'flex-start'} justifyContent={'flex-start'}>
                    <MLToggleButtonGroup
                      value={hasDiscomfort}
                      exclusive
                      onChange={(_e, newValue) => {
                        if (newValue === null) return;      // no-op if they de‑select
                        setHasDiscomfort(newValue);
                        handleDiscomfortChange(newValue);
                      }}
                      sx={{ mt: 1.5 }}
                    >
                      <MLToggleButton value={true}>Yes</MLToggleButton>
                      <MLToggleButton value={false}>No</MLToggleButton>
                    </MLToggleButtonGroup>

                    {hasDiscomfort && (
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        mt: 1
                      }}>
                        <Editpeference />
                        <MLButton sx={{
                          textTransform: 'capitalize'
                        }} onClick={handleEditPreferences}>
                          Edit preferences
                        </MLButton>
                      </Box>
                    )}
                  </Stack>

                  {/* Discomfort Selection Dialog */}
                  <DiscomfortSelector
                    open={isDiscomfortDialogOpen}
                    onClose={() => setIsDiscomfortDialogOpen(false)}
                    discomfort={discomfort}
                    setDiscomfort={handleUpdateDiscomfort}
                    bodyPartOptions={bodyParts}
                    selectedBodyParts={selectedBodyParts}
                    setSelectedBodyParts={setSelectedBodyParts}
                    axiosInstance={axiosRef.current}
                  />
                </Box>
              }

              {/* Work Setup Questions */}
              {
                loggedUserRoleIsEmployee &&
                <Stack gap={"12px"}>
                  <MLTypography variant="h1" fontSize={"24px"} fontWeight={700} lineHeight={1.2} display={"inline-block"}>
                    Your work setup question
                  </MLTypography>
                  <Stack gap={{ xs: "20px", sm: "60px" }} direction={{ xs: "column", sm: "row" }}>
                    <Stack gap={"10px"}>
                      <MLTypography variant="body1" fontSize={"16px"} fontWeight={400} lineHeight={1.2} display={"inline-block"}>
                        Is your chair height adjustable?
                      </MLTypography>
                      <MLToggleButtonGroup
                        value={workSetup.chair.adjustableHeight}
                        exclusive
                        onChange={(event, value) => {
                          if (value != null) {
                            setWorkSetup({
                              ...workSetup,
                              chair: {
                                ...workSetup.chair,
                                adjustableHeight: value
                              }
                            });
                          }
                        }}
                      >
                        <MLToggleButton value={true}>Yes</MLToggleButton>
                        <MLToggleButton value={false}>No</MLToggleButton>
                      </MLToggleButtonGroup>
                    </Stack>
                    <Stack gap={"10px"}>
                      <MLTypography variant="body1" fontSize={"16px"} fontWeight={400} lineHeight={1.2} display={"inline-block"}>
                        Is your desk height adjustable?
                      </MLTypography>
                      <MLToggleButtonGroup
                        value={workSetup.sitStandDesk}
                        exclusive
                        onChange={(event, value) => {
                          if (value != null) {
                            setWorkSetup({
                              ...workSetup,
                              sitStandDesk: value
                            });
                          }
                        }}
                      >
                        <MLToggleButton value={true}>Yes</MLToggleButton>
                        <MLToggleButton value={false}>No</MLToggleButton>
                      </MLToggleButtonGroup>
                    </Stack>
                  </Stack>
                </Stack>
              }
            </Stack>
            {/* mobile view - end */}
          </Stack>

          <Divider
            sx={{
              marginY: "26px",
            }}
          />

          <Stack gap={"35px"}>
            <Stack direction={"row"}
              sx={{
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <MLCheckbox
                name="termsAcceptCheckbox"
                checked={isTermsAccepted}
                onChange={() => setIsTermsAccepted(!isTermsAccepted)}
              />
              <MLTypography fontSize={"14px"} variant="body1" fontWeight={400}>I accept the</MLTypography>
              <MLButton
                disableRipple
                sx={{
                  paddingX: "5px",
                  justifyContent: "center",
                  alignItems: "center",
                  "&:hover": { backgroundColor: "transparent" },
                  textTransform: 'none',
                  textDecoration: "underline"
                }}
                onClick={() => setIsTOCModalOpened(true)}
              >
                Terms and Conditions
              </MLButton>
            </Stack>

            {loggedUserRoleIsEmployee ?
              <Stack direction={"row"}
                sx={{
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MLButton
                  variant="contained"
                  color="secondary"
                  endIcon={<ChevronRight />}
                  sx={{
                    paddingX: "24px",
                    textTransform: 'uppercase',
                    alignSelf: 'flex-start',
                    backgroundColor: '#DFFF32',
                  }}
                  disabled={!isTermsAccepted || workSetup.chair.adjustableHeight == undefined || workSetup.sitStandDesk == undefined || isAnalyzing}
                  onClick={() => {
                    if (!deskChairImage) {
                      setDeskChairImageErrMsg("Workstation photo required");
                      return;
                    }
                    if (!chairOnlyImage) {
                      setChairOnlyImageErrMsg("Chair photo required");
                      return;
                    }
                    if (isStandingDeskUploading) {
                      setStandingDeskImageErrMsg("Standing desk is uploading");
                      return
                    }
                    setIsProgressModalOpen(true);
                    setIsAnalyzing(true);
                  }}
                >
                  {isAnalyzing ? 'Analyzing...' : 'Submit for posture analysis'}
                </MLButton>
              </Stack>
              :
              <Stack direction={{ xs: "column", sm: "row" }} spacing={2}
                sx={{
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MLButton
                  variant="contained"
                  color="secondary"
                  endIcon={<ChevronRight />}
                  sx={{
                    paddingX: "18px",
                    textTransform: 'uppercase',
                    backgroundColor: '#DFFF32',
                  }}
                  disabled={!isTermsAccepted || workSetup.chair.adjustableHeight == undefined || workSetup.sitStandDesk == undefined || isAnalyzing}
                  onClick={() => {
                    if (!deskChairImage && !chairOnlyImage) {
                      setDeskChairImageErrMsg("Workstation photo required");
                      setChairOnlyImageErrMsg("Chair photo required");
                      return;
                    }
                    else if (!deskChairImage) {
                      setDeskChairImageErrMsg("Workstation photo required");
                      return;
                    }
                    else if (!chairOnlyImage) {
                      setChairOnlyImageErrMsg("Chair photo required");
                      return;
                    }
                    setIsProgressModalOpen(true);
                    setIsAnalyzing(true);
                  }}
                >
                  {isAnalyzing ? 'Analyzing...' : 'Submit and get result'}
                </MLButton>

                <MLButton
                  variant="outlined"
                  color="primary"
                  sx={{
                    paddingX: "18px",
                    textTransform: 'uppercase',
                  }}
                  disabled={!isTermsAccepted || workSetup.chair.adjustableHeight == undefined || workSetup.sitStandDesk == undefined || isAnalyzing}
                  onClick={() => {
                    if (!deskChairImage) {
                      setDeskChairImageErrMsg("Workstation photo required");
                      return;
                    }
                    if (!chairOnlyImage) {
                      setChairOnlyImageErrMsg("Chair photo required");
                      return;
                    }
                    setIsAnalyzing(true);
                    navigate('/aicase/create');
                  }}
                >
                  Submit and Move to next employee
                </MLButton>
              </Stack>
            }
          </Stack>
        </MLContainer>
      </Stack >

      {/** Progress modal */}
      <Modal
        open={isProgressModalOpen}
      >
        <Stack
          sx={{
            padding: { xs: "24px", sm: "32px" },
            position: "absolute",
            top: { xs: "50%", md: "50%" },
            left: { xs: "0%", md: "50%" },
            transform: { xs: "translate(0%, -50%)", md: "translate(-50%, -50%)" },
            bgcolor: "background.paper",
            boxShadow: 24,
            display: "flex",
            flexDirection: "column",
            gap: 2,
            borderRadius: "4px",
            width: { xs: "100vw", md: "90vw", lg: "90vw" },
            maxWidth: "1200px",
            maxHeight: { xs: "100vh", md: "80vh" },
            height: "auto",
            overflow: "auto",
            "&::-webkit-scrollbar": {
              width: "8px"
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "rgba(0,0,0,.2)",
              borderRadius: "4px"
            }
          }}
        >
          <MLTypography variant="h1" fontSize={{ xs: "24px", sm: "40px" }} fontWeight={700} lineHeight={1.2}>
            Performing expert analysis...
          </MLTypography>

          {/* Add progress notification when analysis hasn't started yet */}
          {loadProgress[ProgressStage.PRECHECKING] === "not_started" && (
            <Box mb={2}>
              <ProgressNotification message="Initializing AI analysis system..." />
            </Box>
          )}
          <Stack
            direction={{ xs: "column", md: "row" }}
            gap={"32px"}>
            <Stack
              flex={8}
              direction={"row"}
              gap={{ xs: "4px", md: "16px" }}
              sx={{
                position: 'relative',
                border: "1px solid #333",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {isAnalyzing && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0, // Make the box fill the entire container
                    zIndex: 1,
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      top: '-24px',
                      left: 0,
                      right: 0,
                      height: '50px',
                      background: `linear-gradient(to bottom,
                        rgba(255,110,110,0) 0%,
                        rgba(255,110,110,0.1) 30%,
                        rgba(255,110,110,0.4) 45%,
                        rgba(255,110,110,1) 48%,
                        rgba(255,110,110,1) 52%,
                        rgba(255,110,110,0.4) 55%,
                        rgba(255,110,110,0.1) 70%,
                        rgba(255,110,110,0) 100%)`,
                      animation: 'scanning 2s linear infinite'
                    },
                    '@keyframes scanning': {
                      '0%': {
                        top: '0'
                      },
                      '100%': {
                        // Animate the top position instead of using transform
                        top: 'calc(100% - 24px)'
                      }
                    }
                  }}
                />
              )}
              <Stack
                direction={"row"}
                sx={{
                  width: "100%",
                  height: "100%",
                }}
                gap={"10px"}
              >
                {sortedImages.map((img, index) => {
                  return (
                    <Box
                      key={index}
                      flex={1}
                      component="img"
                      sx={{
                        width: standingDeskPreview === null ? "45%" : "30%",
                        height: "auto",
                        objectFit: "contain",
                      }}
                      src={img.url ?? ""}
                      alt={img.alt}
                    />
                  );
                })}
              </Stack>
            </Stack>
            <Stack flex={4} gap={"40px"}>
              {
                !loggedUserRoleIsEmployee &&
                <AssessmentForWhomBanner name={userDetails.username} />
              }
              <Stack gap={"20px"}>
                <Stack direction={"row"} gap={"16px"}
                  sx={{
                    alignItems: "center",
                  }}
                >
                  {loadProgress["Conducting prechecks"] === "not_started" ? <LoadingIcon /> : <></>}
                  {loadProgress["Conducting prechecks"] === "ongoing" ? <CircularProgress size={"20px"} /> : <></>}
                  {loadProgress["Conducting prechecks"] === "done" ? <CheckedIcon /> : <></>}
                  {loadProgress["Conducting prechecks"] === "error" ? <Warning sx={{ color: "#C40000" }} /> : <></>}
                  <MLTypography variant={"body1"} fontSize={"16px"} fontWeight={400} lineHeight={1.2}>
                    {ProgressStage.PRECHECKING}
                  </MLTypography>
                </Stack>
                <Stack direction={"row"} gap={"16px"}>
                  {loadProgress["Examining work setup"] === "not_started" ? <LoadingIcon /> : <></>}
                  {loadProgress["Examining work setup"] === "ongoing" ? <CircularProgress size={"20px"} /> : <></>}
                  {loadProgress["Examining work setup"] === "done" ? <CheckedIcon /> : <></>}
                  {loadProgress["Examining work setup"] === "error" ? <Warning sx={{ color: "#C40000" }} /> : <></>}
                  <MLTypography variant={"body1"} fontSize={"16px"} fontWeight={400} lineHeight={1.2}>
                    {ProgressStage.EXAMINE_WORKSETUP}
                  </MLTypography>
                </Stack>
                <Stack direction={"row"} gap={"16px"}>
                  {loadProgress["Assessing posture"] === "not_started" ? <LoadingIcon /> : <></>}
                  {loadProgress["Assessing posture"] === "ongoing" ? <CircularProgress size={"20px"} /> : <></>}
                  {loadProgress["Assessing posture"] === "done" ? <CheckedIcon /> : <></>}
                  {loadProgress["Assessing posture"] === "error" ? <Warning sx={{ color: "#C40000" }} /> : <></>}
                  <MLTypography variant={"body1"} fontSize={"16px"} fontWeight={400} lineHeight={1.2}>
                    {ProgressStage.ASSESSING_POSTURE}
                  </MLTypography>
                </Stack>
                <Stack direction={"row"} gap={"16px"}>
                  {loadProgress["Identifying ergo risks"] === "not_started" ? <LoadingIcon /> : <></>}
                  {loadProgress["Identifying ergo risks"] === "ongoing" ? <CircularProgress size={"20px"} /> : <></>}
                  {loadProgress["Identifying ergo risks"] === "done" ? <CheckedIcon /> : <></>}
                  {loadProgress["Identifying ergo risks"] === "error" ? <Warning sx={{ color: "#C40000" }} /> : <></>}
                  <MLTypography variant={"body1"} fontSize={"16px"} fontWeight={400} lineHeight={1.2}>
                    {ProgressStage.IDENTIFY_ERGORISK}
                  </MLTypography>
                </Stack>
                <Stack direction={"row"} gap={"16px"}>
                  {loadProgress["Creating recommendations"] === "not_started" ? <LoadingIcon /> : <></>}
                  {loadProgress["Creating recommendations"] === "ongoing" ? <CircularProgress size={"20px"} /> : <></>}
                  {loadProgress["Creating recommendations"] === "done" ? <CheckedIcon /> : <></>}
                  {loadProgress["Creating recommendations"] === "error" ? <Warning sx={{ color: "#C40000" }} /> : <></>}
                  <MLTypography variant={"body1"} fontSize={"16px"} fontWeight={400} lineHeight={1.2}>
                    {ProgressStage.GENERATING_REPORT}
                  </MLTypography>
                </Stack>
              </Stack>
              <Stack height={"100%"}
                sx={{
                  justifyContent: "space-between",
                }}
              >
                <MLButton
                  disabled={loadProgress["Creating recommendations"] !== "done"}
                  variant="contained"
                  color="secondary"
                  sx={{
                    paddingX: "24px",
                    textTransform: 'uppercase',
                    alignSelf: { xs: "center", sm: 'flex-start' },
                    backgroundColor: '#DFFF32',
                  }}
                  onClick={handleViewPersonalizedReport}
                >
                  {loggedUserRoleIsEmployee ? 'View personalised report' : 'View AI assessment results'}
                </MLButton>
                <MLButton
                  disabled={isAnalyzing && !isReportCreated ? true : aiErrorMsg !== ""}
                  disableRipple
                  startIcon={<ChevronLeft />}
                  onClick={() => {
                    window.location.reload();
                  }}
                  sx={{
                    "&:hover": { backgroundColor: "transparent" },
                    alignSelf: { xs: "center", sm: 'flex-start' },
                    paddingX: "24px",
                  }}
                >
                  Back to upload image
                </MLButton>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
      </Modal>

      {/** Error message modal */}
      <Dialog
        open={isAiErrorModalOpened}
      >
        <Stack
          sx={{
            paddingY: "30px",
            paddingX: "40px",
            gap: "14px"
          }}
        >
          <Stack direction={"row"}>
            <MLTypography variant="h5">
              Error occurred during analysis
            </MLTypography>
          </Stack>
          <MLTypography variant="body1">
            {aiErrorMsg}
          </MLTypography>
          <Stack direction={"row"}
            sx={{
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <MLButton
              variant="contained"
              color="secondary"
              onClick={() => {
                setIsAiErrorModalOpened(false);
                if (isAIOverLoaded) {
                  navigate('/');
                } else {
                  window.location.reload();
                }
              }}
            >
              Close
            </MLButton>
          </Stack>
        </Stack>
      </Dialog>

      {/** Terms and conditions */}
      <Modal
        open={isTOCModalOpened}
        onClose={() => setIsTOCModalOpened(false)}
      >
        <Stack
          sx={{
            position: "absolute",
            top: { xs: "0%", sm: "50%" },
            left: { xs: "0%", sm: "50%" },
            transform: { xs: "translate(0%, 0%)", sm: "translate(-50%, -50%)" },
            bgcolor: "background.paper",
            boxShadow: 24,
            display: "flex",
            flexDirection: "column",
            gap: 2,
            borderRadius: "10px",
            // maxWidth: { xs: "100vw", sm: "60vw" },
            width: { xs: '100%', sm: '580px', md: '650px', lg: '80%' },
            maxHeight: { xs: "100vh", sm: "90vh" },
            height: "auto",
            overflow: "auto",
            "&::-webkit-scrollbar": {
              width: "8px"
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "rgba(0,0,0,.2)",
              borderRadius: "4px"
            }
          }}
        >
          <Stack
            sx={{
              p: { xs: "24px", sm: "32px" }
            }}
          >
            <MLTypography fontSize={"32px"} variant="h1" fontWeight={500}>
              Terms and Conditions
            </MLTypography>
            <Markdown>
              {aiTermsAndConditions}
            </Markdown>
          </Stack>
          <Stack
            sx={{
              position: "sticky",
              bottom: 0,
              backgroundColor: "white",
              paddingX: "32px",
              paddingY: "10px"
            }}
          >
            <MLButton
              variant="contained"
              color="secondary"
              sx={{
                paddingX: "24px",
                textTransform: 'uppercase',
                alignSelf: 'flex-end',
              }}
              onClick={() => setIsTOCModalOpened(false)}
            >
              Close
            </MLButton>
          </Stack>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default AIAssessment;