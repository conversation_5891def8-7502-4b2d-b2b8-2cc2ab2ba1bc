import React, { useState, useEffect, useRef } from 'react';
import {
    <PERSON>ack,
    Box,
    Menu,
    MenuItem,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogContentText,
    DialogActions
} from '@mui/material';
import { ChevronRight } from '@mui/icons-material';
import CameraAltIcon from '@mui/icons-material/CameraAlt';
import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary';
import { useParams, useLocation } from 'react-router-dom';
import axios, { AxiosInstance } from 'axios';
import { API_URL, TOKEN_KEY } from '../../../constants';

import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../components/ui/MLButton/MLButton';
import MLBanner from '../../../components/ui/MLBanner/MLBanner';
import MLCheckbox from '../../../components/ui/MLCheckbox/MLCheckbox';
import Markdown from 'react-markdown';
import Loading from '../../Loading/Loading';
import MLToggleButtonGroup from '../../../components/ui/MLToggleButtonGroup/MLToggleButtonGroup';
import MLToggleButton from '../../../components/ui/MLToggleButton/MLToggleButton';
import MobileUploadStatus from './MobileUploadStatus';
import Editpeference from '../../../assets/icons/Editpeference';
import { PhotoSection } from './PhotoSection';
import DiscomfortSelector from './DiscomfortDialog';
import { OptionType } from '../../../components/ui/MLMultiSelecttag/MLSelecttag';

// Define constants for API URLs
const AI_API_BASE_URL = 'https://aistream.synergopro.com';
const CHAIR_ANALYSIS_ENDPOINT = '/api/v1/ergonomic/analyze-chair/async';
const DESK_ANALYSIS_ENDPOINT = '/api/v1/ergonomic/analyze-desk/async';
const STANDING_ANALYSIS_ENDPOINT = '/api/v1/ergonomic/analyze-standing/async';

// Define types for API responses
interface AIAnalysisResponse {
    analysis_id: string;
    status: string;
}

// Define progress statuses
enum ProgressStatus {
    NONE = '',
    UPLOADING = 'Uploading',
    UPLOADED = 'Uploaded',
    PROCESSING = 'Processing',
    FAILED = 'Failed',
    COMPLETED = 'Completed'
}

// Define discomfort interface
export interface IDiscomfort {
    isDiscomfortChecked?: boolean;
    selectedCondition?: {
        [key: string]: {
            isSelected: boolean;
            affectsBodyPart: string;
            affectsBodyPartText: string;
        }
    };
    reportGenerated?: {
        [key: string]: string;
    };
    [key: string]: any;
}

// Define image type
export type ImgType = "chair_only" | "desk_chair" | "standing_desk";

// Define section key type for type safety
type SectionKey = 'sitting' | 'working' | 'standing';

// Camera/Gallery Menu component
interface CameraGalleryMenuProps {
    menuAnchor: null | HTMLElement;
    open: boolean;
    onClose: () => void;
    onCameraClick: () => void;
    onGalleryClick: () => void;
    isDisabled: boolean;
}

const CameraGalleryMenu: React.FC<CameraGalleryMenuProps> = ({
    menuAnchor,
    open,
    onClose,
    onCameraClick,
    onGalleryClick,
    isDisabled
}) => {
    return (
        <Menu
            anchorEl={menuAnchor}
            open={open}
            onClose={onClose}
        >
            <MenuItem onClick={onCameraClick} disabled={isDisabled}>
                <CameraAltIcon sx={{ mr: 1 }} /> Take Photo
            </MenuItem>
            <MenuItem onClick={onGalleryClick} disabled={isDisabled}>
                <PhotoLibraryIcon sx={{ mr: 1 }} /> Choose from Gallery
            </MenuItem>
        </Menu>
    );
};

const MobilePhotoUpload: React.FC = () => {
    const { employeeId, caseId: AiCaseId, token } = useParams<{ employeeId: string, caseId: string, token: string }>();
    const location = useLocation();

    // State for terms and conditions
    const [isTOCModalOpened, setIsTOCModalOpened] = useState<boolean>(false);
    const [isTermsAccepted, setIsTermsAccepted] = useState<boolean>(false);

    // State for confirmation dialog
    const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);

    // Error messages for image validations
    const [chairOnlyImageErrMsg, setChairOnlyImageErrMsg] = useState<string>('');
    const [deskChairImageErrMsg, setDeskChairImageErrMsg] = useState<string>('');
    const [standingImageErrMsg, setStandingImageErrMsg] = useState<string>('');

    // Image conversion state
    const [isChairConverting, setIsChairConverting] = useState<boolean>(false);
    const [isDeskConverting, setIsDeskConverting] = useState<boolean>(false);
    const [isStandingConverting, setIsStandingConverting] = useState<boolean>(false);

    // Upload success states - THESE ARE CRITICAL FOR SUBMIT BUTTON
    const [isChairUploadSuccess, setIsChairUploadSuccess] = useState<boolean | null>(null);
    const [isDeskUploadSuccess, setIsDeskUploadSuccess] = useState<boolean | null>(null);
    const [isStandingUploadSuccess, setIsStandingUploadSuccess] = useState<boolean | null>(null);

    // Analyzing state
    const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);

    // Upload status overlay state
    const [uploadStatus, setUploadStatus] = useState<'uploading' | 'complete' | 'receiving' | 'processing'>('uploading');
    const [showUploadStatusOverlay, setShowUploadStatusOverlay] = useState<boolean>(false);

    // Error state for invalid QR
    const [qrError, setQrError] = useState<string>("");

    // Terms and conditions
    const [aiTermsAndConditions, setAiTermsAndConditions] = useState<string>("");

    // State for managing expanded sections
    const [expandedSections, setExpandedSections] = useState<Record<SectionKey, boolean>>({
        sitting: false,
        working: false,
        standing: false
    });

    // Add new state for immediate upload feedback
    const [isUploadingSection, setIsUploadingSection] = useState<SectionKey | null>(null);
    const [uploadStageMessage, setUploadStageMessage] = useState<string>('');

    // Work setup state
    const [workSetup, setWorkSetup] = useState({
        "chair": {
            "armRest": false,
            "seatDepth": false,
            "adjustableHeight": undefined
        },
        "sitStandDesk": undefined
    });

    // Discomfort state
    const [discomfort, setDiscomfort] = useState<IDiscomfort>({ isDiscomfortChecked: true });
    const [hasDiscomfort, setHasDiscomfort] = useState<boolean>(false);
    const [isDiscomfortDialogOpen, setIsDiscomfortDialogOpen] = useState<boolean>(false);

    // Add these new state variables
    const [uploadProgress, setUploadProgress] = useState<number>(0);
    const [uploadingSection, setUploadingSection] = useState<SectionKey | null>(null);

    // AI analysis states
    const [chairAnalysisId, setChairAnalysisId] = useState<string>('');
    const [deskAnalysisId, setDeskAnalysisId] = useState<string>('');
    const [standingAnalysisId, setStandingAnalysisId] = useState<string>('');
    const [chairProgress, setChairProgress] = useState<ProgressStatus>(ProgressStatus.NONE);
    const [deskProgress, setDeskProgress] = useState<ProgressStatus>(ProgressStatus.NONE);
    const [standingProgress, setStandingProgress] = useState<ProgressStatus>(ProgressStatus.NONE);

    // Menu states for camera/gallery selection
    const [activeMenu, setActiveMenu] = useState<string | null>(null);
    const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

    // File states
    const [photoFiles, setPhotoFiles] = useState<Record<SectionKey, File | undefined>>({
        sitting: undefined,
        working: undefined,
        standing: undefined
    });

    // Camera and gallery refs - consolidated into objects for better organization
    const cameraRefs = {
        sitting: useRef<HTMLInputElement>(null),
        working: useRef<HTMLInputElement>(null),
        standing: useRef<HTMLInputElement>(null)
    };

    const galleryRefs = {
        sitting: useRef<HTMLInputElement>(null),
        working: useRef<HTMLInputElement>(null),
        standing: useRef<HTMLInputElement>(null)
    };

    // Case data
    const [caseData, setCaseData] = useState<any>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isTokenValidationDialogOpen, setIsTokenValidationDialogOpen] = useState<boolean>(false);
    // For discomfort body parts
    const [bodyParts, setBodyParts] = useState<OptionType[]>([]);
    const [selectedBodyParts, setSelectedBodyParts] = useState<OptionType[]>([]);

    // Initialize a reference to hold our axios instance
    const axiosRef = useRef<AxiosInstance | null>(null);

    // Photo section configurations
    const photoSections: Array<{ id: SectionKey; title: string; subtitle: string }> = [
        {
            id: 'sitting',
            title: '1. Sitting in chair',
            subtitle: ''
        },
        {
            id: 'working',
            title: '2. Working on computer',
            subtitle: ''
        },
        {
            id: 'standing',
            title: '3. Standing',
            subtitle: '(Optional)'
        }
    ];

    // Instructions for each section
    const photoInstructions = {
        sitting: [
            "Full Side profile (Head to feet)",
            "Hands on thighs. Looking in front."
        ],
        working: [
            "Full Side profile (Head to feet)",
            "Hands on keyboard. Looking at screen"
        ],
        standing: [
            "Full Side profile (Head to feet)",
            "Standing. Hands on keyboard. Looking at screen"
        ]
    };

    // Sample photo URLs
    const samplePhotoUrls = {
        sitting: "https://app.synergopro.com/strapi/sitting_square_a136e1202a.png",
        working: "https://app.synergopro.com/strapi/working_square_797712d98b.png",
        standing: "https://app.synergopro.com/strapi/standing_square_101357bb4f.png"
    };

    // Toggle section expansion
    const toggleSection = (section: SectionKey) => {
        setExpandedSections({
            ...expandedSections,
            [section]: !expandedSections[section]
        });
    };

    // Map section key to ImgType
    const getSectionImgType = (section: SectionKey): ImgType => {
        switch (section) {
            case 'sitting': return 'chair_only';
            case 'working': return 'desk_chair';
            case 'standing': return 'standing_desk';
        }
    };

    // Map section key to field name
    const getSectionFieldName = (section: SectionKey): string => {
        switch (section) {
            case 'sitting': return 'chairImage';
            case 'working': return 'chairDeskImage';
            case 'standing': return 'standingImage';
        }
    };

    // Map section key to API endpoint
    const getSectionEndpoint = (section: SectionKey): string => {
        switch (section) {
            case 'sitting': return CHAIR_ANALYSIS_ENDPOINT;
            case 'working': return DESK_ANALYSIS_ENDPOINT;
            case 'standing': return STANDING_ANALYSIS_ENDPOINT;
        }
    };

    // Get error setter for a section
    const getSectionErrorSetter = (section: SectionKey): React.Dispatch<React.SetStateAction<string>> => {
        switch (section) {
            case 'sitting': return setChairOnlyImageErrMsg;
            case 'working': return setDeskChairImageErrMsg;
            case 'standing': return setStandingImageErrMsg;
        }
    };

    // Get upload success setter for a section
    const getSectionUploadSuccessSetter = (section: SectionKey): React.Dispatch<React.SetStateAction<boolean | null>> => {
        switch (section) {
            case 'sitting': return setIsChairUploadSuccess;
            case 'working': return setIsDeskUploadSuccess;
            case 'standing': return setIsStandingUploadSuccess;
        }
    };

    // Get converting state setter for a section
    const getSectionConvertingSetter = (section: SectionKey): React.Dispatch<React.SetStateAction<boolean>> => {
        switch (section) {
            case 'sitting': return setIsChairConverting;
            case 'working': return setIsDeskConverting;
            case 'standing': return setIsStandingConverting;
        }
    };

    // Get progress state setter for a section
    const getSectionProgressSetter = (section: SectionKey): React.Dispatch<React.SetStateAction<ProgressStatus>> => {
        switch (section) {
            case 'sitting': return setChairProgress;
            case 'working': return setDeskProgress;
            case 'standing': return setStandingProgress;
        }
    };

    // Fetch body parts when component mounts
    useEffect(() => {
        if (!axiosRef.current) return;

        const fetchBodyParts = async () => {
            try {
                // Fetch body parts
                const bodyPartsResponse = await axiosRef!.current!.get('/api/self-assessment-body-part-configs');
                if (bodyPartsResponse.data?.data) {
                    const sortedOptions = bodyPartsResponse.data.data
                        .map((config: any) => ({
                            label: config.attributes.bodyPartText,
                            value: config.id,
                        }))
                        .sort((a: any, b: any) => a.label.toLowerCase().localeCompare(b.label.toLowerCase()));

                    setBodyParts(sortedOptions);
                }
            } catch (error) {
                console.error("Error fetching body part data:", error);
            }
        };

        fetchBodyParts();
    }, [axiosRef.current]);

    // Extract JWT token from query params and configure axios
    useEffect(() => {
        const queryParams = new URLSearchParams(location.search);
        const jwtToken = queryParams.get('auth');

        if (jwtToken) {
            // Store the JWT token in localStorage for API usage
            localStorage.setItem(TOKEN_KEY, jwtToken);

            // Create a configured axios instance
            axiosRef.current = axios.create({
                baseURL: API_URL,
                headers: {
                    Authorization: `Bearer ${jwtToken}`
                }
            });
        } else {
            console.error("No JWT token found in URL");
        }
    }, [location]);

    // Fetch case details with the configured axios instance
    useEffect(() => {
        const fetchData = async () => {
            if (!AiCaseId || !axiosRef.current) {
                setIsLoading(false);
                return;
            }

            setIsLoading(true);
            try {
                // Fetch terms and conditions
                const termsResponse = await axiosRef.current.get(`/api/common-text-contents?filters[textContentName][$eq]=aiAssessment-termsAndConditions`);
                if (termsResponse.data?.data?.length > 0) {
                    setAiTermsAndConditions(termsResponse.data.data[0].attributes.text || "");
                }

                // Fetch case data
                const caseResponse = await axiosRef.current.get(`/api/ai-cases/${AiCaseId}`);
                setCaseData(caseResponse.data?.data);

                // Load existing discomfort data if available
                if (caseResponse.data?.data?.attributes?.discomfort) {
                    setDiscomfort(caseResponse.data.data.attributes.discomfort);
                    setHasDiscomfort(!caseResponse.data.data.attributes.discomfort.isDiscomfortChecked);
                }

                // Load existing work setup data if available
                if (caseResponse.data?.data?.attributes?.workSetup) {
                    setWorkSetup(caseResponse.data.data.attributes.workSetup);
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                setQrError("Error fetching assessment data. Please try again.");
                setIsTokenValidationDialogOpen(true);
            } finally {
                setIsLoading(false);
            }
        };

        // Only fetch data when axios is configured and we have a caseId
        if (axiosRef.current && AiCaseId) {
            fetchData();
        }
    }, [AiCaseId, token]);

    // Debug effect to monitor upload success states
    //    i useEffect(() => {
    //         console.log('Upload Success States Changed:', {
    //             isChairUploadSuccess,
    //             isDeskUploadSuccess,
    //             isStandingUploadSuccess,
    //             isTermsAccepted,
    //             chairHeight: workSetup.chair.adjustableHeight,
    //             deskHeight: workSetup.sitStandDesk,
    //             isAnalyzing
    //         });
    //     }, [isChairUploadSuccess, isDeskUploadSuccess, isStandingUploadSuccess, isTermsAccepted, workSetup, isAnalyzng]);

    // Dialog close handler
    const handleTokenValidationDialogClose = () => {
        setIsTokenValidationDialogOpen(false);
        window.close();
    };

    // Confirmation dialog handlers
    const handleOpenConfirmationDialog = () => {
        setIsConfirmationDialogOpen(true);
    };

    const handleCloseConfirmationDialog = () => {
        setIsConfirmationDialogOpen(false);
    };

    const handleConfirmUpload = () => {
        handleCloseConfirmationDialog();
        handleUpload();
    };

    // Generic attach handler for all photo types
    const handleAttach = (section: string) => (event: React.MouseEvent<HTMLButtonElement>) => {
        setActiveMenu(section);
        setMenuAnchor(event.currentTarget);
    };

    // Close menu
    const handleMenuClose = () => {
        setMenuAnchor(null);
        setActiveMenu(null);
    };

    // Camera handler
    const handleCamera = (section: string) => () => {
        handleMenuClose();
        if (cameraRefs[section as keyof typeof cameraRefs]?.current) {
            cameraRefs[section as keyof typeof cameraRefs].current?.click();
        }
    };

    // Gallery handler
    const handleGallery = (section: string) => () => {
        handleMenuClose();
        if (galleryRefs[section as keyof typeof galleryRefs]?.current) {
            galleryRefs[section as keyof typeof galleryRefs].current?.click();
        }
    };

    // Modified upload image function with immediate feedback
    const uploadImageToField = async (
        imageFile: File,
        fieldName: string,
        section: SectionKey,
        setErrorMsg: React.Dispatch<React.SetStateAction<string>>
    ): Promise<boolean> => {
        if (!axiosRef.current) return false;

        try {
            // Show upload status immediately
            setIsUploadingSection(section);
            setUploadStageMessage('Uploading photo...');
            setShowUploadStatusOverlay(true);
            setUploadStatus('uploading');
            setUploadProgress(0);

            // Create a filename with case ID and field name
            const fileName = `aiCase_${AiCaseId}_${fieldName}`;

            // Create form data for upload
            const formData = new FormData();
            formData.append('files', imageFile, fileName);
            formData.append('refId', AiCaseId as string);
            formData.append('ref', 'api::ai-case.ai-case');
            formData.append('field', fieldName);

            // Upload the image using our configured axios instance with progress tracking
            await axiosRef.current.post(`/api/upload`, formData, {
                onUploadProgress: (progressEvent) => {
                    const percentCompleted = Math.round(
                        (progressEvent.loaded * 100) / (progressEvent.total || 100)
                    );
                    setUploadProgress(percentCompleted);
                }
            });

            console.log(`✅ Image upload successful for ${section} (${fieldName})`);

            // Update message after upload
            setUploadStageMessage('Photo uploaded successfully');

            // Hide upload overlay after a brief delay
            setTimeout(() => {
                setShowUploadStatusOverlay(false);
                setIsUploadingSection(null);
            }, 1500);

            return true;
        } catch (error) {
            console.log(`❌ Image upload failed for ${section} (${fieldName}):`, error);
            setErrorMsg(`Failed to upload ${fieldName} image`);
            setShowUploadStatusOverlay(false);
            setIsUploadingSection(null);
            return false;
        }
    };

    const callAiAnalysisApi = async (
        imageFile: File,
        endpoint: string,
        setErrorMsg: React.Dispatch<React.SetStateAction<string>>
    ): Promise<AIAnalysisResponse | null> => {
        try {
            // Create form data for the AI API call
            const formData = new FormData();
            formData.append('token', token || '');
            formData.append('image', imageFile);

            // console.log(`🔍 Calling AI analysis API: ${AI_API_BASE_URL}${endpoint}`);

            // Make the API call
            const response = await axios.post(`${AI_API_BASE_URL}${endpoint}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            // console.log(`✅ AI analysis successful, analysis_id: ${response.data.analysis_id}`);

            // Return the analysis ID from the response
            return response.data as AIAnalysisResponse;
        } catch (error) {
            // console.log(`❌ AI analysis failed (${endpoint}):`, error);
            setErrorMsg(`Failed to analyze image. Please try again.`);
            return null;
        }
    };

    // Update AI case with analysis ID
    const updateCaseWithAnalysisId = async (
        analysisId: string,
        fieldName: string,
        setErrorMsg: React.Dispatch<React.SetStateAction<string>>
    ): Promise<boolean> => {
        if (!axiosRef.current) return false;

        try {
            // Create the update object based on the field name
            let updateData: any = {};

            if (fieldName === 'chairImage') {
                updateData = {
                    chairImageAnalysisId: analysisId,
                    chairImageProgress: ProgressStatus.PROCESSING
                };
            } else if (fieldName === 'chairDeskImage') {
                updateData = {
                    chairDeskImageAnalysisId: analysisId,
                    chairDeskImageProgress: ProgressStatus.PROCESSING
                };
            } else if (fieldName === 'standingImage') {
                updateData = {
                    standingImageAnalysisId: analysisId,
                    standingImageProgress: ProgressStatus.PROCESSING
                };
            }

            await axiosRef.current.put(`/api/ai-cases/${AiCaseId}`, {
                data: updateData
            });

            // console.log(`✅ Case updated with analysis ID for ${fieldName}: ${analysisId}`);

            return true;
        } catch (error) {
            // console.log(`❌ Failed to update case with ${fieldName} analysis ID:`, error);
            setErrorMsg(`Failed to update case with analysis information`);
            return false;
        }
    };

    const handleFileChange = (section: SectionKey) => async (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            // Get section-specific setters and values
            const setErrorMsg = getSectionErrorSetter(section);
            const setIsUploadSuccess = getSectionUploadSuccessSetter(section);
            const setIsConverting = getSectionConvertingSetter(section);
            const setProgress = getSectionProgressSetter(section);
            const imgType = getSectionImgType(section);
            const fieldName = getSectionFieldName(section);
            const endpoint = getSectionEndpoint(section);

            // console.log(`🚀 Starting upload process for ${section} (${fieldName})`);

            // Clear previous error message
            setErrorMsg('');

            let file = event.target.files[0];

            // Size validation
            if (file.size > 4900000) {
                setErrorMsg("Image size must not be larger than 5 MB");
                return;
            }

            // Convert HEIC to JPEG if needed
            if (file.type === 'image/heic' || file.type === 'image/heif' ||
                file.name.toLowerCase().endsWith('.heic') || file.name.toLowerCase().endsWith('.heif')) {
                try {
                    setIsConverting(true);
                    setUploadStageMessage('Converting image format...');

                    const heic2any = await import('heic2any').then(module => module.default);
                    const jpegBlob = await heic2any({
                        blob: file,
                        toType: 'image/jpeg',
                        quality: 0.8
                    });

                    const singleJpegBlob = Array.isArray(jpegBlob) ? jpegBlob[0] : jpegBlob;
                    file = new File(
                        [singleJpegBlob],
                        file.name.replace(/\.(heic|heif)$/i, '.jpg'),
                        { type: 'image/jpeg' }
                    );

                    setIsConverting(false);
                } catch (error) {
                    console.error("Error converting HEIC image:", error);
                    setErrorMsg("Error processing HEIC image. Please try another format.");
                    setIsConverting(false);
                    return;
                }
            }

            // Validate image resolution
            const url = URL.createObjectURL(file);
            const img = new Image();

            try {
                const isValidResolution = await new Promise<boolean>((resolve) => {
                    img.onload = () => {
                        URL.revokeObjectURL(url);

                        if (img.width < 512 || img.height < 512) {
                            setErrorMsg("Photo dimension must not be smaller than 512 x 512");
                            resolve(false);
                            return;
                        }

                        const aspectRatio = img.width / img.height;
                        if (!(aspectRatio >= 0.5 && aspectRatio <= 2)) {
                            setErrorMsg("Aspect ratio of photo must not be larger than 2 or lesser than 0.5");
                            resolve(false);
                            return;
                        }

                        resolve(true);
                    };

                    img.onerror = () => {
                        URL.revokeObjectURL(url);
                        resolve(false);
                    };

                    img.src = url;
                });

                if (!isValidResolution) {
                    return;
                }

                // Clear existing input field value
                if (event.target) {
                    event.target.value = '';
                }

                // Set the file in state
                setPhotoFiles(prev => ({
                    ...prev,
                    [section]: file
                }));

                // Begin upload process with immediate feedback
                // console.log(`📤 Starting upload for ${section}...`);

                setIsUploadSuccess(null); // In progress
                setProgress(ProgressStatus.UPLOADING);

                // 1. Upload image to server storage with immediate feedback
                const uploadSuccess = await uploadImageToField(
                    file,
                    fieldName,
                    section,
                    setErrorMsg
                );

                if (!uploadSuccess) {
                    // console.log(`❌ Upload failed for ${section}`);
                    setIsUploadSuccess(false);
                    setProgress(ProgressStatus.FAILED);
                    return;
                }

                // 2. Send to AI analysis API
                setUploadStageMessage('Preparing for analysis...');
                const analysisResponse = await callAiAnalysisApi(
                    file,
                    endpoint,
                    setErrorMsg
                );

                if (!analysisResponse) {
                    // console.log(`❌ AI analysis failed for ${section}`);
                    setIsUploadSuccess(false);
                    setProgress(ProgressStatus.FAILED);
                    return;
                }

                // 3. Update analysis ID in state
                if (section === 'sitting') {
                    setChairAnalysisId(analysisResponse.analysis_id);
                } else if (section === 'working') {
                    setDeskAnalysisId(analysisResponse.analysis_id);
                } else if (section === 'standing') {
                    setStandingAnalysisId(analysisResponse.analysis_id);
                }

                // 4. Update AI case with analysis ID
                const updateSuccess = await updateCaseWithAnalysisId(
                    analysisResponse.analysis_id,
                    fieldName,
                    setErrorMsg
                );

                if (!updateSuccess) {
                    // console.log(`❌ Case update failed for ${section}`);
                    setIsUploadSuccess(false);
                    setProgress(ProgressStatus.FAILED);
                    return;
                }

                // All steps successful - THIS IS THE CRITICAL PART
                // console.log(`✅ All upload steps completed successfully for ${section}`);
                setProgress(ProgressStatus.PROCESSING);
                setIsUploadSuccess(true); // THIS ENABLES THE SUBMIT BUTTON

            } catch (error) {
                console.error(`❌ Error processing ${section} image:`, error);
                setErrorMsg("Error processing image. Please try again.");
                setIsUploadSuccess(false);
                setProgress(ProgressStatus.FAILED);
                setShowUploadStatusOverlay(false);
                setIsUploadingSection(null);
            }
        }
    };

    // Get image URL for a section
    const getImageUrl = (section: string) => {
        const file = photoFiles[section as keyof typeof photoFiles];
        return file ? URL.createObjectURL(file) : undefined;
    };

    // Function to invalidate QR code - Only called after successful upload
    const invalidateQRCode = async (): Promise<boolean> => {
        if (!axiosRef.current) return false;

        try {
            // Then update the case to invalidate the QR code
            await axiosRef.current.put(`/api/ai-cases/${AiCaseId}`, {
                data: {
                    qrValid: false
                }
            });

            console.log('QR code invalidated');
            return true;
        } catch (error) {
            console.error('Error invalidating QR code:', error);
            return false;
        }
    };

    // Handle discomfort change
    const handleDiscomfortChange = (value: boolean) => {
        setHasDiscomfort(value);

        if (value) {
            // User selected "Yes" - open discomfort dialog
            setIsDiscomfortDialogOpen(true);

            // Initialize discomfort if needed
            if (discomfort.isDiscomfortChecked) {
                setDiscomfort({
                    isDiscomfortChecked: false,
                    selectedCondition: {},
                    reportGenerated: {}
                });
                setSelectedBodyParts([]);
            }
        } else {
            // User selected "No"
            setDiscomfort({
                isDiscomfortChecked: true
            });
            setSelectedBodyParts([]);
        }
    };

    // Handle edit preferences
    const handleEditPreferences = () => {
        setIsDiscomfortDialogOpen(true);
    };

    // Update discomfort from dialog
    const handleUpdateDiscomfort = (newDiscomfort: IDiscomfort) => {
        // console.log("Updating discomfort:", newDiscomfort);
        setDiscomfort(newDiscomfort);
    };

    const handleUpload = async () => {
        // Validate required photos
        if (!isChairUploadSuccess || !isDeskUploadSuccess) {
            alert('Please upload both required photos (sitting and working positions)');
            return;
        }

        // Validate terms acceptance
        if (!isTermsAccepted) {
            alert('Please accept the terms and conditions');
            return;
        }

        // Validate work setup questions
        if (workSetup.chair.adjustableHeight === undefined || workSetup.sitStandDesk === undefined) {
            alert('Please answer both work setup questions');
            return;
        }

        // Validate axios instance and case ID
        if (!AiCaseId || !axiosRef.current) {
            alert('System error. Please try refreshing the page.');
            return;
        }

        // Start the submission process
        setIsAnalyzing(true);
        setUploadStatus('uploading');
        setShowUploadStatusOverlay(true);
        setUploadProgress(0);
        setUploadStageMessage('Submitting your assessment...');

        try {
            // Simulate progress for better UX
            const progressInterval = setInterval(() => {
                setUploadProgress(prev => {
                    const newProgress = prev + 10;
                    return newProgress > 90 ? 90 : newProgress;
                });
            }, 500);

            // Prepare the data payload - ensure discomfort is properly formatted
            let finalDiscomfort = discomfort;

            // If user selected "No" for discomfort, ensure proper structure
            if (!hasDiscomfort) {
                finalDiscomfort = {
                    isDiscomfortChecked: true
                };
            }

            // If user selected "Yes" but discomfort dialog wasn't completed properly
            if (hasDiscomfort && (!discomfort || Object.keys(discomfort).length === 0)) {
                finalDiscomfort = {
                    isDiscomfortChecked: false,
                    selectedCondition: {},
                    reportGenerated: {}
                };
            }

            const updateData = {
                mobileWorkSetup: workSetup,
                discomfort: finalDiscomfort,
                triggerAiAssessment: true, // This signals the desktop that setup is complete
            };

            // console.log('Submitting data:', updateData); // Debug log

            // Store work setup data, discomfort data, and trigger AI assessment
            const response = await axiosRef.current.put(`/api/ai-cases/${AiCaseId}`, {
                data: updateData
            });

            // console.log('Update response:', response.data); // Debug log

            clearInterval(progressInterval);
            setUploadProgress(100);
            setUploadStageMessage('Data submitted successfully!');

            // Update upload status progression
            setTimeout(() => {
                setUploadStatus('receiving');
                setUploadStageMessage('Desktop is processing your photos...');

                setTimeout(() => {
                    setUploadStatus('processing');
                    setUploadStageMessage('Analysis in progress...');

                    setTimeout(() => {
                        setUploadStatus('complete');
                        setUploadStageMessage('Your photos are being analyzed on the desktop. You may close this window now.');

                        // Invalidate the QR code after successful completion
                        invalidateQRCode();
                    }, 3000);
                }, 2000);
            }, 1000);

        } catch (error) {
            console.error("Error in upload process:", error);

            // Reset states on error
            setIsAnalyzing(false);
            setShowUploadStatusOverlay(false);
            setUploadStageMessage('');

            // Show more specific error message
            let errorMessage = "Upload failed. Please try again.";
            alert(errorMessage);
        }
    };

    const handleCloseOverlay = () => {
        // Close the overlay and redirect to success page
        setShowUploadStatusOverlay(false);
        window.close();
    };

    // Function to determine if a section is converting
    const isSectionConverting = (section: SectionKey): boolean => {
        switch (section) {
            case 'sitting': return isChairConverting;
            case 'working': return isDeskConverting;
            case 'standing': return isStandingConverting;
            default: return false;
        }
    };

    // CRITICAL: Fixed submit button validation logic
    const isSubmitDisabled = () => {
        const conditions = {
            chairUpload: isChairUploadSuccess !== true,
            deskUpload: isDeskUploadSuccess !== true,
            analyzing: isAnalyzing,
            terms: !isTermsAccepted,
            chairHeight: workSetup.chair.adjustableHeight === undefined,
            deskHeight: workSetup.sitStandDesk === undefined
        };

        const isDisabled = Object.values(conditions).some(Boolean);

        // console.log('🔍 Submit Button Validation:', {
        //     isChairUploadSuccess: isChairUploadSuccess,
        //     isDeskUploadSuccess: isDeskUploadSuccess,
        //     isAnalyzing,
        //     isTermsAccepted,
        //     chairHeight: workSetup.chair.adjustableHeight,
        //     deskHeight: workSetup.sitStandDesk,
        //     conditions,
        //     finalDecision: isDisabled ? '🔒 DISABLED' : '✅ ENABLED'
        // });

        return isDisabled;
    };

    if (isLoading) {
        return (
            <Stack
                sx={{
                    minHeight: '100vh',
                    width: '100%',
                    maxWidth: '500px',
                    margin: '0 auto',
                    boxSizing: 'border-box',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}
            >
                <Loading />
            </Stack>
        );
    }

    const renderUploadStatusOverlay = () => {
        if (!showUploadStatusOverlay) return null;

        return (
            <MobileUploadStatus
                status={uploadStatus}
                progress={uploadProgress}
                message={uploadStageMessage}
                onClose={uploadStatus === 'complete' || uploadStatus === 'processing' ? handleCloseOverlay : undefined}
            />
        );
    };

    return (
        <Stack
            sx={{
                minHeight: '100vh',
                width: '100%',
                maxWidth: '500px',
                margin: '0 auto',
                boxSizing: 'border-box',
            }}
            spacing={0}
        >
            {/* Upload Status Overlay */}
            {renderUploadStatusOverlay()}

            {/* Header with purple background */}
            <MLBanner backgroundColor="#E3DDFF" title='AI based assessment' />

            <Stack sx={{ p: '20px' }} spacing={2}>
                {/* QR Error Message if invalid */}
                {qrError && (
                    <Stack
                        sx={{
                            bgcolor: '#FFEBEE',
                            p: 2,
                            borderRadius: '10px',
                            border: '1px solid #EF5350'
                        }}
                    >
                        <MLTypography color="error" fontWeight={500}>
                            {qrError}
                        </MLTypography>
                    </Stack>
                )}

                <MLTypography variant="h1" sx={{ fontSize: '24px', fontWeight: 700, mb: '40px', lineHeight: '120%' }}>
                    Provide 3 photos of your posture and setup:
                </MLTypography>

                {/* Render photo sections using the reusable component */}
                {photoSections.map(section => (
                    <PhotoSection
                        key={section.id}
                        title={section.title}
                        subtitle={section.subtitle}
                        instructions={photoInstructions[section.id as keyof typeof photoInstructions]}
                        samplePhotoUrl={samplePhotoUrls[section.id as keyof typeof samplePhotoUrls]}
                        imageFile={photoFiles[section.id as keyof typeof photoFiles]}
                        getImageUrl={() => getImageUrl(section.id)}
                        onAttachClick={handleAttach(section.id)}
                        isExpanded={expandedSections[section.id as keyof typeof expandedSections]}
                        onToggle={() => toggleSection(section.id)}
                        isAnalyzing={isAnalyzing || isSectionConverting(section.id)}
                        hasImage={Boolean(photoFiles[section.id as keyof typeof photoFiles])}
                    />
                ))}

                {/* Discomfort Question */}
                <Box sx={{ display: 'flex', flexDirection: 'column', }} >
                    <MLTypography variant="h2" sx={{ fontSize: '24px', fontWeight: 700, mb: 1, }}>
                        Do you experience any discomfort in your body?
                    </MLTypography>

                    <Stack flexDirection={'row'} gap={4} alignItems={'flex-start'} justifyContent={'flex-start'}>
                        <MLToggleButtonGroup
                            value={hasDiscomfort}
                            exclusive
                            onChange={() => handleDiscomfortChange(!hasDiscomfort)}
                            sx={{ mt: 1.5 }}
                        >
                            <MLToggleButton value={true}>Yes</MLToggleButton>
                            <MLToggleButton value={false}>No</MLToggleButton>
                        </MLToggleButtonGroup>

                        {hasDiscomfort && (
                            <Box sx={{
                                display: 'flex',
                                justifyContent: 'flex-start',
                                alignItems: 'center',
                                mt: 1
                            }}>
                                <Editpeference />
                                <MLButton sx={{
                                    textTransform: 'capitalize'
                                }} onClick={handleEditPreferences}>
                                    Edit preferences
                                </MLButton>
                            </Box>
                        )}
                    </Stack>

                    {/* Discomfort Selection Dialog */}
                    <DiscomfortSelector
                        open={isDiscomfortDialogOpen}
                        onClose={() => setIsDiscomfortDialogOpen(false)}
                        discomfort={discomfort}
                        setDiscomfort={handleUpdateDiscomfort}
                        bodyPartOptions={bodyParts}
                        selectedBodyParts={selectedBodyParts}
                        setSelectedBodyParts={setSelectedBodyParts}
                        axiosInstance={axiosRef.current}
                    />
                </Box>

                {/* Work Setup Questions */}
                <Box sx={{ mt: 2 }}>
                    <MLTypography variant="h2" sx={{ fontSize: '24px', fontWeight: 700, mb: 2 }}>
                        Your work setup question
                    </MLTypography>

                    {/* Chair Height Question */}
                    <Box sx={{ mb: 2 }}>
                        <MLTypography variant="body1" sx={{ mb: 1 }}>
                            Is your chair height adjustable?
                        </MLTypography>
                        <MLToggleButtonGroup
                            value={workSetup.chair.adjustableHeight}
                            exclusive
                            onChange={(event, value) => {
                                if (value !== null) {
                                    setWorkSetup({
                                        ...workSetup,
                                        chair: {
                                            ...workSetup.chair,
                                            adjustableHeight: value
                                        }
                                    });
                                }
                            }}
                            sx={{ mb: 2 }}
                        >
                            <MLToggleButton value={true}>Yes</MLToggleButton>
                            <MLToggleButton value={false}>No</MLToggleButton>
                        </MLToggleButtonGroup>
                    </Box>

                    {/* Desk Height Question */}
                    <Box>
                        <MLTypography variant="body1" sx={{ mb: 1 }}>
                            Is your desk height adjustable?
                        </MLTypography>
                        <MLToggleButtonGroup
                            value={workSetup.sitStandDesk}
                            exclusive
                            onChange={(event, value) => {
                                if (value !== null) {
                                    setWorkSetup({
                                        ...workSetup,
                                        sitStandDesk: value
                                    });
                                }
                            }}
                        >
                            <MLToggleButton value={true}>Yes</MLToggleButton>
                            <MLToggleButton value={false}>No</MLToggleButton>
                        </MLToggleButtonGroup>
                    </Box>
                </Box>

                {/* Terms and Conditions */}
                <Stack
                    direction="row"
                    alignItems="center"
                    spacing={0.5}
                    sx={{
                        justifyContent: "center",
                        mt: 1
                    }}
                >
                    <MLCheckbox
                        name="termsAcceptCheckbox"
                        checked={isTermsAccepted}
                        onChange={() => setIsTermsAccepted(!isTermsAccepted)}
                        disabled={false} // Always enable checkbox
                    />

                    <MLTypography sx={{ fontSize: '14px' }}>
                        Accept
                    </MLTypography>
                    <MLButton
                        disableRipple
                        sx={{
                            minWidth: 'auto',
                            p: 0,
                            m: 0,
                            color: '#6a5acd',
                            textTransform: 'none',
                            fontSize: '14px',
                            fontWeight: 400,
                            textDecoration: 'underline',
                            '&:hover': {
                                backgroundColor: 'transparent',
                                textDecoration: 'underline'
                            }
                        }}
                        onClick={() => setIsTOCModalOpened(true)}
                    >
                        Terms and condition
                    </MLButton>
                </Stack>

                {/* Submit Button */}
                <Box sx={{ mt: 2, mb: 4 }}>
                    <MLButton
                        variant="contained"
                        fullWidth
                        endIcon={<ChevronRight />}
                        sx={{
                            backgroundColor: '#DFFF32',
                            color: 'black',
                            textTransform: 'uppercase',
                            fontWeight: 600,
                            py: 1.5,
                            borderRadius: '6px',
                            '&:hover': {
                                backgroundColor: '#CBEC2A'
                            },
                            '&.Mui-disabled': {
                                backgroundColor: '#f5f5f5',
                                color: '#9e9e9e'
                            }
                        }}
                        disabled={isSubmitDisabled()}
                        onClick={handleOpenConfirmationDialog}
                    >
                        Submit for posture analysis
                    </MLButton>

                    {/* Debug Panel - Shows real-time validation status */}
                    {/* <Box sx={{ mt: 1, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '11px' }}>
                        <div>Chair Upload: {isChairUploadSuccess === true ? '✅ SUCCESS' : isChairUploadSuccess === false ? '❌ FAILED' : '⏳ PENDING'} ({String(isChairUploadSuccess)})</div>
                        <div>Desk Upload: {isDeskUploadSuccess === true ? '✅ SUCCESS' : isDeskUploadSuccess === false ? '❌ FAILED' : '⏳ PENDING'} ({String(isDeskUploadSuccess)})</div>
                        <div>Terms: {isTermsAccepted ? '✅ ACCEPTED' : '❌ NOT ACCEPTED'}</div>
                        <div>Chair Height: {workSetup.chair.adjustableHeight !== undefined ? `✅ SET (${workSetup.chair.adjustableHeight})` : '❌ NOT SET'}</div>
                        <div>Desk Height: {workSetup.sitStandDesk !== undefined ? `✅ SET (${workSetup.sitStandDesk})` : '❌ NOT SET'}</div>
                        <div>Analyzing: {isAnalyzing ? '❌ IN PROGRESS' : '✅ READY'}</div>
                        <div> <strong>Submit Button: {isSubmitDisabled() ? '🔒 DISABLED' : '✅ ENABLED'}</strong></div>
                    </Box> */}
                </Box>

                {/* Terms and conditions dialog */}
                <Dialog
                    open={isTOCModalOpened}
                    onClose={() => setIsTOCModalOpened(false)}
                    maxWidth="lg"
                    fullWidth
                >
                    <DialogTitle>
                        <MLTypography fontSize="24px" variant="h1" fontWeight={500}>
                            Terms and Conditions
                        </MLTypography>
                    </DialogTitle>
                    <DialogContent>
                        <Markdown>
                            {aiTermsAndConditions}
                        </Markdown>
                    </DialogContent>
                    <DialogActions sx={{ p: 2 }}>
                        <MLButton
                            variant="contained"
                            color="primary"
                            onClick={() => setIsTOCModalOpened(false)}
                            sx={{
                                paddingX: "24px",
                                textTransform: 'uppercase',
                            }}
                        >
                            Close
                        </MLButton>
                    </DialogActions>
                </Dialog>

                {/* Confirmation Dialog */}
                <Dialog
                    open={isConfirmationDialogOpen}
                    onClose={handleCloseConfirmationDialog}
                >
                    <DialogTitle variant='h4'>Confirm Submission</DialogTitle>
                    <DialogContent>
                        <DialogContentText>
                            Are you sure you want to submit these photos for posture analysis?
                        </DialogContentText>
                    </DialogContent>
                    <DialogActions sx={{
                        mx: 2
                    }}>
                        <MLButton variant="contained" color="primary" onClick={handleConfirmUpload} autoFocus>
                            Confirm
                        </MLButton>

                        <MLButton variant="outlined" onClick={handleCloseConfirmationDialog}>
                            Cancel
                        </MLButton>
                    </DialogActions>
                </Dialog>

                {/* Token validation dialog */}
                <Dialog
                    maxWidth='lg'
                    open={isTokenValidationDialogOpen}
                >
                    <DialogTitle variant='h4' mb={0.5}>Invalid Upload Link</DialogTitle>
                    <DialogContent>
                        <DialogContentText color={'black'}>
                            <MLTypography fontSize={'16px'} mb={1} > The upload link you're using is no longer valid.</MLTypography>
                            <MLTypography fontSize={'16px'}>
                                Please scan again from the desktop.
                            </MLTypography>
                        </DialogContentText>
                    </DialogContent>
                    <DialogActions>
                        <MLButton variant="outlined" onClick={handleTokenValidationDialogClose} color="primary" autoFocus>
                            Close
                        </MLButton>
                    </DialogActions>
                </Dialog>

                {/* Unified Camera/Gallery Menu */}
                <CameraGalleryMenu
                    menuAnchor={menuAnchor}
                    open={Boolean(menuAnchor)}
                    onClose={handleMenuClose}
                    onCameraClick={handleCamera(activeMenu as SectionKey || 'sitting')}
                    onGalleryClick={handleGallery(activeMenu as SectionKey || 'sitting')}
                    isDisabled={isAnalyzing}
                />

                {/* Hidden inputs for camera and gallery - rendered dynamically for each section */}
                {photoSections.map(section => (
                    <React.Fragment key={`inputs-${section.id}`}>
                        <input
                            type="file"
                            accept="image/*"
                            capture="environment"
                            onChange={handleFileChange(section.id)}
                            style={{ display: 'none' }}
                            ref={cameraRefs[section.id as keyof typeof cameraRefs]}
                        />
                        <input
                            type="file"
                            accept="image/*"
                            onChange={handleFileChange(section.id)}
                            style={{ display: 'none' }}
                            ref={galleryRefs[section.id as keyof typeof galleryRefs]}
                        />
                    </React.Fragment>
                ))}
            </Stack>
        </Stack>
    );
};

export default MobilePhotoUpload;