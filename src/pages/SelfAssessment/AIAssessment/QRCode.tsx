import React, { useRef } from 'react';
import { QRCodeSVG } from 'qrcode.react';
import { Box, Stack, useTheme } from "@mui/material";
import Loading from '../../Loading/Loading';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import MLButton from '../../../components/ui/MLButton/MLButton';

interface QRCodeProps {
    url: string;
    isGenerating: boolean;
    isExpired: boolean;
    error: string | null;
    onTryAgain: () => void;
    onRegenerate: () => void;
    size?: number;
    qrCodeRef?: React.RefObject<HTMLDivElement>;
}

const QRCode: React.FC<QRCodeProps> = ({
    url,
    isGenerating,
    isExpired,
    error,
    onTryAgain,
    onRegenerate,
    size = 150,
    qrCodeRef
}) => {
    const theme = useTheme();

    if (isGenerating) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                <Loading />
            </Box>
        );
    }

    if (error) {
        return (
            <Stack alignItems="center" mt={2}>
                <ErrorOutlineIcon color="error" sx={{ fontSize: 50 }} />
                <MLTypography
                    variant="body2"
                    fontSize="12px"
                    align="center"
                    sx={{
                        mt: 1,
                        cursor: "pointer",
                        textDecoration: "underline",
                        color: theme.palette.primary.main
                    }}
                    onClick={onTryAgain}
                >
                    Try again
                </MLTypography>
            </Stack>
        );
    }

    if (isExpired) {
        return (
            <Stack
                sx={{ position: 'relative' }}
                alignItems="center"
            >
                <QRCodeSVG
                    value={url}
                    size={size}
                    level="M"
                    style={{ opacity: 0.1 }}
                />
                <Stack
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        p: 5.8,
                        borderRadius: 2
                    }}
                >
                    <MLButton
                        variant="contained"
                        onClick={onRegenerate}
                        sx={{
                            bgcolor: theme.palette.primary.main,
                            color: 'white',
                            '&:hover': {
                                bgcolor: theme.palette.primary.dark,
                            },
                        }}
                    >
                        Generate QR Code
                    </MLButton>
                </Stack>
            </Stack>
        );
    }

    if (url) {
        return (
            <Stack ref={qrCodeRef}>
                <QRCodeSVG
                    value={url}
                    size={size}
                    level="M"
                />
            </Stack>
        );
    }

    return <Loading />;
};

export default QRCode;