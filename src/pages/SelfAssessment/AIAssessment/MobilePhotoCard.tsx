import React from 'react';
import { Stack } from '@mui/material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../components/ui/MLButton/MLButton';

interface MobilePhotoCardProps {
    title: string;
    subtitle?: string; // Optional subtitle
    samplePhoto: string;
    imageFile?: File;
    imageUrl?: string;
    instructions: string[]; // Array of instructions instead of a single description
    onBrowseClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
    isAnalyzing: boolean;
    errorMessage?: string;
    inputRef: React.RefObject<HTMLInputElement>;
    onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const MobilePhotoCard: React.FC<MobilePhotoCardProps> = ({
    title,
    subtitle,
    samplePhoto,
    imageFile,
    imageUrl,
    instructions,
    onBrowseClick,
    isAnalyzing,
    errorMessage,
    inputRef,
    onFileChange
}) => {
    return (
        <Stack
            sx={{
                bgcolor: 'white',
                borderRadius: '10px',
                border: '0.5px solid #9C9C9C',
                overflow: 'hidden',
                padding: '30px'
            }}
        >
            <Stack spacing={0}>
                {/* Title */}
                <Stack sx={{ textAlign: 'center', pb: 2 }}>
                    <MLTypography
                        variant='h1'
                        sx={{
                            fontSize: '24px',
                            fontWeight: 'bold',
                            textAlign: 'center'
                        }}
                    >
                        {title}
                    </MLTypography>

                    {subtitle && (
                        <MLTypography
                            variant='h2'
                            sx={{
                                fontSize: '16px',
                                fontWeight: 'bold',
                                textAlign: 'center'
                            }}
                        >
                            {subtitle}
                        </MLTypography>
                    )}
                </Stack>

                {/* Image */}
                <Stack sx={{ position: 'relative', px: 2 }}>
                    {imageUrl ? (
                        <img
                            src={imageUrl}
                            alt="Uploaded"
                            style={{
                                width: '100%',
                                height: 'auto',
                                borderRadius: '8px'
                            }}
                        />
                    ) : (
                        <img
                            src={samplePhoto}
                            alt="Sample"
                            style={{
                                width: '100%',
                                height: 'auto',
                                borderRadius: '8px'
                            }}
                        />
                    )}

                    {!imageUrl && (
                        <MLTypography
                            sx={{
                                position: 'absolute',
                                bottom: '10px',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: '24px',
                                textTransform: 'uppercase',
                                textShadow: '1px 1px 3px rgba(0,0,0,0.7)'
                            }}
                        >
                            SAMPLE
                        </MLTypography>
                    )}
                </Stack>

                {/* Instructions */}
                <Stack sx={{ pt: 2, pb: 1, px: 2 }}>
                    <ul style={{ paddingLeft: '35px', margin: 0 }}>
                        {instructions.map((instruction, index) => (
                            <li key={index}>
                                <MLTypography
                                    sx={{
                                        fontSize: '14px',
                                        fontWeight: 400,
                                        mb: '2px'
                                    }}
                                >
                                    {instruction}
                                </MLTypography>
                            </li>
                        ))}
                    </ul>
                </Stack>

                {/* Error Message if any */}
                {errorMessage && (
                    <MLTypography
                        sx={{
                            fontSize: '14px',
                            px: 2,
                            color: 'red',
                            pb: '8px'
                        }}
                    >
                        {errorMessage}
                    </MLTypography>
                )}

                {/* Hidden input */}
                <input
                    type="file"
                    ref={inputRef}
                    onChange={onFileChange}
                    style={{ display: 'none' }}
                    accept="image/*"
                />

                {/* Button */}
                <Stack sx={{ p: 2, pt: 0 }}>
                    <MLButton
                        variant="outlined"
                        color="primary"
                        fullWidth
                        sx={{
                            borderRadius: '8px',
                            textTransform: 'uppercase',
                            height: '40px',
                            color: '#6a5acd',
                            borderColor: '#6a5acd'
                        }}
                        onClick={onBrowseClick}
                        disabled={isAnalyzing}
                    >
                        {imageFile ? "REUPLOAD" : "BROWSE TO ATTACH"}
                    </MLButton>
                </Stack>
            </Stack>
        </Stack>
    );
};

export default MobilePhotoCard;