import React, { useState, useEffect } from 'react';
import {
    Dialog,
    DialogContent,
    DialogActions,
    Box,
    Grid,
    FormControlLabel,
    Typography,
    Stack,
    IconButton,
    useTheme,
    useMediaQuery,
    CircularProgress
} from '@mui/material';
import { ChevronRight, CloseRounded } from '@mui/icons-material';
import { useList } from "@refinedev/core";
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../components/ui/MLButton/MLButton';
import MLCheckbox from '../../../components/ui/MLCheckbox/MLCheckbox';
import MLMultiSelecttag from '../../../components/ui/MLMultiSelecttag/MLSelecttag';

interface OptionType {
    label: string;
    value: number;
}

interface IDiscomfort {
    isDiscomfortChecked?: boolean;
    specialConsiderations?: Record<string, boolean>;
    selectedCondition?: Record<string, {
        isSelected: boolean;
        affectsBodyPart: string;
        affectsBodyPartText: string;
    }>;
    reportGenerated?: Record<string, string>;
    [key: string]: any;
}

interface DiscomfortSelectorProps {
    open: boolean;
    onClose: () => void;
    discomfort: IDiscomfort;
    setDiscomfort: (discomfort: IDiscomfort) => void;
    bodyPartOptions: any;
    selectedBodyParts: any;
    setSelectedBodyParts: (parts: OptionType[]) => void;
    // New props for direct API access
    axiosInstance?: any; // For token-based API calls in mobile
    conditionsData?: any; // Pre-fetched conditions data for mobile
}

interface ConditionItem {
    id: string;
    label: string;
    bodyPart?: string;
    bodyPartText?: string;
    category?: string;
    sortOrder?: number;
    isSpecialConsideration?: boolean;
}

const DiscomfortSelector: React.FC<DiscomfortSelectorProps> = ({
    open,
    onClose,
    discomfort,
    setDiscomfort,
    bodyPartOptions,
    selectedBodyParts,
    setSelectedBodyParts,
    axiosInstance,
    conditionsData: propConditionsData
}) => {
    const theme = useTheme();
    const isSmUp = useMediaQuery(theme.breakpoints.up('sm'));

    // Array to store processed medical conditions
    const [medicalConditions, setMedicalConditions] = useState<ConditionItem[]>([]);
    // Array to store processed special considerations
    const [specialConsiderations, setSpecialConsiderations] = useState<ConditionItem[]>([]);
    // Loading states
    const [isLoadingMedical, setIsLoadingMedical] = useState(true);
    const [isLoadingSpecial, setIsLoadingSpecial] = useState(true);

    // Local state to store conditions data from direct API call
    const [directFetchedConditions, setDirectFetchedConditions] = useState<any>(null);
    const [isDirectFetching, setIsDirectFetching] = useState(false);

    // Use either useList or direct fetch depending on props
    const { data: refineConditionsData, isLoading: isLoadingRefineConditions } = useList({
        resource: "self-assessment-conditions",
        pagination: { pageSize: 100 },
        meta: {
            populate: ['canAffectBodyParts', 'category']
        },
        queryOptions: {
            enabled: !axiosInstance && !propConditionsData // Only use useList if not using direct API
        }
    });

    // If axiosInstance is provided, fetch conditions directly
    useEffect(() => {
        const fetchConditionsDirectly = async () => {
            if (!axiosInstance || propConditionsData || directFetchedConditions || isDirectFetching) {
                return;
            }

            setIsDirectFetching(true);
            try {
                const response = await axiosInstance.get('/api/self-assessment-conditions', {
                    params: {
                        'pagination[pageSize]': 100,
                        populate: ['canAffectBodyParts', 'category']
                    }
                });

                setDirectFetchedConditions(response.data);
            } catch (error) {
                console.error("Error fetching conditions directly:", error);
            } finally {
                setIsDirectFetching(false);
            }
        };

        fetchConditionsDirectly();
    }, [axiosInstance, propConditionsData, directFetchedConditions, isDirectFetching]);

    // Determine which data source to use
    const effectiveConditionsData = propConditionsData || directFetchedConditions || refineConditionsData;

    // Process the conditions data when it's loaded
    useEffect(() => {
        if (effectiveConditionsData?.data) {
            try {
                // console.log("Raw conditions data:", effectiveConditionsData.data);

                const allConditions = effectiveConditionsData.data
                    .filter((condition: any) => {
                        // Only include conditions where showAsOption is true
                        if ('attributes' in condition && condition.attributes) {
                            return condition.attributes.showAsOption === true;
                        } else if ('showAsOption' in condition) {
                            return condition.showAsOption === true;
                        }
                        return false;
                    })
                    .map((condition: any) => {
                        let bodyPart = '';
                        let bodyPartText = '';
                        let conditionName = '';
                        let category = '';
                        let sortOrder = 0;
                        let isSpecialConsideration = false;

                        // Handle both data structures
                        if ('attributes' in condition && condition.attributes) {
                            // Handle attributes structure
                            conditionName = condition.attributes.condition;
                            sortOrder = condition.attributes.sortOrder || 0;

                            // Log the raw category data to debug
                            // console.log(`Condition: ${conditionName}, Raw category:`, condition.attributes.category);

                            // Check if this is marked as a special consideration
                            isSpecialConsideration = condition.attributes.isSpecialConsideration || false;

                            // Get category if available
                            if (condition.attributes.category) {
                                if (condition.attributes.category.data && condition.attributes.category.data.attributes) {
                                    category = condition.attributes.category.data.attributes.name || '';
                                } else if (typeof condition.attributes.category === 'object') {
                                    category = condition.attributes.category.name || '';
                                } else {
                                    category = String(condition.attributes.category);
                                }
                            }

                            // Get first body part if available
                            if (condition.attributes.canAffectBodyParts?.data &&
                                condition.attributes.canAffectBodyParts.data.length > 0) {
                                const firstBodyPart = condition.attributes.canAffectBodyParts.data[0];

                                if (firstBodyPart.attributes) {
                                    bodyPart = firstBodyPart.attributes.bodyPart;
                                    bodyPartText = firstBodyPart.attributes.bodyPartText;
                                }
                            }
                        } else {
                            // Handle direct structure
                            conditionName = condition.condition;
                            sortOrder = condition.sortOrder || 0;
                            isSpecialConsideration = condition.isSpecialConsideration || false;

                            // Log the raw category data to debug
                            // console.log(`Condition: ${conditionName}, Raw category:`, condition.category);

                            // Get category if available
                            if (condition.category) {
                                if (typeof condition.category === 'object') {
                                    category = condition.category.name || '';
                                } else {
                                    category = String(condition.category);
                                }
                            }

                            // Get first body part if available
                            if (condition.canAffectBodyParts && condition.canAffectBodyParts.length > 0) {
                                const firstBodyPart = condition.canAffectBodyParts[0];
                                bodyPart = firstBodyPart.bodyPart;
                                bodyPartText = firstBodyPart.bodyPartText;
                            }
                        }

                        return {
                            id: `condition-${condition.id}`,
                            label: conditionName,
                            bodyPart,
                            bodyPartText,
                            category,
                            sortOrder,
                            isSpecialConsideration
                        };
                    });

                // Log the processed conditions to help debug
                // console.log("Processed conditions:", allConditions);

                // Determine special considerations based on multiple criteria
                const specialItems = allConditions
                    .filter((item: any) => {
                        // Check explicit isSpecialConsideration flag first
                        if (item.isSpecialConsideration === true) {
                            return true;
                        }

                        // Then check category name (case insensitive)
                        if (item.category) {
                            const lowerCaseCategory = item.category.toLowerCase();
                            return lowerCaseCategory.includes('special') ||
                                lowerCaseCategory === 'consideration' ||
                                lowerCaseCategory === 'considerations';
                        }

                        return false;
                    })
                    // Sort by sortOrder if available
                    .sort((a: any, b: any) => (a.sortOrder || 0) - (b.sortOrder || 0));

                // console.log("Special items:", specialItems);

                // Set special considerations
                setSpecialConsiderations(specialItems);
                setIsLoadingSpecial(false);

                // Track processed items to avoid duplicates
                const specialItemIds = new Set(specialItems.map((item: any) => item.id));

                // Process remaining items as medical conditions
                const medicalItems = allConditions
                    .filter((item: any) => !specialItemIds.has(item.id))
                    .sort((a: any, b: any) => (a.sortOrder || 0) - (b.sortOrder || 0));

                // console.log("Medical items:", medicalItems);

                setMedicalConditions(medicalItems);
                setIsLoadingMedical(false);
            } catch (error) {
                console.error("Error processing conditions:", error);

                // No fallbacks - just show empty arrays if there's an error
                setSpecialConsiderations([]);
                setIsLoadingSpecial(false);
                setMedicalConditions([]);
                setIsLoadingMedical(false);
            }
        }
    }, [effectiveConditionsData]);

    const handleSpecialConsiderationChange = (id: string, checked: boolean) => {
        setDiscomfort((prev: any) => {
            const special = { ...(prev.specialConsiderations || {}) };
            if (checked) {
                special[id] = true;
            } else {
                delete special[id];
            }
            return { ...prev, specialConsiderations: special };
        });
    };

    const handleMedicalConditionChange = (
        id: string,
        bodyPart: string,
        bodyPartText: string,
        checked: boolean
    ) => {
        setDiscomfort((prev: any) => {
            const selected = { ...(prev.selectedCondition || {}) };
            if (checked) {
                selected[id] = { isSelected: true, affectsBodyPart: bodyPart, affectsBodyPartText: bodyPartText };
            } else {
                delete selected[id];
            }
            return { ...prev, selectedCondition: selected };
        });
    };

    // Handle body part selection via MultiSelecttag
    const handleSelectedBodyDiscomfort = (selectedValues: OptionType[]) => {
        setSelectedBodyParts(selectedValues);

        // Update reportGenerated in discomfort object to be compatible with existing functionality
        setDiscomfort((prev: any) => {
            const report = { ...(prev.reportGenerated || {}) };

            // Clear previous selections
            Object.keys(report).forEach(key => {
                if (key.endsWith('-low')) {
                    delete report[key];
                }
            });

            // Add new selections
            selectedValues.forEach(item => {
                const key = `${item.label.toLowerCase().replace(/\s+/g, '-')}-low`;
                report[key] = '';
            });

            return { ...prev, reportGenerated: report };
        });
    };

    return (
        <Dialog
            open={open}
            maxWidth="md"
            onClose={onClose}
            PaperProps={{
                sx: {
                    width: { xs: '380px', sm: '90%', md: '90%' },
                    borderRadius: '8px',
                    m: 0,
                    position: 'relative'
                }
            }}
        >
            {/* Mobile Close Icon */}
            <Stack display={{ xs: 'flex', md: 'none' }}>
                <IconButton
                    onClick={onClose}
                    sx={{ position: 'fixed', right: { xs: 25, sm: 46 }, top: { xs: 32, sm: 60 }, zIndex: 1, color: '#888' }}
                    aria-label="Close"
                >
                    <CloseRounded color="primary" />
                </IconButton>
            </Stack>

            <DialogContent sx={{ p: '24px 24px 20px' }}>
                {/* Discomfort Multiselect - Replacing Diagrams */}
                <Box sx={{ mb: 3.5, mt: 2 }}>
                    <Box sx={{ width: "100%" }}>
                        <MLTypography variant="h2" sx={{ fontSize: 24, fontWeight: 700, }}>
                            Discomfort
                        </MLTypography>
                        <MLMultiSelecttag
                            value={selectedBodyParts}
                            heading={''}
                            placeholder="Select body discomfort"
                            options={bodyPartOptions}
                            onChange={handleSelectedBodyDiscomfort}
                            labelKey="label"
                            valueKey="value"
                        />
                    </Box>
                </Box>

                {/* Special Consideration */}
                {specialConsiderations.length > 0 && (
                    <Box sx={{ mb: 3 }}>
                        <MLTypography variant="h2" sx={{ fontSize: 24, fontWeight: 700, mb: 2 }}>
                            Special Consideration
                        </MLTypography>

                        {isLoadingSpecial ? (
                            <Box display="flex" justifyContent="center" p={2}>
                                <CircularProgress size={24} />
                            </Box>
                        ) : (
                            <Grid container spacing={1}>
                                {specialConsiderations.map(item => (
                                    <Grid item xs={6} sm={4} lg={4} key={item.id}>
                                        <FormControlLabel
                                            control={
                                                <MLCheckbox
                                                    name='Special Consideration'
                                                    checked={!!discomfort.specialConsiderations?.[item.id]}
                                                    onChange={e => handleSpecialConsiderationChange(item.id, e.target.checked)}
                                                />
                                            }
                                            label={
                                                <Typography sx={{ fontSize: 14, whiteSpace: 'pre-line', lineHeight: 1.2 }}>
                                                    {item.label}
                                                </Typography>
                                            }
                                            sx={{ m: 0 }}
                                        />
                                    </Grid>
                                ))}
                            </Grid>
                        )}
                    </Box>
                )}

                {/* Medical Condition */}
                {medicalConditions.length > 0 && (
                    <Box sx={{ mb: 3 }}>
                        <MLTypography variant="h2" sx={{ fontSize: 24, fontWeight: 700, mb: 2 }}>
                            Medical Condition
                        </MLTypography>

                        {isLoadingMedical ? (
                            <Box display="flex" justifyContent="center" p={2}>
                                <CircularProgress size={24} />
                            </Box>
                        ) : (
                            <Grid container spacing={1}>
                                {medicalConditions.map(item => (
                                    <Grid item xs={6} sm={4} lg={4} key={item.id}>
                                        <FormControlLabel
                                            control={
                                                <MLCheckbox
                                                    name='Medical Condition'
                                                    checked={!!discomfort.selectedCondition?.[item.id]?.isSelected}
                                                    onChange={e =>
                                                        handleMedicalConditionChange(
                                                            item.id,
                                                            item.bodyPart || '',
                                                            item.bodyPartText || '',
                                                            e.target.checked
                                                        )
                                                    }
                                                />
                                            }
                                            label={
                                                <MLTypography sx={{ fontSize: 14, whiteSpace: 'pre-line', lineHeight: 1.2 }}>
                                                    {item.label}
                                                </MLTypography>
                                            }
                                            sx={{ m: 0 }}
                                        />
                                    </Grid>
                                ))}
                            </Grid>
                        )}
                    </Box>
                )}

                {/* Show message when no conditions are available */}
                {!isLoadingMedical && !isLoadingSpecial &&
                    medicalConditions.length === 0 && specialConsiderations.length === 0 && (
                        <Box sx={{ my: 3, textAlign: 'center' }}>
                            <MLTypography variant="body1" color="text.secondary">
                                No medical conditions or special considerations available.
                            </MLTypography>
                        </Box>
                    )}
            </DialogContent>

            <DialogActions sx={{ px: 3, pb: 3, pt: 0, justifyContent: 'center' }}>
                {/* Mobile-only SUBMIT */}
                <Box display={{ xs: 'block', sm: 'none' }} width="100%">
                    <MLButton
                        fullWidth
                        variant="contained"
                        endIcon={<ChevronRight />}
                        onClick={onClose}
                    >
                        CONTINUE
                    </MLButton>
                </Box>

                {/* Tablet & Desktop CONTINUE */}
                <Box display={{ xs: 'none', sm: 'block' }}>
                    <MLButton
                        variant="contained"
                        onClick={onClose}
                        sx={{ minWidth: 200, textTransform: 'none' }}
                    >
                        CONTINUE
                    </MLButton>
                </Box>
            </DialogActions>
        </Dialog>
    );
};

export default DiscomfortSelector;