// These utility functions can be placed in a separate file for reuse

import heic2any from 'heic2any';

/**
 * Validates an image file for dimensions, aspect ratio, and size
 * Reusable function that encapsulates the validation logic from UploadPhoto
 */
export const validateImage = async (file: File): Promise<{ isValid: boolean, errorMsg: string }> => {
    // Check file size (5MB limit)
    if (file.size > 4900000) {
        return { isValid: false, errorMsg: "Image size must not be larger than 5 MB" };
    }

    // Check image resolution and aspect ratio
    const url = URL.createObjectURL(file);
    const img = new Image();
    let errorMsg = "";

    try {
        const isValidResolution = await new Promise<boolean>((resolve) => {
            img.onload = () => {
                URL.revokeObjectURL(url);

                // Check minimum dimensions
                if (img.width < 512 || img.height < 512) {
                    errorMsg = "Photo dimension must not be smaller than 512 x 512";
                    return resolve(false);
                }

                // Check aspect ratio
                const aspectRatio = img.width / img.height;
                if (!(aspectRatio >= 0.5 && aspectRatio <= 2)) {
                    errorMsg = "Aspect ratio of photo must not be larger than 2 or lesser than 0.5";
                    return resolve(false);
                }

                resolve(true);
            };

            img.onerror = () => {
                URL.revokeObjectURL(url);
                errorMsg = "Failed to load image";
                resolve(false);
            };

            img.src = url;
        });

        return { isValid: isValidResolution, errorMsg };
    } catch (error) {
        console.error("Error validating image", error);
        return { isValid: false, errorMsg: "Error validating image" };
    }
};

/**
 * Converts HEIC/HEIF images to JPEG format
 * Extracted from the UploadPhoto component for reuse
 */
export const convertHeicToJpeg = async (file: File): Promise<{ converted: boolean, file: File, error?: string }> => {
    // Check if file is HEIC/HEIF format
    const isHeic = file.type === 'image/heic' ||
        file.type === 'image/heif' ||
        file.name.toLowerCase().endsWith('.heic') ||
        file.name.toLowerCase().endsWith('.heif');

    if (!isHeic) {
        return { converted: false, file };
    }

    try {
        const jpegBlob = await heic2any({
            blob: file,
            toType: 'image/jpeg',
            quality: 0.8
        });

        const singleJpegBlob = Array.isArray(jpegBlob) ? jpegBlob[0] : jpegBlob;

        const jpegFile = new File(
            [singleJpegBlob],
            file.name.replace(/\.(heic|heif)$/i, '.jpg'),
            { type: 'image/jpeg' }
        );

        return { converted: true, file: jpegFile };
    } catch (error) {
        console.error("Error converting HEIC to JPEG", error);
        return { converted: false, file, error: "Failed to convert image format" };
    }
};

/**
 * Processes an image file - converts if needed and validates
 * Combines both operations from UploadPhoto for reuse
 */
export const processImageFile = async (
    file: File,
    setImage: (file: File) => void,
    setErrorMsg: (msg: string) => void,
    setIsConverting?: (isConverting: boolean) => void
): Promise<boolean> => {
    if (setIsConverting) setIsConverting(true);

    try {
        // Step 1: Convert HEIC/HEIF if needed
        const { converted, file: processedFile, error: conversionError } = await convertHeicToJpeg(file);

        if (conversionError) {
            setErrorMsg(conversionError);
            return false;
        }

        // Step 2: Validate the image
        const { isValid, errorMsg } = await validateImage(processedFile);

        if (isValid) {
            setErrorMsg("");
            setImage(processedFile);
            return true;
        } else {
            setErrorMsg(errorMsg);
            return false;
        }
    } catch (error) {
        console.error("Error processing image", error);
        setErrorMsg("An unexpected error occurred processing the image");
        return false;
    } finally {
        if (setIsConverting) setIsConverting(false);
    }
};