import { Box, Stack } from "@mui/material";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import UploadIconSvg from "../../../assets/icons/upload";
import MLButton from "../../../components/ui/MLButton/MLButton";

// Photo section type definition
interface PhotoSectionProps {
    title: string;
    subtitle: string;
    instructions: string[];
    samplePhotoUrl: string;
    imageFile?: File;
    getImageUrl: () => string | undefined;
    onAttachClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
    isExpanded: boolean;
    onToggle: () => void;
    isAnalyzing: boolean;
    hasImage: boolean;
}

// Reusable photo section component
export const PhotoSection: React.FC<PhotoSectionProps> = ({
    title,
    subtitle,
    instructions,
    samplePhotoUrl,
    imageFile,
    getImageUrl,
    onAttachClick,
    isExpanded,
    onToggle,
    isAnalyzing,
    hasImage
}) => {
    return (
        <Box
            sx={{
                border: '1px solid #E0E0E0',
                borderRadius: '8px',
                overflow: 'hidden',
            }}
        >
            <Box
                sx={{
                    p: 2,
                    cursor: 'pointer',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}
                onClick={onToggle}
            >
                <Box>
                    <MLTypography variant="h2" sx={{ fontSize: '22px', fontWeight: 700 }}>
                        {title}
                    </MLTypography>
                    <MLTypography mx={0.5} variant="h2" sx={{ fontSize: '18px', fontWeight: 600 }}>
                        {subtitle}
                    </MLTypography>
                </Box>
                <UploadIconSvg />
            </Box>

            {(isExpanded || hasImage) && (
                <Box sx={{ px: 2, pb: 2 }}>
                    {/* Image container with relative positioning for both cases */}
                    <Box
                        sx={{
                            position: 'relative',
                            width: '100%',
                            mb: 2
                        }}
                    >
                        {/* Conditionally render either uploaded image or sample image */}
                        <Box
                            component="img"
                            src={hasImage ? getImageUrl() : samplePhotoUrl}
                            alt={hasImage ? `${title} posture` : `Sample ${title.toLowerCase()} posture`}
                            sx={{
                                width: '100%',
                                borderRadius: '8px',
                                height: 'auto',
                            }}
                        />

                        {/* Status indicator - changes based on whether image is uploaded */}
                        {hasImage ? (
                            <Box
                                sx={{
                                    position: 'absolute',
                                    bottom: "4%",
                                    right: "5%",
                                    borderRadius: "5px",
                                    backgroundColor: "#31C100"
                                }}
                            >
                                <MLTypography
                                    sx={{
                                        color: '#FFFFFF',
                                        padding: '5px 10px',
                                        fontWeight: 'bold',
                                        fontSize: '14px',
                                        userSelect: 'none',
                                        pointerEvents: 'none',
                                        textTransform: 'uppercase',
                                        letterSpacing: '1px',
                                        borderRadius: '4px',
                                    }}
                                >
                                    ✔ Uploaded
                                </MLTypography>
                            </Box>
                        ) : (
                            <Box
                                sx={{
                                    position: 'absolute',
                                    bottom: "4%",
                                    right: "5%",
                                    borderRadius: "5px",
                                    backgroundColor: "#FF6E6E"
                                }}
                            >
                                <MLTypography
                                    sx={{
                                        color: '#FFFFFF',
                                        padding: '5px 10px',
                                        fontWeight: 'bold',
                                        fontSize: '14px',
                                        userSelect: 'none',
                                        pointerEvents: 'none',
                                        textTransform: 'uppercase',
                                        letterSpacing: '1px',
                                        borderRadius: '4px',
                                    }}
                                >
                                    Sample Photo
                                </MLTypography>
                            </Box>
                        )}
                    </Box>

                    {/* Instructions */}
                    <Stack spacing={1} sx={{ mb: 2 }}>
                        {instructions.map((instruction, idx) => (
                            <Box key={idx} sx={{ display: 'flex', alignItems: 'flex-start' }}>
                                <Box
                                    sx={{
                                        mr: 1,
                                        color: 'black',
                                        fontSize: '20px',
                                        lineHeight: '1.5',
                                    }}
                                >
                                    •
                                </Box>
                                <MLTypography sx={{
                                    lineHeight: '1.5',
                                    mt: '3px', // Adjust as needed for perfect alignment,
                                    fontFamily: 'work sans',
                                    fontWeight: 400
                                }}>
                                    {instruction}
                                </MLTypography>
                            </Box>
                        ))}
                    </Stack>

                    {/* Browse button */}
                    <MLButton
                        variant="outlined"
                        sx={{
                            textTransform: 'none',
                            borderColor: '#A78BFA',
                            color: '#A78BFA',
                            width: '100%',
                            my: '10px'
                        }}
                        onClick={onAttachClick}
                        disabled={isAnalyzing}
                    >
                        {hasImage ? 'REUPLOAD' : 'BROWSE TO ATTACH'}
                    </MLButton>
                </Box>
            )}
        </Box>
    );
};