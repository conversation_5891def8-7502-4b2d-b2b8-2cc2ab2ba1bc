import { Box, Stack } from "@mui/material";
import MLTypography from "../../../../components/ui/MLTypography/MLTypography";
import MLButton from "../../../../components/ui/MLButton/MLButton";
import { ImgType } from "../AIAssessment";
import Loading from "../../../Loading/Loading";
import { useState } from "react";
import heic2any from 'heic2any';
import { AI_API_BASE_URL, API_URL, CHAIR_ANALYSIS_ENDPOINT, DESK_ANALYSIS_ENDPOINT, STANDING_DESK_ANALYSIS_ENDPOINT } from "../../../../constants";
import axios from 'axios';

interface UploadPhotoProps {
  inputRef: React.RefObject<HTMLInputElement>;
  imgType: ImgType;
  imgUrl: string | undefined;
  imageFile: File | undefined;
  imgErrMsg: string;
  samplePhoto: string;
  setImageErrMsg: (msg: string) => void;
  setImage: (file: File) => void;
  token: string | undefined;
  aiCaseId: number | undefined;
  setIsUploadSuccess: (isSuccess: boolean) => void;
  setAiErrorMsg: (msg: string) => void;
  setIsAiErrorModalOpened: (isOpened: boolean) => void;
  setIsUploading?: (isUploading: boolean) => void;
}
const UploadPhoto = ({
  inputRef,
  imgType,
  imgUrl,
  imageFile,
  imgErrMsg,
  samplePhoto,
  setImageErrMsg,
  setImage,
  token,
  aiCaseId,
  setIsUploadSuccess,
  setAiErrorMsg,
  setIsAiErrorModalOpened,
  setIsUploading,
}: UploadPhotoProps) => {
  const [isConverting, setIsConverting] = useState<boolean>(false);

  const handleUploadPhoto = async (photoFile: File | undefined, aiCaseId: number | undefined, photoType: ImgType): Promise<boolean> => {
    if (!photoFile) return false;
    if (!aiCaseId) return false;

    const authToken = localStorage.getItem('strapi-jwt-token');
    // choosing endpoint and fields by photoType
    let apiEndpoint;
    let analysisIdField;
    let fieldPhotoType;

    switch (photoType) {
      case "chair_only":
        apiEndpoint = CHAIR_ANALYSIS_ENDPOINT;
        break;
      case "desk_chair":
        apiEndpoint = DESK_ANALYSIS_ENDPOINT;
        break;
      case "standing_desk":
        apiEndpoint = STANDING_DESK_ANALYSIS_ENDPOINT;
        break;
    }

    switch (photoType) {
      case "chair_only":
        analysisIdField = "chairImageAnalysisId";
        break;
      case "desk_chair":
        analysisIdField = "chairDeskImageAnalysisId";
        break;
      case "standing_desk":
        analysisIdField = "standingDeskImageAnalysisId";
        break;
    }

    switch (photoType) {
      case "chair_only":
        fieldPhotoType = "chairImage";
        break;
      case "desk_chair":
        fieldPhotoType = "chairDeskImage";
        break;
      case "standing_desk":
        fieldPhotoType = "standingImage";
        break;
    }

    // ai backend healthcheck before uploading
    try {
      const aiBackendHealthStatus = await axios.get(`${AI_API_BASE_URL}/health`, { timeout: 30000 });
      if (aiBackendHealthStatus.status !== 200) {
        throw new Error("AI service not responding.");
      }
    } catch (e) {
      console.log("ai healthcheck error: ", e);
      setAiErrorMsg("Our AI services are currently overloaded. Please try again");
      setIsAiErrorModalOpened(true);
      return false;
    }

    // uploading image to ai backend
    let analyzeRes: any;
    try {
      const ergonomicAnalyzeFormData = new FormData();
      ergonomicAnalyzeFormData.append("image", photoFile);
      ergonomicAnalyzeFormData.append("token", token ?? "");

      analyzeRes = await axios.post(`${AI_API_BASE_URL}${apiEndpoint}`, ergonomicAnalyzeFormData);
      setIsUploadSuccess(analyzeRes.status === 200 ? true : false)
    } catch (e) {
      console.log("upload to ai analyze error: ", e);
      setIsUploadSuccess(false);
      return false;
    }

    // update analysis id response given by ai backend to strapi
    try {
      const updateAiCase = await axios.put(`${API_URL}/api/ai-cases/${aiCaseId}`, {
        data: {
          [analysisIdField]: analyzeRes.data.analysis_id
        }
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      if (updateAiCase.status !== 200) {
        throw new Error("Updating analysis id to strapi failed.");
      }
    } catch (e) {
      console.log("update analysis id to ai case error: ", e);
      setAiErrorMsg("Our Backend services are currently overloaded. Please try again");
      setIsAiErrorModalOpened(true);
      setIsUploadSuccess(false);
      return false;
    }

    // upload image ai case at strapi backend
    try {
      const aiCaseUploadFormData = new FormData();
      aiCaseUploadFormData.append("files", photoFile, `aiCase_${aiCaseId}_${photoType}_image`);
      aiCaseUploadFormData.append("refId", aiCaseId.toString());
      aiCaseUploadFormData.append("ref", "api::ai-case.ai-case");
      aiCaseUploadFormData.append("field", fieldPhotoType);

      const uploadImageAiCase = await axios.post(`${API_URL}/api/upload`, aiCaseUploadFormData, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      if (uploadImageAiCase.status !== 200) {
        throw new Error("Uploading image to strapi failed.");
      }
    } catch (e) {
      console.log("upload image to strapi error: ", e);
      setAiErrorMsg("Our Backend services are currently overloaded. Please try again");
      setIsAiErrorModalOpened(true);
      setIsUploadSuccess(false);
      return false;
    }

    return true;
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>, imgType: ImgType) => {
    const file = event.target.files;
    if (!file || file.length === 0) return;

    let imageFile = file[0];
    if (imageFile.size > 4900000) return setImageErrMsg("Image size must not be larger than 5 MB");

    // Convert HEIC to JPEG using heic2any
    if (imageFile.type === 'image/heic' || imageFile.type === 'image/heif' || imageFile.name.toLowerCase().endsWith('.heic') || imageFile.name.toLowerCase().endsWith('.heif')) {
      setIsConverting(true);

      const jpegBlob = await heic2any({
        blob: imageFile,
        toType: 'image/jpeg',
        quality: 0.8
      });

      const singleJpegBlob = Array.isArray(jpegBlob) ? jpegBlob[0] : jpegBlob;

      const jpegFile = new File(
        [singleJpegBlob],
        imageFile.name.replace(/\.(heic|heif)$/i, '.jpg'),
        { type: 'image/jpeg' }
      );

      imageFile = jpegFile;
      setIsConverting(false);
    }

    // check image resolution
    const url = URL.createObjectURL(imageFile);
    const img = new Image();
    let errorMsg = "";

    const isValidResolution = await new Promise((resolve) => {
      img.onload = () => {
        URL.revokeObjectURL(url);
        if (img.width < 512 || img.height < 512) {
          errorMsg = "Photo dimension must not be smaller than 512 x 512"
          return resolve(false);
        }

        const aspectRatio = img.width / img.height;

        if (!(aspectRatio >= 0.5 && aspectRatio <= 2)) {
          errorMsg = "Aspect ratio of photo must not be larger than 2 or lesser than 0.5";
          return resolve(false);
        }
        resolve(aspectRatio >= 0.5 && aspectRatio <= 2);
      };
      img.src = url;
    });

    // clear the file input value after setting file
    if (inputRef.current) {
      inputRef.current.value = '';
    }

    if (isValidResolution) {
      setImageErrMsg("");
      setIsConverting(true);
      if (setIsUploading) setIsUploading(true);
      const isUploadPhotoSuccess = await handleUploadPhoto(imageFile, aiCaseId, imgType);
      console.log("")
      if (isUploadPhotoSuccess) {
        setImage(imageFile);
      }
      if (setIsUploading) setIsUploading(false);
      return setIsConverting(false);
    }

    setImageErrMsg(errorMsg);
  };

  const getHeaderText = (imgType: ImgType) => {
    switch (imgType) {
      case "chair_only":
        return "1. Sitting in chair";
      case "desk_chair":
        return "2. Working on computer";
      case "standing_desk":
        return "3. Standing (optional)";
    }
  }

  const getHelperText = (imgType: ImgType): string[] => {
    switch (imgType) {
      case "chair_only":
        return ["Full Side profile (Head to feet)", "Hands on thighs. Looking in front."];
      case "desk_chair":
        return ["Full Side profile (Head to feet)", "Hands on keyboard. Looking at screen"];
      case "standing_desk":
        return ["Full Side profile (Head to feet)", "Standing. Hands on keyboard. Looking at screen"];
      default:
        return [];
    }
  }

  return (
    <Stack direction={"column"} gap={"24px"}
      sx={{
        border: "0.5px solid #9C9C9C",
        borderRadius: "10px",
        padding: { xs: "20px", sm: "30px" },
        alignItems: { xs: "center", sm: "stretch" },
        height: "100%", // Make the entire stack take full height
        display: "flex",
        flexDirection: "column",
        width: "100%",
      }}
    >
      <Stack
        sx={{
          position: 'relative',
          width: "100%",
          alignItems: { xs: "center", sm: "flex-start" },
        }}
      >
        <MLTypography
          fontSize={"24px"}
          variant="h1"
          fontWeight={700}
          marginBottom={"20px"}
        >
          {getHeaderText(imgType)}
        </MLTypography>
        <Stack
          sx={{
            position: 'relative',
            alignItems: "center",
            width: "100%",
            height: "380px",
            backgroundColor: "#fcfcfc",
            overflow: "hidden",
          }}
        >
          {isConverting ? (
            <Loading />
          ) : (
            <Box
              component="img"
              sx={{
                border: "0.5px solid #9C9C9C",
                borderRadius: "10px",
                backgroundColor: "#fcfcfc",
                width: "100%",
                height: "380px",
                objectFit: imgUrl ? "contain" : "cover"
              }}
              src={imgUrl ? imgUrl : samplePhoto}
              alt="Sample photo"
            />
          )}
        </Stack>
        {imgUrl ? (
          <Box
            sx={{
              position: 'absolute',
              bottom: "4%",
              right: "5%",
              borderRadius: "5px",
              backgroundColor: "#31C100"
            }}
          >
            <MLTypography
              sx={{
                color: '#FFFFFF',
                padding: '5px 10px',
                fontWeight: 'bold',
                fontSize: '14px',
                userSelect: 'none',
                pointerEvents: 'none',
                textTransform: 'uppercase',
                letterSpacing: '1px',
                borderRadius: '4px',
              }}
            >
              ✔ Uploaded
            </MLTypography>
          </Box>
        ) : isConverting ? <></> : (
          <Box
            sx={{
              position: 'absolute',
              bottom: "4%",
              right: "5%",
              borderRadius: "5px",
              backgroundColor: "#FF6E6E"
            }}
          >
            <MLTypography
              sx={{
                color: '#FFFFFF',
                padding: '5px 10px',
                fontWeight: 'bold',
                fontSize: '14px',
                userSelect: 'none',
                pointerEvents: 'none',
                textTransform: 'uppercase',
                letterSpacing: '1px',
                borderRadius: '4px',
              }}
            >
              Sample Photo
            </MLTypography>
          </Box>
        )}
      </Stack>
      <Stack
        sx={{
          justifyContent: "space-between",
          flexGrow: 1, // Allow this section to grow and fill available space
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Stack gap={"10px"} sx={{ minHeight: "60px", mb: "12px" }}>
          <ul style={{ paddingLeft: "18px", marginBlock: 0, gap: "0px" }}>
            {getHelperText(imgType).map((itemPoint: string, key) => (
              <li key={key + itemPoint}>
                <MLTypography fontSize={"16px"} variant="body1" fontWeight={400}>
                  {itemPoint}
                </MLTypography>
              </li>
            ))}
          </ul>
        </Stack>
        <Stack width={"100%"} sx={{ marginTop: "auto", marginBottom: 0 }}> {/* Push to bottom */}
          <input
            type="file"
            ref={inputRef}
            onChange={(e) => handleFileChange(e, imgType)}
            style={{ display: 'none' }}
            accept="image/*"
          />
          {/* Error message - only renders when there's an error */}
          {imgErrMsg ? (
            <MLTypography fontSize={"16px"} variant="body1" fontWeight={400} color={"red"} sx={{ mb: "15px" }}>
              {imgErrMsg}
            </MLTypography>
          ) : null}

          <MLButton
            fullWidth
            variant="outlined"
            color="primary"
            sx={{
              textTransform: 'uppercase',
              alignSelf: 'flex-start',
            }}
            onClick={() => inputRef.current?.click()}
          >
            {imgUrl ? "Reupload" : "Browse to attach"}
          </MLButton>
        </Stack>
      </Stack>
    </Stack>
  );
}
export default UploadPhoto;