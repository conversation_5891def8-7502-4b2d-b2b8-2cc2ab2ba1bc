// Enhanced MobileUploadStatus Component
import React from 'react';
import {
    Dialog,
    Stack,
    Box,
    CircularProgress,
    LinearProgress
} from '@mui/material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../components/ui/MLButton/MLButton';
import CheckedIcon from '../Work Setup/SvgIcon/CheckedIcon';

interface MobileUploadStatusProps {
    status: 'uploading' | 'complete' | 'receiving' | 'processing';
    progress: number;
    message?: string;
    onClose?: () => void;
}

const MobileUploadStatus: React.FC<MobileUploadStatusProps> = ({
    status,
    progress,
    message,
    onClose
}) => {
    const getStatusIcon = () => {
        switch (status) {
            case 'uploading':
                return <CircularProgress size={60} thickness={4} />;
            case 'receiving':
                return <CircularProgress size={60} thickness={4} color="secondary" />;
            case 'processing':
                return <CircularProgress size={60} thickness={4} color="info" />;
            case 'complete':
                return (
                    <Box sx={{
                        width: 60,
                        height: 60,
                        borderRadius: '50%',
                        backgroundColor: '#4CAF50',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}>
                        <CheckedIcon />
                    </Box>
                );
            default:
                return <CircularProgress size={60} thickness={4} />;
        }
    };

    const getStatusTitle = () => {
        switch (status) {
            case 'uploading':
                return 'Uploading Photos';
            case 'receiving':
                return 'Photos Received';
            case 'processing':
                return 'Processing Analysis';
            case 'complete':
                return 'Photos Sent!';
            default:
                return 'Processing';
        }
    };

    const getStatusMessage = () => {
        if (message) return message;

        switch (status) {
            case 'uploading':
                return 'Uploading your photos to the server...';
            case 'receiving':
                return 'Desktop is receiving your photos...';
            case 'processing':
                return 'AI analysis is in progress on the desktop...';
            case 'complete':
                return 'Your photos are being analyzed on the desktop.\nYou may close this window now.';
            default:
                return 'Processing your request...';
        }
    };

    const showProgressBar = status === 'uploading' && progress > 0;
    const showCloseButton = status === 'complete';

    return (
        <Dialog
            open={true}
            maxWidth="sm"
            fullWidth
            PaperProps={{
                sx: {
                    padding: 4,
                    borderRadius: 3,
                    textAlign: 'center'
                }
            }}
        >
            <Stack spacing={3} alignItems="center">
                {/* Status Icon */}
                {getStatusIcon()}

                {/* Status Title */}
                <MLTypography
                    variant="h5"
                    fontWeight={700}
                    sx={{ color: '#333' }}
                >
                    {getStatusTitle()}
                </MLTypography>

                {/* Progress Bar for Upload */}
                {showProgressBar && (
                    <Box sx={{ width: '100%', mb: 2 }}>
                        <LinearProgress
                            variant="determinate"
                            value={progress}
                            sx={{
                                height: 8,
                                borderRadius: 4,
                                '& .MuiLinearProgress-bar': {
                                    borderRadius: 4
                                }
                            }}
                        />
                        <MLTypography
                            variant="body2"
                            sx={{ mt: 1, color: '#666' }}
                        >
                            {progress}% complete
                        </MLTypography>
                    </Box>
                )}

                {/* Status Message */}
                <MLTypography
                    variant="body1"
                    align="center"
                    sx={{
                        color: '#666',
                        lineHeight: 1.5,
                        maxWidth: '300px'
                    }}
                >
                    {getStatusMessage()}
                </MLTypography>

                {/* Additional Status-specific Content */}
                {status === 'receiving' && (
                    <Stack direction="row" spacing={1} alignItems="center">
                        <Box sx={{ width: 8, height: 8, backgroundColor: '#4CAF50', borderRadius: '50%' }} />
                        <MLTypography variant="body2" sx={{ color: '#4CAF50', fontWeight: 600 }}>
                            Connected to desktop
                        </MLTypography>
                    </Stack>
                )}

                {status === 'processing' && (
                    <Stack direction="row" spacing={1} alignItems="center">
                        <Box sx={{ width: 8, height: 8, backgroundColor: '#2196F3', borderRadius: '50%' }} />
                        <MLTypography variant="body2" sx={{ color: '#2196F3', fontWeight: 600 }}>
                            AI analysis in progress
                        </MLTypography>
                    </Stack>
                )}

                {/* Close Button */}
                {showCloseButton && onClose && (
                    <MLButton
                        variant="contained"
                        onClick={onClose}
                        sx={{
                            backgroundColor: '#DFFF32',
                            color: 'black',
                            textTransform: 'uppercase',
                            fontWeight: 600,
                            px: 4,
                            py: 1.5,
                            borderRadius: 2,
                            '&:hover': {
                                backgroundColor: '#CBEC2A'
                            }
                        }}
                    >
                        Close Window
                    </MLButton>
                )}

                {/* Instructions for non-complete states */}
                {!showCloseButton && (
                    <MLTypography
                        variant="body2"
                        sx={{
                            color: '#999',
                            fontStyle: 'italic',
                            mt: 2
                        }}
                    >
                        Please keep this window open...
                    </MLTypography>
                )}
            </Stack>
        </Dialog>
    );
};

export default MobileUploadStatus;