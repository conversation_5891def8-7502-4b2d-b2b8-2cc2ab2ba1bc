import React from 'react';
import { Box, Stack } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import ScrollToTop from '../../Dashboard/employeeDashboard/ScrollTop';

const UploadSuccess: React.FC = () => {
    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '100vh',
                padding: 2,
            }}
        >
            <ScrollToTop />
            <Stack
                sx={{
                    maxWidth: 450,
                    width: '100%',
                    padding: 4,
                    borderRadius: 2,
                    backgroundColor: '#f5f5f5',
                    border: '0.5px solid #9c9c9c',
                    textAlign: 'center'
                }}
                spacing={3}
            >
                <CheckCircleIcon sx={{ fontSize: 80, color: '#4CAF50', alignSelf: 'center' }} />

                <MLTypography variant="h5" gutterBottom fontWeight={700}>
                    Upload Complete!
                </MLTypography>

                <MLTypography variant="body1">
                    Your workstation photos have been successfully uploaded. You can now close this page and
                    return to your computer to continue with your assessment.
                </MLTypography>

                <MLTypography variant="body2">
                    The analysis process has started on your desktop. Please check your computer to see the results.
                </MLTypography>
            </Stack>
        </Box>
    );
};

export default UploadSuccess;