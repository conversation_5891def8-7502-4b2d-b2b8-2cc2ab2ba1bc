import { Stack } from "@mui/material";
import React from "react";
import { GoodHabitSection } from "../../Report/ReportStyleA";
import { IReportData } from "../../Report/Report";
import { desktop, tablet } from "../../../../responsiveStyles";

interface GoodHabitProp {
  report: IReportData;
}
const GoodHabit = ({
  report
}: GoodHabitProp) => {
  return (
    <Stack
      direction="column"
      sx={{
        paddingX: {
          lg: desktop.contentContainer.paddingX,
          md: tablet.contentContainer.paddingX,
          xs: tablet.contentContainer.paddingX,
        },
        marginBottom: "45px"
      }}
    >
      <GoodHabitSection
        goodHabits={report?.goodHabits ?? []}
        isLeftBorder={true}
        showEditables={false}
        hasContainer={false}
      />
    </Stack>
  )
}
export default GoodHabit;