import React, { useState } from "react";
import { IReportData } from "../../Report/Report";
import { Modal, Stack } from "@mui/material";
import { desktop, tablet } from "../../../../responsiveStyles";
import AskAQuestion from "../../../Dashboard/AskAQuestion";
import { ENActionPlanSection } from "../ENReportStyleA";

interface ActionPlanProp {
  report: IReportData;
  handleActionPlanComplete: (outcome: string, points: number,) => void,
}
const ENActionPlan = ({
  report,
  handleActionPlanComplete
}: ActionPlanProp) => {
  const [isNeedHelpModalOpened, setIsNeedHelpModalOpened] = useState<boolean>(false)

  const handleNeedHelp = () => {
    setIsNeedHelpModalOpened(true);
  }

  return (
    <Stack>
      <ENActionPlanSection
        isLeftBorder={true}
        ergoPostureScore={report.ergoPostureScore}
        achievedActionScore={report.achievedActionScore}
        actionPlans={report.actionPlans}
        handleActionPlanComplete={handleActionPlanComplete}
        showActionDone={true}
        hasContainer={false}
        handleNeedHelp={handleNeedHelp}
      />
      <AskAQuestion
        openAskAQuestionModal={isNeedHelpModalOpened}
        handleClose={() => setIsNeedHelpModalOpened(false)}
      />
    </Stack>
  )
}
export default ENActionPlan;