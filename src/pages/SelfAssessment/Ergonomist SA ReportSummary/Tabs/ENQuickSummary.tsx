import { Stack } from "@mui/material";
import { ConsultScheduleSection, ErgoSubscriptionSection, ProductSuggestionsWithoutTotalSection, ProductSuggestionsWithTotalSection } from "../../Report/ReportStyleA";
import { IActionPlan, IReportData } from "../../Report/Report";
import { desktop, tablet } from "../../../../responsiveStyles";
import { TabTitle } from "../ErgonomistReportReportSummary";
import BodyPartsAtRisk from "../components/ENBodyPartsAtRisk";
import ENErgoRisksAndRecommendations from "../components/ENErgoRisksAndRecommendations";
import MLContainer from "../../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";

interface ReportSummaryProp {
  report: IReportData;
  handleActionPlanComplete: (outcome: string, points: number) => void,
  setCurrentTab: (currentTab: TabTitle) => void,
  handleEditActionPlan: (updatedActionPlan: IActionPlan) => void,
  showProductPrice: boolean;
  enableProductLink: boolean;
}
const ENQuickSummary = ({
  report,
  handleActionPlanComplete,
  setCurrentTab,
  handleEditActionPlan,
  showProductPrice,
  enableProductLink,
}: ReportSummaryProp) => {
  report.mergedProducts
  return (
    <Stack
      direction="column"
      height="100%"
      gap={"45px"}
    >
      <BodyPartsAtRisk handleViewAll={() => setCurrentTab(TabTitle.DETAILED_REPORT)} data={report?.potentialRiskPart || {}} />

      <ENErgoRisksAndRecommendations
        handleActionPlanComplete={handleActionPlanComplete}
        actionPlans={report.actionPlans}
        handleViewAll={() => setCurrentTab(TabTitle.DETAILED_REPORT)}
        handleEditActionPlan={handleEditActionPlan}
      />

      {
        report.mergedProducts.length > 0 &&
        <ProductSuggestionsWithoutTotalSection
          isLeftBorder={true}
          mergedProducts={report.mergedProducts}
          hasContainer={false}
          showProductPrice={showProductPrice}
          enableProductLink={enableProductLink}
        />
      }

      <ConsultScheduleSection
        isLeftBorder={true}
        hasContainer={false}
      />
    </Stack >
  )
}
export default ENQuickSummary;