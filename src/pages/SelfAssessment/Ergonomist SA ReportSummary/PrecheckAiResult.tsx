import React, { useEffect, useState } from 'react';
import {
    Box,
    FormControlLabel,
    Grid,
    Stack,
    RadioGroup,
    Switch,
    IconButton,
    Collapse
} from '@mui/material';
import { useBack, useCreate, useGo, useList, useOne, useUpdate } from '@refinedev/core';

// Import components and assets
import AssessmentHipsImage from '/src/assets/expertAssessImg/Assessment_Hips.png';
import MLCheckbox from '../../../components/ui/MLCheckbox/MLCheckbox';
import MLRadioButton from '../../../components/ui/MLRadioButton/MLRadioButton';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import { desktop, tablet } from '../../../responsiveStyles';
import CustomRightArrow from '../../../assets/icons/CustomRightArrow';
import MLButton from '../../../components/ui/MLButton/MLButton';
import LeftArrow from '../../../assets/icons/CustomLeftArrow';
import { useLocation, useParams } from 'react-router-dom';
import { generateReportData } from '../Report/reportUtils';
import { RequestType } from '../../../models/Case';
import Loading from '../../Loading/Loading';
import { ErrorComponent } from '@refinedev/mui';

// Added for accordion
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { ErChevronleft } from '@mindlens/ergo-icons';
import ChairIcon from '../Work Habit/SvgIcon/ChairIcon';
import DeskIcon from '../Work Habit/SvgIcon/DeskIcon';
import KeyboardIcon from '../Work Setup/SvgIcon/KeyboardIcon';
import MouseIcon from '../Work Setup/SvgIcon/MouseIcon';
import ScreenIcon from '../Work Setup/SvgIcon/ScreenIcon';
import OthersIcon from '../../../assets/icons/OthersIcon';

interface AssessmentItem {
    option: string;
    label: string;
    imageSrc: string;
    id: string | number;
    isLevelOne: boolean;
    group: string;
    isAdjustable?: boolean;
    categoryName: string;
}

interface AssessmentCategory {
    category: string;
    items: AssessmentItem[];
}

interface AssessmentQuestionConfig {
    question: string;
    optionConfig: Array<{
        option: string;
        optionText: string;
        id: string | number;
        isLevelOne: boolean;
        group?: string;
        optionCategory: {
            isAdjustable: boolean;
            categoryName: string;
        };
    }>;
}

const PrecheckAiResult: React.FC = () => {
    const go = useGo()
    const { aicaseid } = useParams()
    const location = useLocation();
    const { userDetails } = location.state || {};
    // const { aiImageAnalysisResponse, userDetails } = location.state || {};
    const [selectedItems, setSelectedItems] = useState<Record<string, string | string[]>>({});
    const [defaultAiResponse, setDefaultAiResponse] = useState<Record<string, string | string[]>>({});

    // Added state for accordion and expand all functionality
    const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

    const [expandAll, setExpandAll] = useState(false);

    const { mutate: updateAiCase } = useUpdate();
    const { mutate: mutateCreateSelfAssessmentReport } = useCreate();
    const { mutate: updateSelfAssessmentCase } = useUpdate();
    const back = useBack();

    const { data: expertAssessQuestion, isLoading: expertAssessQuestionLoading } =
        useList<AssessmentQuestionConfig>({
            resource: "expert-assessment-question-configs",
            pagination: {
                pageSize: 100,
            },
            meta: {
                populate: {
                    optionConfig: {
                        fields: ["option", "optionText", "id", "isLevelOne", "group"],
                        populate: {
                            optionCategory: {
                                fields: ["isAdjustable", "categoryName"]
                            }
                        }
                    },
                },
            },
            filters: [
                {
                    field: "question",
                    operator: "ne",
                    value: "RSI",
                },
            ],
            sorters: [
                {
                    field: "id",
                    order: "asc",
                },
            ],
        });

    const { data: ergonomicScoreTextData } = useList<{ text: string; textContentName: string }>({
        resource: "common-text-contents",
        filters: [
            {
                field: "textContentName",
                operator: "contains",
                value: `ergonomicScore-`,
            }
        ],
        pagination: {
            pageSize: 3,
        }
    });

    const ergonomicScoreText = ergonomicScoreTextData ? ergonomicScoreTextData.data : [];

    const { data: aiCaseData } = useOne({
        resource: "ai-cases",
        id: aicaseid,
        meta: {
            populate: ["selfAssessmentCase", "case", "employee", 'chairImage', 'chairDeskImage', 'standingImage']
        }
    });

    // console.log('aiCaseData: ', aiCaseData);

    // Initialize expanded sections based on assessment items
    useEffect(() => {
        if (expertAssessQuestion?.data) {
            const initialExpandedState = expertAssessQuestion.data.reduce((acc, category) => {
                acc[category.question] = false;
                return acc;
            }, {} as Record<string, boolean>);
            setExpandedSections(initialExpandedState);
        }
    }, [expertAssessQuestion?.data]);


    useEffect(() => {
        if (aiCaseData) {
            if (aiCaseData?.data?.expertAIOverride) {
                setSelectedItems(aiCaseData.data.expertAIOverride)
                setDefaultAiResponse(aiCaseData.data.expertAIOverride)
            }
            else {
                setSelectedItems({
                    ...aiCaseData?.data.chairMappedAiResponse,
                    ...aiCaseData?.data.chairDeskMappedAiResponse,
                    ...aiCaseData?.data.standingMappedAiResponse,
                })
                setDefaultAiResponse({
                    ...aiCaseData?.data.chairMappedAiResponse,
                    ...aiCaseData?.data.chairDeskMappedAiResponse,
                    ...aiCaseData?.data.standingMappedAiResponse,
                })
            }
        }
    }, [aiCaseData])

    // Format data category for rendering the checkboxes and radio buttons
    const assessmentItems: AssessmentCategory[] =
        expertAssessQuestion?.data.map((category) => ({
            category: category.question,
            items: category.optionConfig.map((item) => ({
                option: item.option,
                label: item.optionText,
                imageSrc: AssessmentHipsImage,
                id: item.id,
                isLevelOne: item.isLevelOne,
                group: item.group || "Others",
                isAdjustable: item.optionCategory.isAdjustable,
                categoryName: item.optionCategory.categoryName,
            })),
        })) || [];

    // Toggle section expanded state
    const handleExpandCollapsedAll = () => {
        const expandedSectionsCopy: Record<string, boolean> = {};

        console.log('expandAll: ', expandAll);
        // Toggle all sections based on the current expandAll state
        for (const category in expandedSections) {
            expandedSectionsCopy[category] = !expandAll;
        }

        setExpandedSections(expandedSectionsCopy);
        setExpandAll(!expandAll);
    }

    const toggleSection = (category: string) => {
        const expandedSectionsCopy: Record<string, boolean> = {
            ...expandedSections,
            [category]: !expandedSections[category],
        };

        // Update expanded sections
        setExpandedSections(prev => ({
            ...prev,
            [category]: !prev[category],
        }));

        // If any section is false, set expandAll to false
        const anyCollapsed = Object.values(expandedSectionsCopy).some(value => !value);
        if (anyCollapsed) {
            setExpandAll(false);
        } else {
            setExpandAll(true); // set to true if all are expanded
        }
    };

    // Function to handle checkbox item selection (for Back support)
    const handleCheckboxChange = (item: AssessmentItem) => {
        if (!item.categoryName) return;

        setSelectedItems((prev) => {
            const newSelectedItems = { ...prev };

            // Ensure category is stored as an array
            const selectedGroupItems = Array.isArray(newSelectedItems[item.categoryName])
                ? newSelectedItems[item.categoryName] as string[]
                : newSelectedItems[item.categoryName]
                    ? [newSelectedItems[item.categoryName] as string]
                    : [];

            const updatedGroupItems = selectedGroupItems.includes(item.option)
                ? selectedGroupItems.filter(opt => opt !== item.option) // Remove if exists
                : [...selectedGroupItems, item.option]; // Add if not exists

            if (updatedGroupItems.length > 0) {
                return { ...newSelectedItems, [item.categoryName]: updatedGroupItems };
            } else {
                // If no items left in the group, remove the category
                const result = { ...newSelectedItems };
                delete result[item.categoryName];
                return result;
            }
        });
    };

    // Function to handle radio button selection (for other groups)
    const handleRadioChange = (item: AssessmentItem) => {
        if (!item.categoryName) return;

        setSelectedItems((prev) => {
            const newSelectedItems = { ...prev };

            // If this option is already selected, deselect it
            if (newSelectedItems[item.categoryName] === item.option) {
                delete newSelectedItems[item.categoryName];
            } else {
                // Otherwise select the new option
                newSelectedItems[item.categoryName] = item.option;
            }

            return newSelectedItems;
        });
    };

    // Check if an item is selected (checkbox)
    const isCheckboxChecked = (item: AssessmentItem) => {
        if (!selectedItems[item.categoryName]) return false;

        return Array.isArray(selectedItems[item.categoryName])
            ? (selectedItems[item.categoryName] as string[]).includes(item.option)
            : selectedItems[item.categoryName] === item.option;
    };

    // Group items by their group property within each category
    const groupItemsByGroup = (items: AssessmentItem[]) => {
        return items.reduce((groups, item) => {
            const group = item.group || "Others";
            const groupItems = groups[group] || [];
            return {
                ...groups,
                [group]: [...groupItems, item],
            };
        }, {} as Record<string, AssessmentItem[]>);
    };

    const handleViewReport = () => {
        const selfAssessmentCaseId = aiCaseData?.data?.selfAssessmentCase?.id;

        if (selfAssessmentCaseId) {
            // Update the workHabit field in the selfAssessmentCase
            updateSelfAssessmentCase({
                resource: "self-assessment-cases",
                id: selfAssessmentCaseId,
                values: {
                    workHabit: selectedItems
                },
            }, {
                onSuccess: async () => {
                    // Generate report
                    const reportData = await generateReportData(
                        selfAssessmentCaseId,
                        aiCaseData?.data?.employee.name,
                        ergonomicScoreText,
                        {}
                    )
                    // console.log('reportData: ', reportData);
                    mutateCreateSelfAssessmentReport(
                        // creating the ai-case-report inserting report data and associate to ai-case
                        {
                            resource: "ai-case-reports",
                            values: {
                                generatedReport: {
                                    ...reportData.newReportData,
                                    uploadedImages: [
                                        aiCaseData?.data?.standingImage?.url,
                                        aiCaseData?.data?.chairDeskImage?.url,
                                        aiCaseData?.data?.chairImage?.url
                                    ].filter(Boolean),
                                },
                                // isAICase: true,
                                aiCase: aicaseid,
                                case: aiCaseData?.data.case.id
                            }
                        },
                        {
                            onSuccess(data) {
                                updateAiCase({
                                    resource: "ai-cases",
                                    id: aicaseid!,
                                    values: {
                                        expertAIOverride: selectedItems
                                    },
                                }, {
                                    onSuccess: () => {
                                        // go({ to: `/ai-assessment/report/${selfAssessmentCaseId}` });
                                        go({ to: `/ai-assessment/report/${data?.data.data.id}` });
                                    },
                                    onError: (error) => {
                                        console.error("Error updating AI case:", error);
                                    }
                                });
                            }
                        })


                },
                onError: (error: any) => {
                    console.error("Error updating self assessment case:", error);
                }
            });
        } else {
            console.error("selfAssessment case ID not found");
        }
    }

    // if (!location.state) {
    //     return <ErrorComponent />
    // }
    // Helper function to get the appropriate icons for each category
    const getCategoryIcons = (category: string) => {
        // Convert to lowercase for case-insensitive comparison
        const categoryLower = category.toLowerCase();

        if (categoryLower.includes('chair')) {
            return <ChairIcon />;
        }

        if (categoryLower.includes('desk')) {
            return <DeskIcon />;
        }

        if (categoryLower.includes('keyboard')) {
            return <KeyboardIcon />;
        }

        if (categoryLower.includes('mouse')) {
            return <MouseIcon />;
        }

        if (categoryLower.includes('screen') || categoryLower.includes('monitor') || categoryLower.includes('display')) {
            return <ScreenIcon />;
        }

        // Default case for "Others" or any unmatched category
        return <OthersIcon />;
    };

    if (expertAssessQuestionLoading) {
        return <Loading />;
    }

    const ToggleIcon = ({ isActive }: { isActive: boolean }) => {
        return (
            <Box
                sx={{
                    cursor: 'pointer',
                    // Add hover effect for better user experience
                    '&:hover': {
                        opacity: 0.85
                    }
                }}
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="48"
                    height="24"
                    viewBox="0 0 48 24"
                    fill="none"
                    style={{
                        // This makes the entire SVG have a smooth transition effect
                        transition: "all 1s ease-in-out"
                    }}
                >
                    <mask id="path-1-inside" fill="white">
                        <path d="M0 12C0 5.37258 5.37258 0 12 0H36C42.6274 0 48 5.37258 48 12C48 18.6274 42.6274 24 36 24H12C5.37258 24 0 18.6274 0 12Z" />
                    </mask>
                    <path
                        d="M0 12C0 5.37258 5.37258 0 12 0H36C42.6274 0 48 5.37258 48 12C48 18.6274 42.6274 24 36 24H12C5.37258 24 0 18.6274 0 12Z"
                        fill="white"
                        style={{
                            transition: "fill 0.3s ease-in-out"
                        }}
                    />
                    <path
                        d="M12 0.5H36V-0.5H12V0.5ZM36 23.5H12V24.5H36V23.5ZM12 23.5C5.64873 23.5 0.5 18.3513 0.5 12H-0.5C-0.5 18.9036 5.09644 24.5 12 24.5V23.5ZM47.5 12C47.5 18.3513 42.3513 23.5 36 23.5V24.5C42.9036 24.5 48.5 18.9036 48.5 12H47.5ZM36 0.5C42.3513 0.5 47.5 5.64873 47.5 12H48.5C48.5 5.09644 42.9036 -0.5 36 -0.5V0.5ZM12 -0.5C5.09644 -0.5 -0.5 5.09644 -0.5 12H0.5C0.5 5.64873 5.64873 0.5 12 0.5V-0.5Z"
                        fill="#7856FF"
                        mask="url(#path-1-inside)"
                        style={{
                            transition: "fill 0.3s ease-in-out"
                        }}
                    />

                    {/* Toggle Ball with enhanced transition */}
                    <circle
                        cy="12"
                        r="6"
                        fill="#7856FF"
                        style={{
                            transition: "cx 1s cubic-bezier(0.4, 0, 0.2, 1), fill 1s ease-in-out",
                        }}
                        cx={isActive ? 36 : 12}
                    />
                </svg>
            </Box>
        );
    };
    return (
        <Stack
            sx={{
                backgroundPosition: "center",
                backgroundSize: "cover",
                paddingX: {
                    lg: desktop.contentContainer.paddingX,
                    md: tablet.contentContainer.paddingX,
                    xs: tablet.contentContainer.paddingX,
                },
                paddingY: {
                    lg: desktop.contentContainer.paddingY,
                    md: tablet.contentContainer.paddingY,
                    xs: tablet.contentContainer.paddingY,
                },
            }}
            gap={"30px"}
        >
            <Stack
                direction="row"
                alignItems="center"
                spacing={1}
            >
                <IconButton size="large" onClick={() => back()}>
                    <ErChevronleft />
                </IconButton>
                <MLTypography
                    variant="h1"
                    fontWeight={700}
                    sx={{
                        fontSize: { xs: '1.2rem', sm: '1.5rem', md: '40px' },
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: { xs: 'normal', sm: 'nowrap' },
                        lineHeight: { xs: 1.2, sm: 1.5 },

                    }}
                >
                    Edit AI assessment results: {aiCaseData?.data?.employee.name}
                </MLTypography>
            </Stack>

            <Stack direction={{ xs: "column-reverse", sm: "row" }} gap={"40px"} alignItems="flex-start">
                {/* Scrollable Accordion Section */}
                <Stack
                    sx={{
                        flex: 1,
                        maxHeight: { sm: "calc(100vh - 200px)" },
                        overflowY: { sm: "auto" },
                        pr: { sm: 2 },
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            mb: 1
                        }}
                    >
                        <MLTypography fontWeight={500}
                            sx={{
                                mr: 1,
                                transition: "opacity 0.3s ease-in-out",
                            }}>
                            {expandAll ? "Collapse" : "Expand"} all
                        </MLTypography>
                        <Box onClick={handleExpandCollapsedAll}>
                            <ToggleIcon isActive={expandAll} />
                        </Box>
                    </Box>

                    <Stack gap="30px">
                        {assessmentItems.map((category) => {
                            const groupedItems = groupItemsByGroup(category.items);
                            const isExpanded = expandedSections[category.category] || false;

                            return (
                                <Stack
                                    key={category.category}
                                    sx={{
                                        p: { xs: "15px", sm: "20px", md: "30px" },
                                        border: "0.5px solid #929292",
                                        borderRadius: "10px"
                                    }}
                                    gap="20px"
                                >
                                    {/* Category Header with Toggle */}
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'center',
                                            cursor: 'pointer'
                                        }}
                                        onClick={() => toggleSection(category.category)}
                                    >
                                        <Stack direction={"row"} gap={"10px"} alignItems="center">
                                            {getCategoryIcons(category.category)}
                                            <MLTypography
                                                sx={{
                                                    fontFamily: "Syne",
                                                    fontSize: { xs: "24px", md: "28px", lg: "32px" },
                                                    fontWeight: "600",
                                                }}
                                            >
                                                {category.category}
                                            </MLTypography>
                                        </Stack>
                                        <IconButton disableRipple color='primary' size="medium">
                                            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                                        </IconButton>
                                    </Box>

                                    {/* Collapsible Content */}
                                    <Collapse in={isExpanded}>
                                        <Grid container spacing={{ xs: 1, sm: 2 }}>
                                            {Object.entries(groupedItems).map(([groupName, items]) => (
                                                <Grid
                                                    item
                                                    xs={12}   // 1 column on mobile (extra small)
                                                    sm={6}    // 2 columns on small tablets
                                                    lg={4}    // 4 columns on desktop
                                                    xl={3}    // 4 columns on large desktops
                                                    key={`${category.category}-${groupName}`}
                                                >
                                                    <Stack gap="10px">
                                                        <MLTypography
                                                            variant="body1"
                                                            sx={{
                                                                fontWeight: 'bold',
                                                                fontSize: { xs: "14px", sm: "16px" }
                                                            }}
                                                        >
                                                            {groupName}
                                                        </MLTypography>
                                                        <Stack mx={0.5}>
                                                            {groupName === "Back support" ? (
                                                                // Use checkboxes for Back support group
                                                                items?.map((item) => (
                                                                    <FormControlLabel
                                                                        key={item.id}
                                                                        control={
                                                                            <MLCheckbox
                                                                                name={`item-${item.id}`}
                                                                                checked={isCheckboxChecked(item)}
                                                                                onChange={() => handleCheckboxChange(item)}
                                                                            />
                                                                        }
                                                                        label={
                                                                            <MLTypography
                                                                                sx={{
                                                                                    fontSize: { xs: "13px", sm: "14px", md: "16px" },
                                                                                    lineHeight: 1.3,
                                                                                    // mt: '2px'
                                                                                }}
                                                                            >
                                                                                {item.label}
                                                                            </MLTypography>
                                                                        }
                                                                        sx={{
                                                                            ml: '-9px',                // Compensate for reduced padding
                                                                            width: '100%',
                                                                            mb: { xs: 0.5, sm: 1 }           // Ensure full width
                                                                        }}
                                                                    />
                                                                ))
                                                            ) : (
                                                                // Use radio buttons for all other groups
                                                                <Stack>
                                                                    <RadioGroup
                                                                        name={`${category.category}-${groupName}`}
                                                                        value={selectedItems[items[0]?.categoryName] || ""}
                                                                        onChange={(e) => {
                                                                            const selectedOption = items.find(item => item.option === e.target.value);
                                                                            if (selectedOption) {
                                                                                handleRadioChange(selectedOption);
                                                                            }
                                                                        }}
                                                                    >
                                                                        {items?.map((item) => (
                                                                            <FormControlLabel
                                                                                key={item.id}
                                                                                value={item.option}
                                                                                control={
                                                                                    <MLRadioButton
                                                                                        name={`item-${item.id}`}
                                                                                        checked={selectedItems[item.categoryName] === item.option}
                                                                                    />
                                                                                }
                                                                                label={item.label}
                                                                                sx={{
                                                                                    display: 'flex',
                                                                                    mb: { xs: 0.5, sm: 1 },
                                                                                    '.MuiFormControlLabel-label': {
                                                                                        fontSize: { xs: "13px", sm: "14px", md: "16px" }
                                                                                    }
                                                                                }}
                                                                            />
                                                                        ))}
                                                                    </RadioGroup>
                                                                </Stack>

                                                            )}
                                                        </Stack>
                                                    </Stack>
                                                </Grid>
                                            ))}
                                        </Grid>
                                    </Collapse>
                                </Stack>
                            );
                        })}
                    </Stack>
                </Stack>
                {/* Sticky Images Section */}
                <Stack
                    gap={"10px"}
                    sx={{
                        position: { sm: "sticky" },
                        top: { sm: "100px" },
                        width: { xs: "100%", sm: "300px" },
                        flexShrink: 0,
                    }}
                >
                    <MLTypography
                        variant="h1"
                        fontWeight={500}
                        sx={{
                            fontSize: { xs: '1.2rem', sm: '1.5rem', md: '32px' },
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: { xs: 'normal', sm: 'nowrap' },
                        }}>
                        Uploaded Images
                    </MLTypography>
                    <Stack direction="column" gap={"30px"} width="100%" maxWidth="100%" alignItems="flex-start">
                        <Box
                            component="img"
                            sx={{
                                borderRadius: "10px",
                                height: "220px",
                                width: "auto",
                                objectFit: "contain",
                                alignSelf: "flex-start",
                            }}
                            src={aiCaseData?.data && 'chairImage' in aiCaseData?.data ? aiCaseData?.data?.chairImage?.url : ""}
                            alt="AI analysis chair photo"
                        />
                        <Box
                            component="img"
                            sx={{
                                borderRadius: "10px",
                                height: "220px",
                                width: "auto",
                                objectFit: "contain",
                                alignSelf: "flex-start",
                            }}
                            src={aiCaseData?.data && 'chairDeskImage' in aiCaseData?.data ? aiCaseData?.data?.chairDeskImage?.url : ""}
                            alt="AI analysis desk photo"
                        />
                        {aiCaseData?.data &&
                            'standingImage' in aiCaseData?.data &&
                            aiCaseData?.data?.standingImage?.url && (
                                <Box
                                    component="img"
                                    sx={{
                                        borderRadius: "10px",
                                        height: "220px",
                                        width: "auto",
                                        objectFit: "contain",
                                        alignSelf: "flex-start",
                                    }}
                                    src={aiCaseData?.data?.standingImage?.url}
                                    alt="AI analysis standing photo"
                                />
                            )}
                    </Stack>
                </Stack>
            </Stack>
            {/* Navigation buttons */}
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
            }}>
                <Stack direction={'row'} spacing={2}
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        color: 'primary.main',
                        cursor: 'pointer'
                    }}
                >
                    <MLButton
                        variant="outlined"
                        onClick={() => setSelectedItems(defaultAiResponse)}
                    >
                        Revert to initial AI results
                    </MLButton>
                    <MLButton
                        variant="contained"
                        color="secondary"
                        onClick={handleViewReport}
                    >
                        Go to report
                    </MLButton>
                </Stack>
            </Box>
        </Stack>
    );
};

export default PrecheckAiResult;
