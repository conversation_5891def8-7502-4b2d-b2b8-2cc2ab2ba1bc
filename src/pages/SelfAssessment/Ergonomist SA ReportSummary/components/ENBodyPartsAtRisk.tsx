import React from 'react';
import { Box, Typography, Stack } from '@mui/material';
import { RiskInfo } from '../../Report/Report';
import MLTypography from '../../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../../components/ui/MLButton/MLButton';
import CustomRightArrow from '../../../../assets/icons/CustomRightArrow';

interface RiskData {
    [key: string]: RiskInfo;
}

const RiskGroup: React.FC<{ riskLevel: string; items: Array<{ label: string; image: string }>; }> = ({ riskLevel, items }) => {
    const getBgColor = (level: string) => {
        switch (level.toLowerCase()) {
            case 'high':
                return '#C40000';
            case 'medium':
                return '#FF7A00';
            case 'low':
                return '#FFC700';
            default:
                return '#FFC700';
        }
    };

    const getTextColor = (level: string) => {
        return ['high', 'medium'].includes(level.toLowerCase()) ? '#FFFFFF' : '#000000';
    };

    return (
        <Stack spacing={2} alignItems="center">
            <Box
                sx={{
                    position: 'relative',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%',
                }}
            >
                {/* Line behind the label */}
                <Box
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        left: 0,
                        right: 0,
                        height: '2px',
                        backgroundColor: getBgColor(riskLevel),
                        transform: 'translateY(-50%)',
                        zIndex: 1,
                    }}
                />
                {/* Risk Level Label */}
                <Box
                    sx={{
                        borderRadius: '34px',
                        background: getBgColor(riskLevel),
                        display: 'inline-flex',
                        padding: '6px 17px',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 2,
                    }}
                >
                    <Typography
                        sx={{
                            color: getTextColor(riskLevel),
                            fontSize: '14px',
                            fontWeight: 600,
                            textTransform: 'capitalize',
                        }}
                    >
                        {riskLevel} Risk
                    </Typography>
                </Box>
            </Box>

            {/* Risk Items */}
            <Box
                sx={{
                    display: 'flex',
                    gap: '20px',
                    flexWrap: 'wrap',
                    justifyContent: 'center',
                }}
            >
                {items.map((item, index) => (
                    <Stack key={index} alignItems="center" spacing={1}>
                        <Box
                            sx={{
                                width: '143px',
                                height: '143px',
                                flexShrink: 0,
                                border: `2px solid #C1C1C1`,
                                // border: `2px solid ${getBgColor(riskLevel)}`,
                                borderRadius: '50%',
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                background: '#FFF',
                                padding: '2px',
                            }}
                        >
                            {item.image ? (
                                <img
                                    src={item.image}
                                    alt={item.label}
                                    style={{
                                        width: '99px',
                                        height: '92px',
                                        objectFit: 'contain',
                                    }}
                                />
                            ) : (
                                <Box
                                    sx={{
                                        width: '99px',
                                        height: '92px',
                                        backgroundColor: '#f5f5f5',
                                        borderRadius: '4px',
                                    }}
                                />
                            )}
                        </Box>
                        <Typography variant="body2" fontWeight={500}>
                            {item.label}
                        </Typography>
                    </Stack>
                ))}
            </Box>
        </Stack>
    );
};

const BodyPartsAtRisk: React.FC<{ data: RiskData, handleViewAll: () => void; }> = ({ data, handleViewAll }) => {
    const categorizeRisks = (riskData: RiskData) => {
        const categories = {
            high: [] as Array<{ label: string; image: string }>,
            medium: [] as Array<{ label: string; image: string }>,
            low: [] as Array<{ label: string; image: string }>,
        };

        Object.entries(riskData).forEach(([label, data]) => {
            const riskLevel = data.riskLevel.toLowerCase() as keyof typeof categories;
            if (categories[riskLevel]) {
                categories[riskLevel].push({
                    label,
                    image: data.potentialCondition?.conditionImage || '',
                });
            }
        });

        return categories;
    };

    const riskCategories = categorizeRisks(data);

    return (
        <Stack gap="20px">
            <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                gap="15px"
            >
                <MLTypography
                    variant='h2'
                    sx={{
                        fontSize: "30px",
                        fontWeight: 600
                    }}
                >
                    Body Parts at Risk
                </MLTypography>
                <MLButton
                    color="primary"
                    sx={{
                        textTransform: 'none',
                        '&:hover': {
                            backgroundColor: 'transparent',
                        }
                    }}
                    onClick={handleViewAll}
                    disableRipple
                >
                    View all
                    <CustomRightArrow
                        sx={{
                            fontSize: "20px",
                            ml: 1
                        }}
                    />
                </MLButton>
            </Stack>
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    gap: '40px',
                    // justifyContent: 'space-between',
                    width: '100%',
                }}
            >
                {Object.entries(riskCategories)
                    .filter(([_, items]) => items.length > 0)
                    .map(([level, items]) => (
                        <RiskGroup
                            key={level}
                            riskLevel={level}
                            items={items}
                        />
                    ))}
            </Box>
        </Stack>
    );
};

export default BodyPartsAtRisk;