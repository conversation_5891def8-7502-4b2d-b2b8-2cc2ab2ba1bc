import EditPencilIcon from "../../../../assets/reportIcons/EditPencilIcon.svg";
import { Box, IconButton, Skeleton, Stack, Tooltip } from "@mui/material";
import cross from "../../../../assets/reportIcons/cross.png";
import tick from "../../../../assets/reportIcons/tick.png";
import MLTypography from "../../../../components/ui/MLTypography/MLTypography";
import ImageEditorDialog from "../../../reports/reportAnnotateImg/ImageEditorDialog";
import MediaGalleryDialog from "../../../reports/reportAnnotateImg/MediaGalleryDialog";
import ResetConfirmationDialog from "../../../reports/reportAnnotateImg/ResetConfirmationDialog";
import { ErCamera } from "@mindlens/ergo-icons";
import { Close, Edit, RestorePage, Save, Cancel } from "@mui/icons-material";
import { BaseKey, useCustomMutation } from "@refinedev/core";
import React, { useEffect, useRef, useState } from "react";
import MLButton from "../../../../components/ui/MLButton/MLButton";
import MLInputbox from "../../../../components/ui/MLInputbox/MLInputbox";
import { API_URL } from "../../../../constants";

interface ResultItemProps {
  type: string;
  image: string;
  title: string;
  text: string | string[];
  isCross: boolean;
  index: number;
  isRow?: boolean;
  isDetailed?: boolean;
  outcome?: string;
  onSave?: (bodyPart: any, value: any, outcome: string, updatedTitle: string, updatedText: string | string[], updatedImage?: string) => void;
  reportId?: string | number;
  bodyPart: string;
  mediaAttachments?: any[];
  caseId?: string | number;
  onUpdateImage: (newImage: string, index: number, bodyPart: string, type: string) => void;
  showEditables?: boolean;
  currentHabitReportUserImage?: string;
  currentHabitReportUserAnnotatedImage?: string;
}

const ENErgoRiskItem: React.FC<ResultItemProps> = ({
  bodyPart,
  type,
  index,
  image,
  title: initialTitle,
  text: initialText,
  isCross,
  isRow = true,
  isDetailed = true,
  outcome,
  mediaAttachments = [],
  caseId,
  onSave,
  onUpdateImage,
}) => {

  const [isLoaded, setIsLoaded] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(initialTitle);
  const [text, setText] = useState(initialText);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const [showResetConfirmation, setShowResetConfirmation] = useState(false);
  const [currentImage, setCurrentImage] = useState(image);
  const [backpupImageUrlForReset, setBackpupImageUrlForReset] = useState(image);
  const [isImageUploaded, setIsImageUploaded] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showUnsavedChangesWarning, setShowUnsavedChangesWarning] = useState(false);
  const { mutate: mutateCustom } = useCustomMutation();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [error, setError] = useState<string | null>(null);
  const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/jpg', 'image/svg+xml'];
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

  useEffect(() => {
    // Track if there are unsaved changes
    const titleChanged = title !== initialTitle;
    const textChanged = typeof text === 'string' && typeof initialText === 'string'
      ? text !== initialText
      : JSON.stringify(text) !== JSON.stringify(initialText);
    const imageChanged = currentImage !== image;

    setHasUnsavedChanges(titleChanged || textChanged || imageChanged);
  }, [title, text, currentImage, initialTitle, initialText, image]);

  const handleGalleryImageSelect = (imageUrl: string) => {
    setIsGalleryOpen(false);
    setIsImageLoading(true);

    // Simulate loading before displaying the image
    setTimeout(() => {
      setCurrentImage(imageUrl);
      setIsImageUploaded(true);
      onUpdateImage(imageUrl, index, bodyPart, type);
    }, 2000);
  };

  const handleSaveClick = () => {
    if (outcome && onSave) {
      onSave(bodyPart, type, outcome, title, text);
    }
    setIsEditing(false);
    setHasUnsavedChanges(false);
  };

  const handleCancelClick = () => {
    if (hasUnsavedChanges) {
      setShowUnsavedChangesWarning(true);
    } else {
      resetToInitialState();
    }
  };

  const resetToInitialState = () => {
    setTitle(initialTitle);
    setText(initialText);
    setIsEditing(false);
    setHasUnsavedChanges(false);
  };

  const handleImageLoad = () => {
    setIsLoaded(true);
    setIsImageLoading(false);
  };

  const handleEditClick = () => {
    setIsEditorOpen(true);
  };


  const handleGalleryClose = () => {
    setIsGalleryOpen(false);
  };

  const handleAnnotatedImageSave = (editedImageData: string) => {
    setIsEditorOpen(false);
    setCurrentImage(editedImageData);
    onUpdateImage(editedImageData, index, bodyPart, type);
  };

  const handleResetClick = () => {
    console.log("okay aaba")
    setShowResetConfirmation(true);
  };

  const handleResetConfirm = () => {
    setShowResetConfirmation(false);
    setCurrentImage(image);
    setIsImageUploaded(false);
    onUpdateImage(backpupImageUrlForReset, index, bodyPart, type);
  };

  const handleResetCancel = () => {
    setShowResetConfirmation(false);
  };

  // Update currentImage when image prop changes
  useEffect(() => {
    setCurrentImage(image);
  }, [image]);

  // Update title and text states when initialTitle and initialText props change
  useEffect(() => {
    if (!isEditing) {
      setTitle(initialTitle);
      setText(initialText);
    }
  }, [initialTitle, initialText, isEditing]);

  const UnsavedChangesWarningDialog = () => (
    <ResetConfirmationDialog
      open={showUnsavedChangesWarning}
      onClose={() => setShowUnsavedChangesWarning(false)}
      onConfirm={() => {
        setShowUnsavedChangesWarning(false);
        resetToInitialState();
      }}
    />
  );

  // const getFieldPath = () => {
  //   if (section === 'self') return `recommendation.self.${type}.image`;
  //   if (section === 'others') return `recommendation.others.${type}.image`;
  // };
  const validateFile = (file: File): boolean => {
    // Check file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      setError('Only JPG, JPEG, PNG, or SVG files are allowed');
      return false;
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      setError('File size should be equal to or less than 5 MB');
      return false;
    }

    setError(null);
    return true;
  };

  const uploadFile = async (event: any) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!validateFile(file)) return;

    setIsImageLoading(true);

    const uploadFormData = new FormData();
    uploadFormData.append("files", new File([file], `${Date.now()}-${file.name}`, { type: file.type }));
    // uploadFormData.append("refId", JSON.stringify(index));
    // uploadFormData.append("ref", "api::ai-case-report.ai-case-report");
    // uploadFormData.append("field", "");

    try {
      mutateCustom({
        url: `${API_URL}/api/upload`,
        method: "post",
        values: uploadFormData,
        successNotification: {
          message: 'File uploaded successfully!',
          type: 'success'
        },
        errorNotification: (err) => ({
          message: `File upload failed. Please try again: ${err?.message}`,
          type: 'error'
        })
      }, {
        onSuccess(data) {
          if (data && data.data[0]) {
            const uploadedImageUrl = data.data[0].url;
            setCurrentImage(uploadedImageUrl);
            setIsImageUploaded(true);
            onUpdateImage(uploadedImageUrl, index, bodyPart, type);
            setIsImageLoading(false);
          }
        },
        onError() {
          setIsImageLoading(false);
        }
      });
    } catch (error) {
      console.error('Upload failed:', error);
      setIsImageLoading(false);
    }
  };

  return (
    <Box sx={{ position: "relative" }}>
      <Stack
        direction={isRow ? (isDetailed ? "row" : "column") : "column"}
        gap={isRow ? { md: "30px", xs: "15px" } : "16px"}
      >
        <Stack
          sx={{
            position: "relative",
            width: {
              lg: "300px",
              md: "242px",
              xs: isDetailed ? "120px" : "100%",
            },
          }}
        >
          <Box sx={{ position: "relative" }}>
            {isLoaded && (
              <>
                {/* Cross/Tick Icon */}
                <Stack
                  sx={{
                    position: "absolute",
                    top: -10,
                    right: -10,
                    width: { md: "35px", xs: "30px" },
                    height: "auto",
                    zIndex: 10,
                  }}
                >
                  {isCross ? <img src={cross} alt="cross" /> : <img src={tick} alt="tick" />}
                </Stack>

                {/* Edit Icons for Image */}
                <Stack display={isEditing ? "block" : "none"}>
                  {!isImageUploaded ? (
                    <>
                      <Tooltip title="Select Image">
                        <IconButton
                          // onClick={() => setIsGalleryOpen(true)}
                          onClick={() => fileInputRef.current?.click()}
                          style={{
                            position: "absolute",
                            top: "0px",
                            right: "24px",
                            backgroundColor: "rgba(255, 255, 255, 0.8)",
                            borderRadius: "50%",
                          }}
                        >
                          <ErCamera />
                        </IconButton>
                      </Tooltip>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/jpeg,image/png,image/jpg,image/svg+xml"
                        onChange={uploadFile}
                        style={{ display: 'none' }}
                      />
                    </>
                  ) : (
                    (
                      <Stack
                        direction="row"
                        style={{
                          position: "absolute",
                          top: "0px",
                          right: "15px",
                        }}
                      >
                        <Tooltip title="Reset Image">
                          <IconButton onClick={handleResetClick}>
                            <RestorePage
                              sx={{
                                color: "#FE6E6E",
                              }}
                            />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit Image">
                          <IconButton onClick={handleEditClick}>
                            <img src={EditPencilIcon} alt="edit-icon" />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    )
                  )}
                </Stack>
              </>
            )}

            <Box
              component="img"
              onLoad={handleImageLoad}
              sx={{
                borderStyle: "solid",
                borderWidth: "0.5px",
                borderColor: isEditing ? "#4a90e2" : "#C1C1C1",
                backgroundColor: "#fcfcfc",
                borderRadius: "8px",
                overflow: "hidden",
                width: {
                  lg: "300px",
                  md: "242px",
                  xs: isDetailed ? "120px" : "100%",
                },
                height: { md: "182px", xs: "auto" },
                objectFit: "contain",
                display: isLoaded && !isImageLoading ? "block" : "none",
                transition: "border-color 0.3s ease",
              }}
              src={currentImage}
              alt={title}
            />

            {(!isLoaded || isImageLoading) && (
              <Skeleton
                animation="wave"
                variant="rounded"
                sx={{
                  width: { lg: "300px", md: "242px", xs: "100%" },
                  height: { md: "182px", xs: isDetailed ? "120px" : "182px" },
                  aspectRatio: { xs: "4/3" },
                }}
              />
            )}
          </Box>
        </Stack>

        <Stack gap={"10px"} sx={{ flex: 1 }}>
          {!isEditing ? (
            <MLTypography
              variant="body1"
              fontSize={{ md: "20px", xs: "16px" }}
              fontWeight={600}
              lineHeight={1.2}
            >
              {`${index + 1}. `}{title}
            </MLTypography>
          ) : (
            <MLInputbox
              label="Title"
              multiline
              value={title}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTitle(e.target.value)}
              sx={{ width: "100%" }}
            />
          )}

          <Stack display={isDetailed ? "block" : "none"}>
            {isEditing ? (
              <MLInputbox
                label="Description"
                multiline
                minRows={3}
                value={typeof text === "string" ? text : text.join("\n")}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setText(typeof initialText === "string" ? e.target.value : e.target.value.split("\n"))
                }
                fullWidth
              />
            ) : (
              typeof text === "string" ? (
                <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                  <MLTypography
                    component="div"
                    variant="body1"
                    fontSize={{ md: "16px", xs: "14px" }}
                    fontWeight={400}
                    lineHeight={1.2}
                    sx={{
                      "& > div > ul > li": {
                        marginBottom: "8px"
                      },
                      "& > div > ul > li:last-child": {
                        marginBottom: 0,
                      }
                    }}
                  >
                    <div dangerouslySetInnerHTML={{ __html: text }} />
                  </MLTypography>
                </ul>
              ) : (
                <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                  {text.map((textPoint, key) => (
                    <li key={textPoint + key}>
                      <MLTypography
                        component="div"
                        variant="body1"
                        fontSize={{ md: "16px", xs: "14px" }}
                        fontWeight={400}
                        lineHeight={1.2}
                        sx={{
                          "& > div > ul > li": {
                            marginBottom: "8px"
                          },
                          "& > div > ul > li:last-child": {
                            marginBottom: 0,
                          }
                        }}
                      >
                        {textPoint}
                      </MLTypography>
                    </li>
                  ))}
                </ul>
              )
            )}

            {/* edit/save/cancel button */}
            <Stack alignItems="start" marginTop={"16px"}>
              {isEditing ?
                <Box>
                  <IconButton
                    size="small"
                    aria-label="save"
                    onClick={handleSaveClick}
                  >
                    <Save sx={{ color: "green" }} />
                  </IconButton>
                  <IconButton
                    size="small"
                    aria-label="close"
                    onClick={handleCancelClick}
                  >
                    <Close sx={{ color: "red" }} />
                  </IconButton>
                </Box>
                :
                <MLButton
                  startIcon={<Edit />}
                  onClick={() => setIsEditing(true)}
                >
                  Edit
                </MLButton>
              }
            </Stack>
          </Stack>


          {/* Unsaved Changes Indicator */}
          {hasUnsavedChanges && isEditing && (
            <MLTypography
              variant="caption"
              fontSize="12px"
              color="warning.main"
              sx={{ mt: 1 }}
            >
              Unsaved changes
            </MLTypography>
          )}

          {/* Dialogs */}
          {(
            <>
              <MediaGalleryDialog
                open={isGalleryOpen}
                onClose={handleGalleryClose}
                mediaAttachments={mediaAttachments || []}
                onSelectImage={handleGalleryImageSelect}
                caseId={caseId as BaseKey}
              />

              <ImageEditorDialog
                open={isEditorOpen}
                onClose={() => setIsEditorOpen(false)}
                imageUrl={currentImage}
                onSave={handleAnnotatedImageSave}
                index={index}
                issue={{} as any}
              />

              <ResetConfirmationDialog
                open={showResetConfirmation}
                onClose={handleResetCancel}
                onConfirm={handleResetConfirm}
              />

              <UnsavedChangesWarningDialog />
            </>
          )}
        </Stack>
      </Stack>
    </Box>
  );
};

export default ENErgoRiskItem;