import React, { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { IActionPlan, IReportData, RiskLevelType } from "../Report/Report";
import { <PERSON>ert, Box, ButtonBase, CardContent, Divider, Grid, IconButton, Stack } from "@mui/material";
import { desktop, tablet } from "../../../responsiveStyles";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import ScoreMeter from "../Report/components/ScoreMeter";
import Markdown from "react-markdown";
import { useBack, useGetIdentity, useGo, useList, useOne, useParsed, useUpdate } from "@refinedev/core";
import Loading from "../../Loading/Loading";
import { formatEnumString } from "../../../utils/enumUtils";
import MLTabs from "../../../components/ui/MLTabs/MLTabs";
import MLTab from "../../../components/ui/MLTab/MLTab";
import { generatePDF, sortBodyParts } from "../Report/reportUtils";
import { DownloadViewPdfReport } from "../Report/components/DownloadViewPdfReport";
import RetakeAssessment from "../Report/components/RetakeAssessment";
import MLContainer from "../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";
import ENActionPlan from "./Tabs/ENActionPlan";
import ENDetailedReport from "./Tabs/ENDetailedReport";
import ENQuickSummary from "./Tabs/ENQuickSummary";
import MLButton from "../../../components/ui/MLButton/MLButton";
import { capitalizeWords } from "../../../utils/colorCodeUtils";
import { API_URL, PDF_GENERATION_SERVICE_API_URL, TOKEN_KEY } from "../../../constants";
import axios from "axios";
import { v4 as uuidv4 } from 'uuid';
import LeftArrow from "../../../assets/icons/CustomLeftArrow";
import { ErChevronleft } from "@mindlens/ergo-icons";
import ScrollToTop from "../../Dashboard/employeeDashboard/ScrollTop";
import { TabContext, TabPanel } from "@mui/lab";
import { AuthContext } from "../../../contexts/authContext/AuthContext";
import IIdentity from "../models/IIdentity";
import OneAndOneReportFormat from "../Report Summary/OneAndOne Report Format/OneAndOneReportFormat";
// import { debounce } from "lodash";

export enum TabTitle {
  QUICK_SUMMARY = "quick_summary",
  DETAILED_REPORT = "detailed_report",
  ACTION_PLAN = "action_plan",
}


export interface IGeneratedReport {
  id: number;
  generatedReport: IReportData;
  generatedPdfUrl: string;
  aiCase: {
    id: number | string
    employee: any;
    chairDeskImage: { url: string };
    chairImage: { url: string };
    standingImage?: { url: string }; // Added with optional modifier
  }
}


const ErgonomistReportReportSummary = () => {
  const { id: AiCaseReportID } = useParsed()
  const { mutate: mutateUpdateReport } = useUpdate();
  const go = useGo();

  const [reportData, setReportData] = useState<IReportData>();
  const [reportPdfUrl, setReportPdfUrl] = useState<string>("");
  const [ergonomicScoreText, setErgonomicScoreText] = useState<string>("");
  const [scoreLevel, setScoreLevel] = useState<RiskLevelType>('low');
  const [currentTab, setCurrentTab] = useState<TabTitle>(TabTitle.QUICK_SUMMARY);
  const [isPdfGenerating, setIsPdfGenerating] = useState<boolean>(false);
  const [hasCalledPdfGeneration, setHasCalledPdfGeneration] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [reportFormatType, setReportFormatType] = useState<"bodyPartBased" | "oneOnOneMapping">("bodyPartBased");
  const back = useBack();

  const { data: identity, isLoading: isIdentityLoading } = useGetIdentity<IIdentity>();

  const { data: userData, isLoading: isUserDataLoading } = useOne({
    resource: "users",
    id: identity?.id,
    meta: {
      populate: {
        organization: {
          fields: ["id"],
          populate: {
            domainConfig: {
              fields: ["domain"],
            },
            admin_report_setting: {
              fields: ["reportSetting"],
            }
          },
        },
      },
    },
  });
  // Define the filters with proper typing
  const { data: AICaseReportData, isLoading: isAICaseReportDataLoading } =
    useList<IGeneratedReport>({
      resource: "ai-case-reports",
      meta: {
        populate: {
          aiCase: {
            // populate: ["employee", "chairImage", "chairDeskImage"],
            fields: ["id"],
            populate: {
              employee: {
                fields: ["id", "name"],
                populate: {
                  organization: {
                    fields: ["name", "showProductPrice", "enableProductLink"]
                  }
                }
              },
              chairImage: {
                fields: ["url"],
              },
              chairDeskImage: {
                fields: ["url"],
              },
              standingImage: {
                fields: ["url"],
              },
            }
          }
        },
      },
      filters:
        [{
          field: "id",
          operator: "eq" as const,  // Type assertion to specific operator
          value: AiCaseReportID
        }]
      , sorters: [
        {
          field: "updatedAt",
          order: "desc"
        }
      ],
      pagination: {
        pageSize: 1,
      },
      queryOptions: {
        onError: (error: any) => {
          setErrorMessage(`Failed to load assessment case: ${error.message || 'Unknown error occurred'}`);
          console.error("Self assessment case loading error:", error);
        }
      }
    });

  const {
    data: adminReportSettings,
    isLoading: adminReportSettingsLoading,
    refetch,
  } = useList({
    resource: "admin-report-settings",
    filters: [
      {
        field: "organization.id",
        operator: "eq",
        value: userData?.data?.organization?.id ?? "",
      },
    ],
    meta: {
      populate: "*",
    },
    queryOptions: {
      enabled: !!userData?.data?.organization?.id,
    },
  });

  useEffect(() => {
    if (adminReportSettings?.data) {
      const report = adminReportSettings.data[0]?.reportSetting.reportFormat;

      if (report?.bodyPartBased) {
        setReportFormatType("bodyPartBased");
      } else if (report?.oneOnOneMapping) {
        setReportFormatType("oneOnOneMapping");
      } else {
        setReportFormatType("bodyPartBased"); // default if neither is true or missing
      }
    }
  }, [adminReportSettings]);

  // Rest of the component remains the same
  const reportDetails = useMemo(() =>
    AICaseReportData?.data ?? [],
    [AICaseReportData]
  );



  useEffect(() => {
    const token = localStorage.getItem(TOKEN_KEY);
    if (reportDetails.length == 1 && !isAICaseReportDataLoading) {
      reportDetails[0].generatedReport.potentialRiskPart = sortBodyParts(reportDetails[0].generatedReport.potentialRiskPart);
      setReportData(reportDetails[0].generatedReport);

      if (!reportDetails[0].generatedPdfUrl && !hasCalledPdfGeneration) {
        const getPdfUrl = async () => {
          setIsPdfGenerating(true);
          setHasCalledPdfGeneration(true)
          const pdfUrl = async () => {
            try {
              const response = await axios.post(`${PDF_GENERATION_SERVICE_API_URL}/api/pdf/generate`, {
                uuid: uuidv4(),
                isErgoAssessment: true,
                reportData: {
                  username: reportDetails[0].generatedReport.username,
                  actionPlans: reportDetails[0].generatedReport.actionPlans,
                  achievedActionScore: reportDetails[0].generatedReport.achievedActionScore,
                  ergonomicScoreText: reportDetails[0].generatedReport.ergonomicScoreText,
                  ergoPostureScore: reportDetails[0].generatedReport.ergoPostureScore,
                  goodHabits: reportDetails[0].generatedReport.goodHabits,
                  mergedProducts: reportDetails[0].generatedReport.mergedProducts,
                  potentialRiskPart: reportDetails[0].generatedReport.potentialRiskPart,
                  reportGeneratedBodyParts: reportDetails[0].generatedReport.reportGeneratedBodyParts,
                }
              });

              if (response.status !== 200) {
                throw new Error(`PDF generation failed: ${response}`);
              }

              await axios.put(
                `${API_URL}/api/ai-case-reports/${AiCaseReportID}`, // Corrected API endpoint
                {
                  data: {
                    generatedPdfUrl: response.data.url
                  }
                },
                {
                  headers: { Authorization: `Bearer ${token}` }
                }
              );

              return response.data.url;
            } catch (error) {
              console.error("Error generating PDF:", error);
              return "Error generating PDF";
            }
          };
          const pdfurl = await pdfUrl();
          setReportPdfUrl(pdfurl);
          setIsPdfGenerating(false);
        }
        getPdfUrl();
      } else {
        setReportPdfUrl(reportDetails[0].generatedPdfUrl);
        setIsPdfGenerating(false);
      }
    }
  }, [reportDetails])

  useEffect(() => {
    if (reportData?.ergoPostureScore != undefined) {
      if (reportData?.ergoPostureScore > 80) {
        setScoreLevel('high');
      } else if (reportData?.ergoPostureScore >= 50) {
        setScoreLevel('medium');
      } else {
        setScoreLevel('low');
      }
    }
  }, [reportData]);


  const handleActionPlanComplete = async (outcome: string, points: number) => {
    const newReport = reportData; // Create a proper copy of the report
    const index = newReport?.actionPlans.findIndex(
      (ap) => ap.outcome === outcome,
    );

    if (index !== undefined && index >= 0 && newReport) {
      // Toggle completion state based on points (positive = complete, negative = incomplete)
      const isCompleting = points > 0;
      newReport.actionPlans[index].isCompleted = isCompleting;

      // Add or subtract points from achievedActionScore
      newReport.achievedActionScore += points;

      // Update the report in the database
      mutateUpdateReport({
        resource: "ai-case-reports",
        id: AiCaseReportID!,
        successNotification: false,
        values: {
          generatedReport: newReport,
        },
      });

      // Update local state
      setReportData({ ...newReport });

    }
  };

  if (isAICaseReportDataLoading) {
    return <Box height={640}> <Loading /> </Box>;
  }

  const ErrorAlert = ({ message, onClose }: { message: string | null, onClose: () => void }) => (
    <Alert
      severity="error"
      variant="outlined"
      onClose={onClose}
      sx={{
        marginBottom: "20px",
        borderRadius: "0.625rem"
      }}
    >
      {message}
    </Alert>
  );

  const handleEditedSaveCause = (bodyPart: string, value: any, outcome: string, updatedTitle: string, updatedText: string | string[]) => {
    if (!reportData) return;

    const updatedReportData = { ...reportData };

    if (bodyPart && updatedReportData.potentialRiskPart[bodyPart]) {
      // Check if it's a causedBy or recommendation
      if (value === "causedBys") {
        const causeIndex = updatedReportData.potentialRiskPart[bodyPart].causedBys.findIndex(
          cause => cause.outcome === outcome
        );

        if (causeIndex !== -1) {
          updatedReportData.potentialRiskPart[bodyPart].causedBys[causeIndex].title = updatedTitle;
          updatedReportData.potentialRiskPart[bodyPart].causedBys[causeIndex].text = updatedText;
        }
      }
      else if (value === "recommendations") {
        const recommendationIndex = updatedReportData.potentialRiskPart[bodyPart].recommendations.findIndex(
          rec => rec.outcome === outcome
        );

        if (recommendationIndex !== -1) {
          updatedReportData.potentialRiskPart[bodyPart].recommendations[recommendationIndex].title = updatedTitle;
          updatedReportData.potentialRiskPart[bodyPart].recommendations[recommendationIndex].text = updatedText;
        }
      }

      // Update the report data
      mutateUpdateReport({
        resource: "ai-case-reports",
        id: AiCaseReportID!,
        // successNotification: false,
        values: {
          generatedReport: updatedReportData,
        },
      });

      setReportData(updatedReportData);
    }
  };

  const handleChangeImage = (imageUrl: string, index: number, bodyPart: string, type: string) => {
    if (!reportData) return;

    const updatedReportData = { ...reportData };

    if (bodyPart && updatedReportData.potentialRiskPart[bodyPart]) {
      if (type === "recommendations") {
        // Update recommendation image
        if (updatedReportData.potentialRiskPart[bodyPart].recommendations[index]) {
          updatedReportData.potentialRiskPart[bodyPart].recommendations[index].image = imageUrl;
        }
      } else {
        // Update causedBy image
        if (updatedReportData.potentialRiskPart[bodyPart].causedBys[index]) {
          updatedReportData.potentialRiskPart[bodyPart].causedBys[index].image = imageUrl;
        }
      }

      // Update the report data
      mutateUpdateReport({
        resource: "ai-case-reports",
        id: AiCaseReportID!,
        values: {
          generatedReport: updatedReportData,
        },
      });

      setReportData(updatedReportData);
    }
  };


  const handleEditActionPlan = (updatedActionPlan: IActionPlan) => {
    if (!reportData) return;

    const updatedReportData = { ...reportData };

    // Find the index of the action plan to update
    const actionPlanIndex = updatedReportData.actionPlans.findIndex(
      ap => ap.outcome === updatedActionPlan.outcome
    );

    if (actionPlanIndex !== -1) {
      // Update the specific action plan
      updatedReportData.actionPlans[actionPlanIndex] = updatedActionPlan;

      // Update the report data in the backend
      mutateUpdateReport({
        resource: "ai-case-reports",
        id: AiCaseReportID!,
        values: {
          generatedReport: updatedReportData,
        },
      });

      // Update local state
      setReportData(updatedReportData);
    }
  };

  // console.log('reportDetails: ', reportDetails);
  // console.log('AICaseReportData: ', AICaseReportData);
  // console.log('reportFormatType: ', reportFormatType);
  // console.log('reportData: ', reportData);

  return reportData ? (
    <>
      {reportFormatType === "bodyPartBased" ?
        <Stack
          direction="column"
          height="100%"
          sx={{
            paddingY: {
              lg: desktop.contentContainer.paddingY,
              md: tablet.contentContainer.paddingY,
              xs: tablet.contentContainer.paddingY,
            },
          }}
        >
          <ScrollToTop />
          {/* ergo score section starts */}
          <MLContainer>
            <Stack gap="30px" >
              <Stack direction="row" alignItems="center" spacing={0.5}>
                <IconButton size="large" onClick={() => back()}>
                  <ErChevronleft />
                </IconButton>
                <MLTypography marginLeft={{ xs: "46px", md: "0px" }} lineHeight="120%" variant="h1">
                  Report
                </MLTypography>
              </Stack>

              {
                errorMessage &&
                <ErrorAlert
                  message={errorMessage}
                  onClose={() => setErrorMessage(null)}
                />
              }
              {/* <EmployeeCard
                ergoCase={AICaseReportData?.data[0]}
                disableAssignSchedule={true}
              /> 
          <Stack
            width="100%"
            position="relative"
            display="flex"
            justifyContent="flex-end"
            mb="30px"
          >
            <MLCard
              sx={{
                // display: isEmployee ? 'none' : 'flex',
                width: "366px",
                backgroundColor: '#FFF',
                position: 'absolute',
                top: '-17px',
                left: '0px',
                zIndex: 99,
                borderColor: theme.palette.error.main,
                boxShadow: "0px 4px 5.099999904632568px 0px #00000040",
                backgroundImage: `url("/background_waves/card_backgrounds/red_bottom_left.svg")`,
                backgroundSize: "cover",
                backgroundPosition: "bottom 0px right 0px",
                flex: 3,
              }}
            >
              <CardContent>
                <Stack direction={"column"} pr={10} gap={1.5} minHeight="10rem">
                  <MLTypography variant="h5" fontWeight={"600"}>
                    {capitalizeWords(AICaseReportData?.data?.[0].aiCase?.employee.name)}
                  </MLTypography>
                  <Stack gap={0}>
                    <MLTypography>{AICaseReportData?.data[0].aiCase.employee.address}</MLTypography>
                    <MLTypography fontWeight={"600"}>
                      Desk no: {AICaseReportData?.data[0].aiCase.employee.deskNo}
                    </MLTypography>
                  </Stack>
                  <MLButton
                    disableRipple
                    sx={{
                      alignSelf: "flex-start", padding: "0px",
                      "&:hover": {
                        backgroundColor: "transparent"
                      }
                    }}
                    onClick={() =>
                      go({
                        to: `/employees/show/${AICaseReportData?.data[0].employee.id}`,
                      })
                    }
                  >
                    View profile
                  </MLButton>
                </Stack>
              </CardContent>
            </MLCard>
            <MLCard
              sx={{
                padding: "20px 20px 20px 375px",
                borderRadius: "10px",
                background: "#FFF",
                // marginLeft: isEmployee ? "0px" : "15px",
                height: "250px"
              }}
            >

            </MLCard>
          </Stack> */}

              <>
                {/* Score card in its own Stack */}
                <Stack
                  direction={"row"}
                  sx={{
                    alignItems: "center",
                    backgroundColor: "#6C4AF4",
                    borderRadius: "10px",
                    width: "100%"
                  }}
                >
                  <Grid container marginY={"30px"}>
                    <Grid
                      item
                      xs={4}
                      sx={{
                        display: { xs: "none", md: "flex", lg: "flex", xl: "flex" },
                        position: "relative"
                      }}
                    >
                      <Stack
                        sx={{
                          position: "absolute",
                          left: -165,
                          width: "450px",
                          height: "450px"
                        }}
                      >
                        <ScoreMeter percentage={reportData?.ergoPostureScore ?? 0}></ScoreMeter>
                      </Stack>
                    </Grid>

                    <Grid
                      item
                      md={8}
                      lg={8}
                      xl={8}
                      sx={{
                        paddingX: { xs: "16px", sm: "32px" },
                      }}
                    >
                      {reportData?.ergoPostureScore != undefined && (
                        <Box
                          sx={{
                            minHeight: "385px",
                          }}
                        >
                          <Stack gap={"6px"} direction={"row"} alignItems={"center"}>
                            <MLTypography
                              variant="body1"
                              fontSize={"20px"}
                              color={"#DFFF30"}
                            >
                              Assessment report for:
                            </MLTypography>
                            <MLTypography
                              variant="body1"
                              fontSize={"20px"}
                              fontWeight={600}
                              lineHeight={1}
                              color={"#DFFF30"}
                            >
                              {AICaseReportData?.data[0]?.aiCase.employee.name.toUpperCase()}
                            </MLTypography>
                          </Stack>

                          <Box
                            // paddingY={"30px"}
                            sx={{
                              marginTop: "8px",
                              // borderStyle: "solid",
                              // borderWidth: "0.5px",
                              // borderColor: "#C1C1C1",
                              // borderRadius: "8px",
                              minHeight: "385px",
                            }}
                          >
                            <Stack
                              marginBottom={"20px"}>

                              {/** desktop view ergo score meter */}
                              <Stack
                                marginBottom={"30px"}
                                direction={"row"}
                                display={{ xs: "none", sm: "flex" }}
                                sx={{
                                  alignItems: "center",
                                }}
                              >
                                <Stack>
                                  <MLTypography
                                    variant="body1"
                                    fontSize={"22px"}
                                    fontWeight={400}
                                    lineHeight={1}
                                    color={"white"}
                                  >
                                    Your Ergo score:
                                  </MLTypography>
                                  <Stack
                                    direction={"row"}
                                    sx={{
                                      alignItems: "center",
                                    }}
                                  >
                                    <MLTypography
                                      variant="h1"
                                      fontSize={"72px"}
                                      fontWeight={600}
                                      lineHeight={1}
                                      color={"white"}
                                    >
                                      {reportData.ergoPostureScore}
                                    </MLTypography>
                                    <MLTypography
                                      variant="h1"
                                      fontSize={"35px"}
                                      fontWeight={600}
                                      lineHeight={1}
                                      color={"white"}
                                    >
                                      /100
                                    </MLTypography>
                                  </Stack>
                                </Stack>
                                <Divider
                                  orientation="vertical"
                                  flexItem
                                  sx={{
                                    display: { xs: "none", sm: "flex" },
                                    backgroundColor: "white",
                                    marginX: "30px"
                                  }}
                                />
                                <Stack gap={"8px"} marginTop={{ xs: "16px", sm: 0 }}>
                                  <MLTypography
                                    variant="body1"
                                    fontSize={"22px"}
                                    fontWeight={400}
                                    lineHeight={1}
                                    color={"white"}
                                  >
                                    Higher score =
                                  </MLTypography>
                                  <MLTypography
                                    variant="body1"
                                    fontSize={"22px"}
                                    fontWeight={400}
                                    lineHeight={1}
                                    color={"#DFFF30"}
                                  >
                                    better ergonomics
                                  </MLTypography>
                                </Stack>
                              </Stack>

                              {/** mobile view ergo score meter */}
                              <Stack
                                direction={"column"}
                                display={{ xs: "flex", sm: "none" }}
                              >
                                <Stack>
                                  <MLTypography
                                    variant="body1"
                                    fontSize={"22px"}
                                    fontWeight={400}
                                    lineHeight={1}
                                    color={"white"}
                                  >
                                    Your Ergo score
                                  </MLTypography>

                                  <Grid container
                                    marginY={"20px"}
                                    minHeight={{ xs: "180px" }}
                                  >
                                    <Grid
                                      item
                                      xs={5}
                                      sx={{
                                        position: "relative"
                                      }}
                                    >
                                      <Stack
                                        sx={{
                                          position: "absolute",
                                          left: { xs: "-70px", },
                                          width: { xs: "180px", },
                                          height: { xs: "180px", },
                                        }}
                                      >
                                        <ScoreMeter
                                          percentage={reportData?.ergoPostureScore ?? 0}
                                          lineWidth={6}
                                        />
                                      </Stack>
                                    </Grid>
                                    <Grid
                                      item
                                      xs={7}
                                    >
                                      <Stack
                                        direction={"column"}
                                        height={"100%"}
                                        sx={{
                                          justifyContent: "center",
                                          alignItems: "flex-start",
                                        }}
                                      >
                                        <Stack direction={"row"}
                                          sx={{
                                            alignItems: "center",
                                          }}
                                        >
                                          <MLTypography
                                            variant="h1"
                                            fontSize={"64px"}
                                            fontWeight={600}
                                            lineHeight={1}
                                            color={"white"}
                                          >
                                            {reportData.ergoPostureScore}
                                          </MLTypography>
                                          <MLTypography
                                            variant="h1"
                                            fontSize={"32px"}
                                            fontWeight={600}
                                            lineHeight={1}
                                            color={"white"}
                                          >
                                            /100
                                          </MLTypography>
                                        </Stack>
                                        <Divider
                                          orientation="horizontal"
                                          flexItem
                                          sx={{
                                            backgroundColor: "white",
                                            marginY: "15px"
                                          }}
                                        />
                                        <Stack gap={"8px"}>
                                          <MLTypography
                                            variant="body1"
                                            fontSize={"16px"}
                                            fontWeight={500}
                                            lineHeight={1}
                                            color={"white"}
                                          >
                                            Higher score =
                                          </MLTypography>
                                          <MLTypography
                                            variant="body1"
                                            fontSize={"16px"}
                                            fontWeight={500}
                                            lineHeight={1}
                                            color={"#DFFF30"}
                                          >
                                            better ergonomics
                                          </MLTypography>
                                        </Stack>
                                      </Stack>
                                    </Grid>
                                  </Grid>
                                </Stack>
                              </Stack>

                              <Stack>
                                <MLTypography
                                  variant="body1"
                                  fontSize={"18px"}
                                  fontWeight={600}
                                  lineHeight={"27px"}
                                  color={"white"}
                                >
                                  What it Means?
                                </MLTypography>
                                <MLTypography
                                  variant="body1"
                                  fontSize={{ xs: "16px", sm: "18px" }}
                                  fontWeight={300}
                                  lineHeight={"27px"}
                                  color={"white"}
                                >
                                  Ergo Score is calculated after comparing the current furniture, equipment and posture against ergonomics standards and best practices.
                                </MLTypography>
                              </Stack>
                            </Stack>
                            <Stack position={"relative"}>
                              <Stack>
                                <Markdown
                                  components={{
                                    p: ({ children }) => (
                                      <MLTypography
                                        variant="body1"
                                        fontSize={"18px"}
                                        fontWeight={300}
                                        color={"white"}
                                      >
                                        {children}
                                      </MLTypography>
                                    ),
                                  }}
                                >
                                  {reportData.ergonomicScoreText}
                                </Markdown>
                              </Stack>
                            </Stack>
                          </Box>

                          {/* <Box
                        paddingY={"30px"}
                        marginTop={"20px"}
                        sx={{
                          borderStyle: "solid",
                          borderWidth: "0.5px",
                          borderColor: "#C1C1C1",
                          borderRadius: "8px",
                          minHeight: "385px",
                        }}
                      >
                        <Stack paddingX={"30px"} marginBottom={"20px"}>
                          <Stack
                            direction={{ xs: "column", sm: "row" }}
                            sx={{
                              alignItems: { xs: "flex-start", sm: "center" },
                            }}
                          >
                            <Stack>
                              <MLTypography
                                variant="body1"
                                fontSize={"22px"}
                                fontWeight={400}
                                lineHeight={1}
                                color={"white"}
                              >
                                Your Ergo score:
                              </MLTypography>
                              <Stack
                                direction={"row"}
                                sx={{
                                  alignItems: "center",
                                }}
                              >
                                <MLTypography
                                  variant="h1"
                                  fontSize={"72px"}
                                  fontWeight={600}
                                  lineHeight={1}
                                  color={"white"}
                                >
                                  {reportData.ergoPostureScore}
                                </MLTypography>
                                <MLTypography
                                  variant="h1"
                                  fontSize={"35px"}
                                  fontWeight={600}
                                  lineHeight={1}
                                  color={"white"}
                                >
                                  /100
                                </MLTypography>
                              </Stack>
                            </Stack>
                            <Divider
                              orientation="vertical"
                              flexItem
                              sx={{
                                display: { xs: "none", md: "flex" },
                                backgroundColor: "white",
                                marginX: "30px"
                              }}
                            />
                            <Stack gap={"8px"} marginTop={{ xs: "16px", sm: 0 }}>
                              <MLTypography
                                variant="body1"
                                fontSize={"22px"}
                                fontWeight={400}
                                lineHeight={1}
                                color={"white"}
                              >
                                Higher score =
                              </MLTypography>
                              <MLTypography
                                variant="body1"
                                fontSize={"22px"}
                                fontWeight={400}
                                lineHeight={1}
                                color={"#DFFF30"}
                              >
                                better ergonomics
                              </MLTypography>
                            </Stack>
                          </Stack>
                          <Divider
                            orientation="horizontal"
                            flexItem
                            sx={{
                              display: { xs: "flex", md: "none" },
                              backgroundColor: "white",
                              marginTop: "30px"
                            }}
                          />
                          <Stack marginTop={"30px"} gap={"20px"}>
                            <MLTypography
                              variant="body1"
                              fontSize={"18px"}
                              fontWeight={300}
                              lineHeight={"27px"}
                              color={"white"}
                            >
                              Ergo Score is calculated after comparing the current furniture, equipment and posture against ergonomics standards and best practices.
                            </MLTypography>
                          </Stack>
                        </Stack>
                        <Stack position={"relative"}>
                          <SideHighlight
                            isLeftBorder={true}
                            color={reportData.ergoPostureScore > 80 ? "white" : reportData.ergoPostureScore < 50 ? "#d03f43" : "#eab34b"}
                            width={"8px"}
                            onlyLeftSide={true}
                          />
                          <Stack paddingX={"30px"}>
                            <Markdown
                              components={{
                                p: ({ children }) => (
                                  <MLTypography
                                    variant="body1"
                                    fontSize={"18px"}
                                    fontWeight={300}
                                    color={"white"}
                                  >
                                    {children}
                                  </MLTypography>
                                ),
                              }}
                            >
                              {reportData.ergonomicScoreText}
                            </Markdown>
                          </Stack>
                        </Stack>
                      </Box> */}

                          <Stack
                            direction={"row"}
                            display={{ xs: "none", md: "flex" }}
                            marginTop={"20px"}
                            gap={"12px"}
                            sx={{
                              alignItems: "center",
                            }}
                          >
                            <MLButton
                              variant="outlined"
                              onClick={() => go({ to: `/ai-assessment/prechecks/${AICaseReportData?.data[0].aiCase.id}` })}
                              disableRipple
                            >
                              <LeftArrow
                                sx={{
                                  fontSize: "20px",
                                  mr: 1
                                }}
                              />
                              Back
                            </MLButton>
                            <DownloadViewPdfReport reportPdfUrl={reportPdfUrl} isPdfGenerating={isPdfGenerating} />
                            <MLButton onClick={() =>
                              go({
                                // to: `/reports/sendreporttoemail/${data?.data?.id}`,
                                to: `/reports/sendreporttoemail/52`,
                                options: {
                                  keepQuery: true,
                                },
                              })
                            } variant="outlined" color="secondary">
                              Email
                            </MLButton>
                          </Stack>
                          {/* <RetakeAssessment caseId={caseId} /> */}
                        </Box>
                      )}
                    </Grid>
                  </Grid>
                </Stack>

                {/* Uploaded images in a completely separate Stack below */}
                {reportDetails?.[0]?.aiCase && (
                  <Stack spacing={2} mt={4} mb={4} width="100%">
                    <MLTypography variant="h1" fontSize={"24px"} fontWeight={700} lineHeight={1.2}>
                      Uploaded Images
                    </MLTypography>
                    <Stack
                      direction={{ xs: "column", sm: "row" }}
                      spacing={3}
                      sx={{
                        flexWrap: "wrap",
                        justifyContent: "flex-start"
                      }}
                    >
                      {reportDetails[0].aiCase?.chairImage?.url && (
                        <Box
                          component="img"
                          sx={{
                            border: "0.5px solid #9C9C9C",
                            borderRadius: "10px",
                            width: "auto",
                            height: "200px",
                            objectFit: "cover",
                          }}
                          src={reportDetails[0].aiCase.chairImage.url}
                          alt="AI analysis chair photo"
                        />
                      )}
                      {reportDetails[0].aiCase?.chairDeskImage?.url && (
                        <Box
                          component="img"
                          sx={{
                            border: "0.5px solid #9C9C9C",
                            borderRadius: "10px",
                            width: "auto",
                            height: "200px",
                            objectFit: "cover",
                          }}
                          src={reportDetails[0].aiCase.chairDeskImage.url}
                          alt="AI analysis desk photo"
                        />
                      )}
                      {reportDetails[0].aiCase?.standingImage?.url && (
                        <Box
                          component="img"
                          sx={{
                            border: "0.5px solid #9C9C9C",
                            borderRadius: "10px",
                            width: "auto",
                            height: "200px",
                            objectFit: "cover",
                          }}
                          src={reportDetails[0].aiCase.standingImage.url}
                          alt="AI analysis standing photo"
                        />
                      )}
                    </Stack>
                  </Stack>
                )}
              </>
            </Stack>
          </MLContainer>
          {/* ergo score section end */}

          {/* tab section starts */}
          <Stack >
            <MLContainer>
              {/* Tabs for tab and desktop */}
              <Stack display={{ xs: "none", sm: "flex" }}>
                <MLTabs
                  value={currentTab}
                  onChange={(e, val) => setCurrentTab(val)}
                  defaultValue={formatEnumString(TabTitle.QUICK_SUMMARY)}
                  variant="scrollable"
                  sx={{
                    maxWidth: { xs: "85vw", sm: "100vw" },
                    marginY: "45px",
                  }}
                >
                  {Object.values(TabTitle).map((tab) => (
                    <MLTab
                      key={tab}
                      value={tab}
                      label={formatEnumString(tab)}
                    />
                  ))}
                </MLTabs>
              </Stack>
              {/** Tabs for mobile view */}
              <Stack direction={"row"} display={{ xs: "flex", sm: "none" }}
                sx={{
                  marginY: "30px",
                  gap: "15px",
                  width: "100%" // Ensure the parent takes full width
                }}
              >
                {/* Quick Summary Tab */}
                <ButtonBase
                  component={Stack}
                  sx={{
                    border: currentTab === TabTitle.QUICK_SUMMARY ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                    backgroundColor: currentTab === TabTitle.QUICK_SUMMARY ? "#E3DDFF" : "",
                    borderRadius: "5px",
                    padding: "15px",
                    justifyContent: "center",
                    alignItems: "center",
                    flex: 1, // This makes each child take equal width
                    minWidth: 0, // This prevents overflow
                    transition: "background-color 0.3s",
                    "&:hover": {
                      backgroundColor: currentTab === TabTitle.QUICK_SUMMARY ? "#D9D0FF" : "#F5F5F5",
                    },
                  }}
                  onClick={() => setCurrentTab(TabTitle.QUICK_SUMMARY)}
                  disableRipple={false}
                  centerRipple
                >
                  <MLTypography
                    variant="body1"
                    fontSize={"14px"}
                    fontWeight={600}
                    textAlign={"center"}
                  >
                    Quick Summary
                  </MLTypography>
                </ButtonBase>

                {/* Detailed Report Tab */}
                <ButtonBase
                  component={Stack}
                  sx={{
                    border: currentTab === TabTitle.DETAILED_REPORT ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                    backgroundColor: currentTab === TabTitle.DETAILED_REPORT ? "#E3DDFF" : "",
                    borderRadius: "5px",
                    padding: "15px",
                    justifyContent: "center",
                    alignItems: "center",
                    flex: 1, // This makes each child take equal width
                    minWidth: 0, // This prevents overflow
                    transition: "background-color 0.3s",
                    "&:hover": {
                      backgroundColor: currentTab === TabTitle.DETAILED_REPORT ? "#D9D0FF" : "#F5F5F5",
                    },
                  }}
                  onClick={() => setCurrentTab(TabTitle.DETAILED_REPORT)}
                  disableRipple={false}
                  centerRipple
                >
                  <MLTypography
                    variant="body1"
                    fontSize={"14px"}
                    fontWeight={600}
                    textAlign={"center"}
                  >
                    Detailed Report
                  </MLTypography>
                </ButtonBase>

                {/* Action Plan Tab */}
                <ButtonBase
                  component={Stack}
                  sx={{
                    border: currentTab === TabTitle.ACTION_PLAN ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                    backgroundColor: currentTab === TabTitle.ACTION_PLAN ? "#E3DDFF" : "",
                    borderRadius: "5px",
                    padding: "15px",
                    justifyContent: "center",
                    alignItems: "center",
                    flex: 1, // This makes each child take equal width
                    minWidth: 0, // This prevents overflow
                    transition: "background-color 0.3s",
                    "&:hover": {
                      backgroundColor: currentTab === TabTitle.ACTION_PLAN ? "#D9D0FF" : "#F5F5F5",
                    },
                  }}
                  onClick={() => setCurrentTab(TabTitle.ACTION_PLAN)}
                  disableRipple={false}
                  centerRipple
                >
                  <MLTypography
                    variant="body1"
                    fontSize={"14px"}
                    fontWeight={600}
                    textAlign={"center"}
                  >
                    Action Plan
                  </MLTypography>
                </ButtonBase>
              </Stack>
            </MLContainer>

          </Stack>
          {/* tab section ends*/}
          <TabContext value={currentTab}>
            <TabPanel sx={{ padding: 0 }} value={TabTitle.QUICK_SUMMARY} >
              <MLContainer sx={{ mb: 6 }}>
                <ENQuickSummary
                  report={reportData!}
                  handleActionPlanComplete={handleActionPlanComplete}
                  setCurrentTab={setCurrentTab}
                  handleEditActionPlan={handleEditActionPlan}
                  showProductPrice={reportDetails[0].aiCase.employee.organization.showProductPrice}
                  enableProductLink={reportDetails[0].aiCase.employee.organization.enableProductLink}
                />
              </MLContainer>
            </TabPanel>
            <TabPanel sx={{ padding: 0 }} value={TabTitle.DETAILED_REPORT} >
              <ENDetailedReport
                discomfort={{
                  reportGenerated: { ...reportData.reportGeneratedBodyParts }
                }}
                report={reportData!}
                handleEditedSaveCause={handleEditedSaveCause}
                handleChangeImage={handleChangeImage}
                showProductPrice={reportDetails[0].aiCase.employee.organization.showProductPrice}
                enableProductLink={reportDetails[0].aiCase.employee.organization.enableProductLink}
              />
            </TabPanel>
            <TabPanel sx={{ padding: 0 }} value={TabTitle.ACTION_PLAN} >
              <MLContainer sx={{ mb: 6 }}>
                <ENActionPlan
                  report={reportData!}
                  handleActionPlanComplete={handleActionPlanComplete}
                />
              </MLContainer>
            </TabPanel>
          </TabContext>
        </Stack>
        :
        // one on one report mapping
        <Stack
          direction="column"
          height="100%"
          sx={{
            paddingY: {
              lg: desktop.contentContainer.paddingY,
              md: tablet.contentContainer.paddingY,
              xs: tablet.contentContainer.paddingY,
            },
          }}
          gap="30px"
        >
          <Stack
            sx={{
              paddingX: {
                lg: desktop.contentContainer.paddingX,
                md: tablet.contentContainer.paddingX,
                xs: tablet.contentContainer.paddingX,
              },
            }}
          >
            <Stack direction="row" alignItems="center" spacing={0.5}>
              <IconButton size="large" onClick={() => back()}>
                <ErChevronleft />
              </IconButton>
              <MLTypography marginLeft={{ xs: "46px", md: "0px" }} lineHeight="120%" variant="h1">
                Report
              </MLTypography>
            </Stack>
          </Stack>
          <OneAndOneReportFormat
            report={reportData}
            reportPdfUrl={reportPdfUrl}
            isPdfGenerating={isPdfGenerating}
            caseId={AiCaseReportID}
            discomfort={{
              reportGenerated: { ...reportData.reportGeneratedBodyParts }
            }}
            ergonomicScoreText={reportData.ergonomicScoreText}
            handleActionPlanComplete={handleActionPlanComplete}
            showProductPrice={reportDetails[0]?.aiCase?.employee?.organization?.showProductPrice}
            enableProductLink={reportDetails[0]?.aiCase?.employee?.organization?.enableProductLink}
            isAiAssessment={true} // Since this is specifically for AI assessment
            aiCaseImages={{
              chairImage: reportDetails[0]?.aiCase?.chairImage,
              chairDeskImage: reportDetails[0]?.aiCase?.chairDeskImage,
              standingImage: reportDetails[0]?.aiCase?.standingImage
            }}
            generatedReport={reportDetails}
          // handleEditedSaveCause={handleEditedSaveCause}
          // handleChangeImage={handleChangeImage}
          // handleEditActionPlan={handleEditActionPlan}
          />
        </Stack >
      }
    </>
  ) : (<Box height={640}> <Loading /> </Box>);
};
export default ErgonomistReportReportSummary;
