import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import CloseIcon from "@mui/icons-material/Close";
import {
  Box,
  FormControlLabel,
  Radio,
  RadioGroup,
  Stack,
} from "@mui/material";
import { useState } from "react";
import IDiscomfort from "../models/IDiscomfort";
import ISelfAssessmentOptionConfig from "../models/ISelfAssessmentOptionConfig";

interface BodyCheckboxProps {
  top: number;
  left: number;
  mobileTop: number;
  mobileLeft: number;
  qnTop: number;
  qnRight: number;
  bodyPart: string;
  bodyPartText: string;
  isVisible: boolean;
  handlePartClick: (part: string) => void;
  discomfortQuestion: string;
  discomfortOptions: ISelfAssessmentOptionConfig[];
  discomfort: IDiscomfort;
  setDiscomfort: (discomfort: IDiscomfort) => void;
  isSelectionVisible: boolean;
  setVisibleSelection: (bodyPart: string | null) => void;
}

const BodyImageCheckbox: React.FC<BodyCheckboxProps> = ({
  discomfort,
  setDiscomfort,
  top,
  left,
  mobileTop,
  mobileLeft,
  qnTop,
  qnRight,
  bodyPart,
  bodyPartText,
  isVisible,
  handlePartClick,
  discomfortQuestion,
  discomfortOptions,
  isSelectionVisible,
  setVisibleSelection,
}) => {
  const [showSelection, setShowSelection] = useState<boolean>(true);
  const [selectedAnswer, setSelectedAnswer] = useState<string>(discomfort[`${bodyPart}`] as string)

  const handleSelectionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDiscomfort({ ...discomfort, [`${bodyPart}`]: (event.target as HTMLInputElement).value });
    setSelectedAnswer((event.target as HTMLInputElement).value);
    setVisibleSelection(null);
  };

  const handlePartLabelClick = () => {
    if (isSelectionVisible) {
      setVisibleSelection(null);
    } else {
      setVisibleSelection(bodyPart)
    }
  }

  return (
    <>
      {isVisible ? (
        <>
          <Box
            //display={{ md: "block", xs: "none" }}
            sx={{
              zIndex: 2,
              position: "absolute",
              // top: `${top}%`,
              // right: `${98.5 - right}%`,
              top: { md: `${top}%`, xs: `${mobileTop}%` },
              left: { md: `${left}%`, xs: `${mobileLeft}%` },
              px: "8px",
              py: "5px",
              borderRadius: 1,
              backgroundColor: isSelectionVisible ? "#7856FF" : "white",
              cursor: "pointer",
              border: "#7856FF solid 1px",
            }}
          >
            <Stack flexDirection={"row"} alignItems="center">
              <MLTypography
                sx={{
                  color: isSelectionVisible ? "white" : "#7856FF",
                  textWrap: "nowrap",
                }}
                onClick={handlePartLabelClick}
              >
                {bodyPartText}
              </MLTypography>
              <CloseIcon
                onClick={() => handlePartClick(bodyPart)}
                fontSize="small"
                sx={{ marginLeft: "0.5rem", color: isSelectionVisible ? "white" : "#7856FF" }}
              />
            </Stack>
          </Box>
          {isSelectionVisible ? (
            <Box
              sx={{
                zIndex: 3,
                minWidth: "270px",
                position: "absolute",
                top: { md: `${qnTop}%`, xs: "30%" },
                right: { md: `${qnRight}%`, xs: "auto" },
                left: { md: "auto", xs: "10%" },
                border: "#7856FF solid 1px",
                backgroundColor: "white",
                borderRadius: "10px",
                padding: "20px",
              }}
            >
              <Stack gap={"10px"}>
                <MLTypography lineHeight={1.2} fontWeight={600}>{discomfortQuestion}</MLTypography>
                <RadioGroup
                  value={selectedAnswer}
                  onChange={handleSelectionChange}
                  sx={{ gap: "10px" }}
                >
                  {discomfortOptions.map((option) => (
                    <FormControlLabel
                      key={option.option}
                      value={option.option}
                      control={<Radio sx={{ paddingY: 0 }} onClick={() => setShowSelection(!showSelection)} />}
                      label={option.optionText}
                    />
                  ))}
                </RadioGroup>
              </Stack>
            </Box>
          ) : null}
        </>
      ) : null}
    </>
  );
}
export default BodyImageCheckbox;