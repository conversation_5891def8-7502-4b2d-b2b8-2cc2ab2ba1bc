import ISelfAssessmentBodyPartConfig from "../models/ISelfAssessmentBodyPartConfig";
import BodyFront from "./BodyFront";
import BodyImageCheckbox from "./BodyImageCheckbox";
import BodyBack from "./BodyBack";
import IDiscomfort from "../models/IDiscomfort";
import { useState } from "react";


interface BodyImageProps {
  discomfort: IDiscomfort;
  setDiscomfort: (discomfort: IDiscomfort) => void;
  isFront: boolean;
  bodyPartConfigs: ISelfAssessmentBodyPartConfig[];
}

const BodyImage: React.FC<BodyImageProps> = ({ discomfort, setDiscomfort, isFront, bodyPartConfigs }) => {
  const [visibleSelection, setVisibleSelection] = useState<string | null>(null);

  const handlePartClick = (part: string) => {
    if (Object.hasOwn(discomfort, part)) {
      const newParts = { ...discomfort }
      delete newParts[part]
      setDiscomfort(newParts);
      setVisibleSelection(null);
    } else {
      setDiscomfort({ ...discomfort, [part]: "" });
      setVisibleSelection(part);
    }
  };

  return (
    <div style={{ position: "relative", display: "flex", flexShrink: 0 }}>
      {isFront ? (
        <BodyFront selectedParts={discomfort} handlePartClick={handlePartClick} />
      ) : (
        <BodyBack selectedParts={discomfort} handlePartClick={handlePartClick} />
      )}
      {bodyPartConfigs
        .filter((b) => b.isFront == isFront)
        .map((bodyPart) => (
          <BodyImageCheckbox
            key={bodyPart.bodyPart}
            bodyPart={bodyPart.bodyPart}
            bodyPartText={bodyPart.bodyPartText}
            isVisible={Object.hasOwn(discomfort, bodyPart.bodyPart) ? true : false}
            handlePartClick={handlePartClick}
            top={bodyPart.topOffset}
            left={bodyPart.leftOffset}
            mobileTop={bodyPart.mobileTopOffset}
            mobileLeft={bodyPart.mobileLeftOffset}
            qnTop={bodyPart.qnTopOffset}
            qnRight={bodyPart.qnLeftOffset}
            discomfortQuestion={bodyPart.discomfortQuestion}
            discomfortOptions={bodyPart.discomfortOptions}
            discomfort={discomfort}
            setDiscomfort={setDiscomfort}
            isSelectionVisible={visibleSelection === bodyPart.bodyPart}
            setVisibleSelection={setVisibleSelection}
          />
        ))}
    </div>
  );
};

export default BodyImage;