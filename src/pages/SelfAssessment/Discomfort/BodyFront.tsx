import { Tooltip } from "@mui/material";
import './body.css';
import IDiscomfort from "../models/IDiscomfort";

interface BodyFrontProp {
  selectedParts: IDiscomfort;
  handlePartClick?: (part: string) => void;
  height?: string
}

const BodyFront: React.FC<BodyFrontProp> = ({ selectedParts, handlePartClick, height = "705" }) => {
  return (
    <svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" width="355" height={height} viewBox="0 0 355 705">
      <g id='select-area'>
        {handlePartClick ? (
          <Tooltip title="Head" onClick={() => handlePartClick('head')}>
            <path className={"cls-1 " + (Object.hasOwn(selectedParts, 'head') ? "selected" : "")} id='head'
              d="M209.45,50.44l-2.65-4.72c-6.69-11.96-17.36-19.11-28.54-19.11s-21.86,7.15-28.55,19.11l-2.61,4.66c.21-38.38,27.23-39.95,28.38-40h5.58c1.17.05,28.22,1.63,28.39,40.06Z" />
          </Tooltip>
        ) : (
          <path className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'head-high') ? "selected" : (Object.hasOwn(selectedParts, 'head-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'head-low') ? "selected-low" : "")))} id='head'
            d="M209.45,50.44l-2.65-4.72c-6.69-11.96-17.36-19.11-28.54-19.11s-21.86,7.15-28.55,19.11l-2.61,4.66c.21-38.38,27.23-39.95,28.38-40h5.58c1.17.05,28.22,1.63,28.39,40.06Z" />
        )}
        {handlePartClick ? (
          <Tooltip title="Eyes" onClick={() => handlePartClick('eyes')}>
            <g id='eyes' className={"cls-1 " + (Object.hasOwn(selectedParts, 'eyes') ? "selected" : "")}>
              <path
                d="M165.56,62.8c-3.09,0-6.03-1.15-8.3-3.24-.2-.18-.31-.44-.31-.71s.11-.53.31-.71c2.27-2.09,5.22-3.24,8.3-3.24s6.03,1.15,8.3,3.24c.**********.31.71s-.11.53-.31.71c-2.27,2.09-5.22,3.24-8.3,3.24h0Z" />
              <path
                d="M190.37,62.8c-3.09,0-6.03-1.15-8.3-3.24-.2-.18-.31-.44-.31-.71s.11-.53.31-.71c2.27-2.09,5.22-3.24,8.3-3.24s6.03,1.15,8.3,3.24c.**********.31.71s-.11.53-.31.71c-2.27,2.09-5.22,3.24-8.3,3.24h0Z" />
            </g>
          </Tooltip>
        ) : (
          <g id='eyes' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'eyes-high') ? "selected" : (Object.hasOwn(selectedParts, 'eyes-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'eyes-low') ? "selected-low" : "")))}>
            <path
              d="M165.56,62.8c-3.09,0-6.03-1.15-8.3-3.24-.2-.18-.31-.44-.31-.71s.11-.53.31-.71c2.27-2.09,5.22-3.24,8.3-3.24s6.03,1.15,8.3,3.24c.**********.31.71s-.11.53-.31.71c-2.27,2.09-5.22,3.24-8.3,3.24h0Z" />
            <path
              d="M190.37,62.8c-3.09,0-6.03-1.15-8.3-3.24-.2-.18-.31-.44-.31-.71s.11-.53.31-.71c2.27-2.09,5.22-3.24,8.3-3.24s6.03,1.15,8.3,3.24c.**********.31.71s-.11.53-.31.71c-2.27,2.09-5.22,3.24-8.3,3.24h0Z" />
          </g>
        )}

        {handlePartClick ? (
          <Tooltip title="Shoulder (L)" onClick={() => handlePartClick && handlePartClick('shoulder-left')}>
            <g id='shoulder-left' className={"cls-1 " + (Object.hasOwn(selectedParts, 'shoulder-left') ? "selected" : "")}>
              <path
                d="M200.52,114.26l31.35,21.96c-.7,1.35-2.13,4.84-2.2,10.93-.03,0-.06-.01-.08-.01h-37.72c-4.55,0-8.93,1.28-12.73,3.68l21.38-36.56Z" />
              <path
                d="M255.3,146.64c14.23,10.87,20.98,28.43,19.57,50.8-13.74-.51-23.82-9.01-29.89-16.15-7.56-8.89-12.02-19.2-13.08-25.48-1.97-11.54.57-17.49,1.36-19,2.34.4,12.44,2.48,22.04,9.83Z" />
            </g>
          </Tooltip>
        ) : (
          <g id='shoulder-left' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'shoulder-left-high') ? "selected" : (Object.hasOwn(selectedParts, 'shoulder-left-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'shoulder-left-low') ? "selected-low" : "")))}>
            <path
              d="M200.52,114.26l31.35,21.96c-.7,1.35-2.13,4.84-2.2,10.93-.03,0-.06-.01-.08-.01h-37.72c-4.55,0-8.93,1.28-12.73,3.68l21.38-36.56Z" />
            <path
              d="M255.3,146.64c14.23,10.87,20.98,28.43,19.57,50.8-13.74-.51-23.82-9.01-29.89-16.15-7.56-8.89-12.02-19.2-13.08-25.48-1.97-11.54.57-17.49,1.36-19,2.34.4,12.44,2.48,22.04,9.83Z" />
          </g>
        )}

        {handlePartClick ? (
          <Tooltip title="Shoulder (R)" onClick={() => handlePartClick && handlePartClick('shoulder-right')}>
            <g id='shoulder-right' className={"cls-1 " + (Object.hasOwn(selectedParts, 'shoulder-right') ? "selected" : "")}>
              <path
                d="M155.76,114.02l19.86,36.49c-3.69-2.21-7.89-3.37-12.24-3.37h-36.97c-.07-6.22-1.56-9.72-2.24-11l31.59-22.12Z" />
              <path
                d="M122.81,136.79c.78,1.45,3.36,7.36,1.37,19.02-1.06,6.28-5.52,16.59-13.08,25.48-6.09,7.16-16.22,15.71-30.05,16.16-1.4-22.37,5.35-39.94,19.57-50.81,9.82-7.51,20.16-9.52,22.19-9.85Z" />
            </g>
          </Tooltip>
        ) : (
          <g id='shoulder-right' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'shoulder-right-high') ? "selected" : (Object.hasOwn(selectedParts, 'shoulder-right-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'shoulder-right-low') ? "selected-low" : "")))}>
            <path
              d="M155.76,114.02l19.86,36.49c-3.69-2.21-7.89-3.37-12.24-3.37h-36.97c-.07-6.22-1.56-9.72-2.24-11l31.59-22.12Z" />
            <path
              d="M122.81,136.79c.78,1.45,3.36,7.36,1.37,19.02-1.06,6.28-5.52,16.59-13.08,25.48-6.09,7.16-16.22,15.71-30.05,16.16-1.4-22.37,5.35-39.94,19.57-50.81,9.82-7.51,20.16-9.52,22.19-9.85Z" />
          </g>
        )}

        {handlePartClick ? (
          <Tooltip title="Elbow (L)" onClick={() => handlePartClick && handlePartClick('elbow-left')}>
            <path id='elbow-left' className={"cls-1 " + (Object.hasOwn(selectedParts, 'elbow-left') ? "selected" : "")}
              d="M287.34,249.22l-31.39,13.42c.31-3.58,1.86-14.29,9.74-18.26,2.82-1.42,5.46-1.98,7.87-1.98,6.07,0,10.62,3.57,12.87,*********.6.66.91,1.02Z" />
          </Tooltip>
        ) : (
          <path id='elbow-left' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'elbow-left-high') ? "selected" : (Object.hasOwn(selectedParts, 'elbow-left-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'elbow-left-low') ? "selected-low" : "")))}
            d="M287.34,249.22l-31.39,13.42c.31-3.58,1.86-14.29,9.74-18.26,2.82-1.42,5.46-1.98,7.87-1.98,6.07,0,10.62,3.57,12.87,*********.6.66.91,1.02Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Elbow (R)" onClick={() => handlePartClick && handlePartClick('elbow-right')}>
            <path id='elbow-right' className={"cls-1 " + (Object.hasOwn(selectedParts, 'elbow-right') ? "selected" : "")}
              d="M100.08,262.64l-31.43-13.44c2.27-2.56,10.51-10.45,21.69-4.82,7.88,3.97,9.43,14.68,9.74,18.26Z" />
          </Tooltip>
        ) : (
          <path id='elbow-right' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'elbow-right-high') ? "selected" : (Object.hasOwn(selectedParts, 'elbow-right-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'elbow-right-low') ? "selected-low" : "")))}
            d="M100.08,262.64l-31.43-13.44c2.27-2.56,10.51-10.45,21.69-4.82,7.88,3.97,9.43,14.68,9.74,18.26Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Forearm (L)" onClick={() => handlePartClick && handlePartClick('forearm-left')}>
            <path id='forearm-left' className={"cls-1 " + (Object.hasOwn(selectedParts, 'forearm-left') ? "selected" : "")}
              d="M303.35,280.88c3.47,14.94,11.17,35.82,13.99,43.3-4.28-3.11-9.34-3.82-13.5-1.72-3.98,2.01-6.23,6.22-6.2,11.32-3.22-4.56-10.7-15-17.1-22.61-.68-.82-1.56-1.78-2.57-2.88-6.17-6.74-17.64-19.27-21.01-39.2-.28-1.62-.6-3.23-.95-4.84l32.35-13.84c4.72,5.68,11.61,15.94,14.99,30.47Z" />
          </Tooltip>
        ) : (
          <path id='forearm-left' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'forearm-left-high') ? "selected" : (Object.hasOwn(selectedParts, 'forearm-left-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'forearm-left-low') ? "selected-low" : "")))}
            d="M303.35,280.88c3.47,14.94,11.17,35.82,13.99,43.3-4.28-3.11-9.34-3.82-13.5-1.72-3.98,2.01-6.23,6.22-6.2,11.32-3.22-4.56-10.7-15-17.1-22.61-.68-.82-1.56-1.78-2.57-2.88-6.17-6.74-17.64-19.27-21.01-39.2-.28-1.62-.6-3.23-.95-4.84l32.35-13.84c4.72,5.68,11.61,15.94,14.99,30.47Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Forearm (R)" onClick={() => handlePartClick && handlePartClick('forearm-right')}>
            <path id='forearm-right' className={"cls-1 " + (Object.hasOwn(selectedParts, 'forearm-right') ? "selected" : "")}
              d="M67.59,250.38l32.35,13.83c-.35,1.62-.68,3.25-.96,4.88-3.36,19.93-14.84,32.46-21.01,39.2-1.01,1.1-1.88,2.06-2.57,2.88-6.41,7.62-13.91,18.09-17.11,22.63.03-5.11-2.22-9.32-6.21-11.34-4.16-2.1-9.22-1.39-13.5,1.72,2.83-7.47,10.52-28.36,14-43.3,3.39-14.56,10.29-24.83,15.01-30.5Z" />
          </Tooltip>
        ) : (
          <path id='forearm-right' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'forearm-right-high') ? "selected" : (Object.hasOwn(selectedParts, 'forearm-right-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'forearm-right-low') ? "selected-low" : "")))}
            d="M67.59,250.38l32.35,13.83c-.35,1.62-.68,3.25-.96,4.88-3.36,19.93-14.84,32.46-21.01,39.2-1.01,1.1-1.88,2.06-2.57,2.88-6.41,7.62-13.91,18.09-17.11,22.63.03-5.11-2.22-9.32-6.21-11.34-4.16-2.1-9.22-1.39-13.5,1.72,2.83-7.47,10.52-28.36,14-43.3,3.39-14.56,10.29-24.83,15.01-30.5Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Wrist (L)" onClick={() => handlePartClick && handlePartClick('wrist-left')}>
            <path id='wrist-left' className={"cls-1 " + (Object.hasOwn(selectedParts, 'wrist-left') ? "selected" : "")}
              d="M317.78,326.45l-18.56,8.9c-.56-5.3,1.39-9.57,5.3-11.55,4.07-2.06,9.17-1.01,13.26,2.65Z" />
          </Tooltip>
        ) : (
          <path id='wrist-left' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'wrist-left-high') ? "selected" : (Object.hasOwn(selectedParts, 'wrist-left-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'wrist-left-low') ? "selected-low" : "")))}
            d="M317.78,326.45l-18.56,8.9c-.56-5.3,1.39-9.57,5.3-11.55,4.07-2.06,9.17-1.01,13.26,2.65Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Wrist (R)" onClick={() => handlePartClick && handlePartClick('wrist-right')}>
            <path id='wrist-right' className={"cls-1 " + (Object.hasOwn(selectedParts, 'wrist-right') ? "selected" : "")}
              d="M51.41,323.8c3.91,1.98,5.85,6.25,5.3,11.55l-18.56-8.9c4.08-3.66,9.18-4.71,13.26-2.65Z" />
          </Tooltip>
        ) : (
          <path id='wrist-right' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'wrist-right-high') ? "selected" : (Object.hasOwn(selectedParts, 'wrist-right-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'wrist-right-low') ? "selected-low" : "")))}
            d="M51.41,323.8c3.91,1.98,5.85,6.25,5.3,11.55l-18.56-8.9c4.08-3.66,9.18-4.71,13.26-2.65Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Fingers (L)" onClick={() => handlePartClick && handlePartClick('fingers-left')}>
            <path id='fingers-left' className={"cls-1 " + (Object.hasOwn(selectedParts, 'fingers-left') ? "selected" : "")}
              d="M339.55,363c3.06,6.17,2.68,8.78,2.18,9.78-.39.79-1,.92-1.01.92-.23.03-.43.17-.55.37-.11.21-.12.45-.04.66,2.3,5.51,1.78,7.85,1.27,8.75-.47.81-1.15.92-1.17.92-.24.02-.45.16-.57.37s-.13.47-.03.69c1.04,2.26,1.77,5.17,1.16,5.74-.32.29-.57.3-.75.28-1.56-.22-3.95-4.05-6.48-8.11-4.75-7.61-11.26-18.05-20.25-18.39h-.08c-.53,0-.97.21-1.29.6-.89,1.11-.71,3.63.61,8.7.55,2.14-.44,3.3-1.08,3.8-.76.59-1.57.7-1.85.55-.03-.01-.06-.03-.04-.17.22-3.09-1.2-8.13-3.81-13.49-.03-.07-.07-.12-.11-.17-1.21-2.63-1.7-6.99-2.22-11.6-.68-6.04-1.39-12.28-3.76-16.41l18.93-9.08c2.23,5.39,6.72,12.28,11.06,18.96,3.67,5.63,7.46,11.44,9.88,16.33Z" />
          </Tooltip>
        ) : (
          <path id='fingers-left' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'fingers-left-high') ? "selected" : (Object.hasOwn(selectedParts, 'fingers-left-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'fingers-left-low') ? "selected-low" : "")))}
            d="M339.55,363c3.06,6.17,2.68,8.78,2.18,9.78-.39.79-1,.92-1.01.92-.23.03-.43.17-.55.37-.11.21-.12.45-.04.66,2.3,5.51,1.78,7.85,1.27,8.75-.47.81-1.15.92-1.17.92-.24.02-.45.16-.57.37s-.13.47-.03.69c1.04,2.26,1.77,5.17,1.16,5.74-.32.29-.57.3-.75.28-1.56-.22-3.95-4.05-6.48-8.11-4.75-7.61-11.26-18.05-20.25-18.39h-.08c-.53,0-.97.21-1.29.6-.89,1.11-.71,3.63.61,8.7.55,2.14-.44,3.3-1.08,3.8-.76.59-1.57.7-1.85.55-.03-.01-.06-.03-.04-.17.22-3.09-1.2-8.13-3.81-13.49-.03-.07-.07-.12-.11-.17-1.21-2.63-1.7-6.99-2.22-11.6-.68-6.04-1.39-12.28-3.76-16.41l18.93-9.08c2.23,5.39,6.72,12.28,11.06,18.96,3.67,5.63,7.46,11.44,9.88,16.33Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Fingers (R)" onClick={() => handlePartClick && handlePartClick('fingers-right')}>
            <path id='fingers-right' className={"cls-1 " + (Object.hasOwn(selectedParts, 'fingers-right') ? "selected" : "")}
              d="M37.31,327.71l18.93,9.08c-2.37,4.13-3.07,10.37-3.75,16.41-.52,4.61-1.01,8.97-2.22,11.59-.05.06-.09.11-.12.18-2.61,5.36-4.03,10.4-3.8,13.49,0,.14-.02.16-.05.17-.27.15-1.09.04-1.84-.55-.64-.5-1.64-1.66-1.09-3.79,1.33-5.08,1.5-7.6.61-8.71-.33-.41-.81-.64-1.36-.6-9,.34-15.5,10.78-20.26,18.39-2.53,4.06-4.92,7.89-6.48,8.11-.18.02-.42.01-.74-.28-.62-.57.12-3.48,1.16-5.74.1-.22.08-.47-.03-.67-.12-.21-.33-.36-.57-.39-.02,0-.71-.11-1.17-.92-.52-.9-1.04-3.24,1.26-8.75.09-.21.07-.44-.04-.64s-.3-.34-.53-.39c-.02,0-.64-.13-1.03-.92-.49-1-.87-3.61,2.18-9.78,2.43-4.89,6.22-10.7,9.88-16.33,4.35-6.68,8.84-13.57,11.06-18.96Z" />
          </Tooltip>
        ) : (
          <path id='fingers-right' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'fingers-right-high') ? "selected" : (Object.hasOwn(selectedParts, 'fingers-right-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'fingers-right-low') ? "selected-low" : "")))}
            d="M37.31,327.71l18.93,9.08c-2.37,4.13-3.07,10.37-3.75,16.41-.52,4.61-1.01,8.97-2.22,11.59-.05.06-.09.11-.12.18-2.61,5.36-4.03,10.4-3.8,13.49,0,.14-.02.16-.05.17-.27.15-1.09.04-1.84-.55-.64-.5-1.64-1.66-1.09-3.79,1.33-5.08,1.5-7.6.61-8.71-.33-.41-.81-.64-1.36-.6-9,.34-15.5,10.78-20.26,18.39-2.53,4.06-4.92,7.89-6.48,8.11-.18.02-.42.01-.74-.28-.62-.57.12-3.48,1.16-5.74.1-.22.08-.47-.03-.67-.12-.21-.33-.36-.57-.39-.02,0-.71-.11-1.17-.92-.52-.9-1.04-3.24,1.26-8.75.09-.21.07-.44-.04-.64s-.3-.34-.53-.39c-.02,0-.64-.13-1.03-.92-.49-1-.87-3.61,2.18-9.78,2.43-4.89,6.22-10.7,9.88-16.33,4.35-6.68,8.84-13.57,11.06-18.96Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Knee (L)" onClick={() => handlePartClick && handlePartClick('knee-left')}>
            <path id='knee-left' className={"cls-1 " + (Object.hasOwn(selectedParts, 'knee-left') ? "selected" : "")}
              d="M228.33,468.11c9.22,0,16.73,9.21,16.73,20.52s-7.51,20.52-16.73,20.52-16.74-9.21-16.74-20.52,7.51-20.52,16.74-20.52Z" />
          </Tooltip>
        ) : (
          <path id='knee-left' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'knee-left-high') ? "selected" : (Object.hasOwn(selectedParts, 'knee-left-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'knee-left-low') ? "selected-low" : "")))}
            d="M228.33,468.11c9.22,0,16.73,9.21,16.73,20.52s-7.51,20.52-16.73,20.52-16.74-9.21-16.74-20.52,7.51-20.52,16.74-20.52Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Knee (R)" onClick={() => handlePartClick && handlePartClick('knee-right')}>
            <path id='knee-right' className={"cls-1 " + (Object.hasOwn(selectedParts, 'knee-right') ? "selected" : "")}
              d="M127.53,468.11c9.23,0,16.73,9.21,16.73,20.52s-7.5,20.52-16.73,20.52-16.7-9.17-16.73-20.45c0-.16.01-.31.01-.47.18-11.13,7.6-20.12,16.72-20.12Z" />
          </Tooltip>
        ) : (
          <path id='knee-right' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'knee-right-high') ? "selected" : (Object.hasOwn(selectedParts, 'knee-right-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'knee-right-low') ? "selected-low" : "")))}
            d="M127.53,468.11c9.23,0,16.73,9.21,16.73,20.52s-7.5,20.52-16.73,20.52-16.7-9.17-16.73-20.45c0-.16.01-.31.01-.47.18-11.13,7.6-20.12,16.72-20.12Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Feet (L)" onClick={() => handlePartClick && handlePartClick('feet-left')}>
            <path id="feet-left" className={"cls-1 " + (Object.hasOwn(selectedParts, 'feet-left') ? "selected" : "")}
              d="M257.58,663.15c5.45,8.56,12.91,20.29,12.91,22.78,0,5.5-9.24,6.94-16.99,7.18-12.46.39-17.35-.68-23.14-14.21-2.46-5.75-5.59-7.62-8.11-9.13-3.64-2.19-6.27-3.76-4.9-16.65.75-6.93,1.49-13.93,1.98-20.82,3.34,6.06,9.4,10.88,17.68,14.01,5.74,2.17,10.81,2.91,12.58,**********.26.8.38,1.09.77,1.88,3.94,6.86,7.61,12.63Z" />
          </Tooltip>
        ) : (
          <path id="feet-left" className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'feet-left-high') ? "selected" : (Object.hasOwn(selectedParts, 'feet-left-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'feet-left-low') ? "selected-low" : "")))}
            d="M257.58,663.15c5.45,8.56,12.91,20.29,12.91,22.78,0,5.5-9.24,6.94-16.99,7.18-12.46.39-17.35-.68-23.14-14.21-2.46-5.75-5.59-7.62-8.11-9.13-3.64-2.19-6.27-3.76-4.9-16.65.75-6.93,1.49-13.93,1.98-20.82,3.34,6.06,9.4,10.88,17.68,14.01,5.74,2.17,10.81,2.91,12.58,**********.26.8.38,1.09.77,1.88,3.94,6.86,7.61,12.63Z" />
        )}

        {handlePartClick ? (
          <Tooltip title="Feet (R)" onClick={() => handlePartClick && handlePartClick('feet-right')}>
            <path id='feet-right' className={"cls-1 " + (Object.hasOwn(selectedParts, 'feet-right') ? "selected" : "")}
              d="M138.71,653.12c1.37,12.89-1.26,14.46-4.9,16.65-2.52,1.51-5.65,3.38-8.11,9.13-5.79,13.53-10.67,14.61-23.14,14.21-7.75-.24-16.98-1.68-16.98-7.18,0-2.49,7.46-14.22,12.9-22.78,3.67-5.77,6.84-10.75,7.61-12.63.12-.28.25-.65.38-1.07,1.62-.18,6.81-.9,12.72-3.14,8.18-3.09,14.19-7.83,17.56-13.79.49,6.83,1.23,13.75,1.96,20.6Z" />
          </Tooltip>
        ) : (
          <path id='feet-right' className={"cls-1-nonselect " + (Object.hasOwn(selectedParts, 'feet-right-high') ? "selected" : (Object.hasOwn(selectedParts, 'feet-right-medium') ? "selected-medium" : (Object.hasOwn(selectedParts, 'feet-right-low') ? "selected-low" : "")))}
            d="M138.71,653.12c1.37,12.89-1.26,14.46-4.9,16.65-2.52,1.51-5.65,3.38-8.11,9.13-5.79,13.53-10.67,14.61-23.14,14.21-7.75-.24-16.98-1.68-16.98-7.18,0-2.49,7.46-14.22,12.9-22.78,3.67-5.77,6.84-10.75,7.61-12.63.12-.28.25-.65.38-1.07,1.62-.18,6.81-.9,12.72-3.14,8.18-3.09,14.19-7.83,17.56-13.79.49,6.83,1.23,13.75,1.96,20.6Z" />
        )}
      </g>
      <g id='outline-graph'>
        <g id='eyes-graph'>
          <path
            d="M165.56,55.4c2.86,0,5.72,1.04,7.96,*********.2.5,0,.68-2.25,2.07-5.11,3.11-7.96,3.11s-5.71-1.04-7.96-3.11c-.2-.18-.2-.5,0-.68,2.25-2.07,5.11-3.11,7.96-3.11M165.56,54.4c-3.21,0-6.28,1.2-8.64,3.37-.3.27-.47.67-.47,1.07s.17.8.47,1.07c2.36,2.18,5.43,3.37,8.64,3.37s6.28-1.2,8.64-3.37c.3-.27.47-.67.47-1.07s-.17-.8-.47-1.07c-2.36-2.18-5.43-3.37-8.64-3.37h0Z" />
          <path
            d="M190.37,55.4c2.86,0,5.72,1.04,7.96,*********.2.5,0,.68-2.25,2.07-5.11,3.11-7.96,3.11s-5.71-1.04-7.96-3.11c-.2-.18-.2-.5,0-.68,2.25-2.07,5.11-3.11,7.96-3.11M190.37,54.4c-3.21,0-6.28,1.2-8.64,3.37-.3.27-.47.67-.47,1.07s.17.8.47,1.07c2.36,2.18,5.43,3.37,8.64,3.37s6.28-1.2,8.64-3.37c.3-.27.47-.67.47-1.07s-.17-.8-.47-1.07c-2.36-2.18-5.43-3.37-8.64-3.37h0Z" />
        </g>
        <path id='body-graph'
          d="M340.89,362.34c2.58,5.2,3.31,8.97,2.15,11.18-.36.69-.83,1.1-1.24,1.33,1.72,4.35,2.01,7.52.86,9.44-.38.64-.88,1.05-1.34,1.29.76,1.83,1.96,5.37.48,6.73-.49.44-1.06.67-1.65.67-.1,0-.21,0-.31-.02-2.26-.31-4.48-3.87-7.55-8.79-4.58-7.34-10.84-17.38-19.04-17.69-.1,0-.12.01-.14.04-.29.36-.56,1.8.9,7.39.79,3.07-.87,4.76-1.62,5.35-1.14.9-2.57,1.18-3.48.69-.58-.31-.89-.9-.83-1.61.08-1.05.05-5.05-3.58-12.55-.04-.05-.08-.1-.11-.16-1.38-2.84-1.9-7.42-2.44-12.27-.67-5.93-1.43-12.65-3.95-16.45-.02-.03-.04-.05-.05-.08-.83-1.19-10.56-15.18-18.56-24.7-.66-.79-1.53-1.74-2.53-2.83-6.27-6.85-17.94-19.59-21.38-39.96-1.51-8.92-4.22-17.74-8.07-26.2-1.93-4.24-3.71-8.47-5.35-12.67-1.99,6.91-8.34,29.31-9.66,38.86-.73,5.37,1.13,14.66,2.94,23.65,2.05,10.25,4,19.94,2.38,24.48-.13.34-.51.92-1.13,1.69.9,2.64,15.63,45.97,16.01,79.31.39,33.45-6,79.38-6.33,81.7.08.84.66,7.37.5,12.98-.1,3.56.41,7.04,1.53,10.35,3.86,11.4,9.55,35.71,4.31,70.35-7.06,46.64-3.19,68.53-1.73,74.42.13.15.2.34.18.56,0,.02-.01.05-.02.07.19.69.31,1.04.31,1.06.73,1.75,4.02,6.92,7.5,12.4,7.69,12.09,13.14,20.86,13.14,23.58,0,5.38-6.21,8.3-18.44,8.68-1.22.04-2.38.06-3.47.06-10.53,0-15.54-2.2-21.1-15.18-2.26-5.3-5.05-6.97-7.5-8.44-3.96-2.37-7.09-4.25-5.62-18.09,2.23-20.74,4.53-42.18-.35-59.18l-.19-.66c-4.78-16.64-13.68-47.6-9.76-81.8,0-.12.06-.23.13-.32-5.92-11.46-7.92-27.94-9.85-44.02-1.16-9.61-2.25-18.69-4.16-26.08-8.35-32.41-10.47-56.81-9.76-68.2-1.37.59-2.83.9-4.29.9-1.08,0-2.16-.18-3.2-.5.82,11.15-1.24,35.82-9.72,68.76-1.9,7.39-3,16.47-4.16,26.08-1.95,16.24-3.97,32.9-10.02,44.36,3.8,33.61-4.57,62.69-9.57,80.08l-.4,1.4c-4.88,17-2.58,38.44-.36,59.18,1.48,13.84-1.65,15.72-5.62,18.09-2.45,1.47-5.23,3.14-7.5,8.44-5.55,12.98-10.57,15.18-21.1,15.18-1.09,0-2.25-.02-3.47-.06-12.23-.38-18.43-3.3-18.43-8.68,0-2.72,5.44-11.49,13.14-23.59,3.48-5.47,6.76-10.64,7.49-12.39.07-.18,7.14-20.66-1.26-76.11-5.24-34.64.46-58.94,4.31-70.35,1.12-3.31,1.64-6.79,1.54-10.35-.05-1.45-.04-2.95,0-4.4,0-.04-.01-.07-.01-.11,0-.58.03-1.16.07-1.73.13-3.4.38-6.21.43-6.74-.33-2.32-6.71-48.25-6.33-81.7.38-32.61,14.47-74.79,15.94-79.08-.52-.77-.89-1.43-1.07-1.93-1.62-4.55.32-14.22,2.38-24.46,1.81-9,3.68-18.3,2.93-23.67-1.34-9.81-7.91-32.88-9.69-39.03-1.66,4.27-3.46,8.56-5.41,12.85-3.01,6.61-5.31,13.43-6.9,20.36,0,.15,0,.25,0,.29,0,.18-.07.33-.18.46-.37,1.69-.72,3.39-1.01,5.09-3.44,20.37-15.11,33.11-21.38,39.96-1,1.09-1.87,2.04-2.53,2.83-8.32,9.9-18.51,24.64-18.62,24.79,0,0-.02.02-.03.03-2.5,3.81-3.26,10.5-3.92,16.41-.55,4.85-1.06,9.43-2.45,12.27-.03.06-.07.12-.12.17-3.61,7.5-3.64,11.49-3.56,12.55.05.7-.25,1.29-.84,1.6-.9.49-2.33.2-3.48-.69-.74-.59-2.4-2.28-1.61-5.35,1.46-5.59,1.19-7.03.89-7.39-.02-.02-.03-.04-.13-.04-8.2.31-14.47,10.35-19.04,17.69-3.08,4.92-5.29,8.48-7.55,8.79-.11.02-.21.02-.32.02-.59,0-1.15-.23-1.64-.67-1.48-1.36-.29-4.89.48-6.73-.46-.24-.97-.65-1.35-1.29-1.14-1.92-.85-5.09.86-9.44-.4-.23-.88-.64-1.23-1.33-1.16-2.21-.44-5.98,2.14-11.18,2.46-4.97,6.28-10.83,9.96-16.49,4.41-6.77,8.96-13.76,11.07-19.05-.02-.16,0-.31.08-.44.57-1.48,10.82-27.95,14.97-45.82,5.12-21.99,18.13-34.42,20.16-36.25.04-35.28,6.8-44.68,8.3-46.34-1.09-16.21,1.58-38.35,20.19-52.55,10.58-8.06,21.43-9.9,23.03-10.13l32.38-22.67v-19.75c0-.26.14-.47.33-.61-6.5-8.12-7.38-16.53-7.49-18.92-1.73-.35-6.07-1.89-7.09-8.67-.81-5.46-.22-9.16,1.77-11.02,1.01-.94,2.14-1.17,2.92-1.19-.77-41.86,29.53-43.55,29.84-43.56h5.64c.33,0,30.63,1.69,29.85,43.56.77.02,1.91.25,2.92,1.19,1.99,1.85,2.58,5.56,1.76,11.02-1.01,6.78-5.36,8.32-7.08,8.67-.12,2.43-1.02,11.09-7.83,19.34.02.06.04.12.04.19v19.51h.01c.19.11.31.29.35.49l32.02,22.42c1.61.23,12.46,2.07,23.03,10.13,18.62,14.2,21.28,36.34,20.2,52.55,1.5,1.66,8.25,11.06,8.29,46.34.5.45,1.66,1.54,3.2,3.24.85.87,1.33,1.48,1.35,1.5.02.04.03.08.05.12,4.85,5.76,12.05,16.33,15.56,31.39,4.15,17.84,14.37,44.26,14.97,45.81.08.14.1.29.08.45,2.12,5.29,6.66,12.28,11.07,19.05,3.69,5.66,7.5,11.52,9.96,16.49ZM341.73,372.78c.5-1,.88-3.61-2.18-9.78-2.42-4.89-6.21-10.7-9.88-16.33-4.34-6.68-8.83-13.57-11.06-18.96l-18.93,9.08c2.37,4.13,3.08,10.37,3.76,16.41.52,4.61,1.01,8.97,2.22,11.6.04.05.08.1.11.17,2.61,5.36,4.03,10.4,3.81,13.49-.02.14.01.16.04.17.28.15,1.09.04,1.85-.55.64-.5,1.63-1.66,1.08-3.8-1.32-5.07-1.5-7.59-.61-8.7.32-.39.76-.6,1.29-.6h.08c8.99.34,15.5,10.78,20.25,18.39,2.53,4.06,4.92,7.89,6.48,8.11.18.02.43.01.75-.28.61-.57-.12-3.48-1.16-5.74-.1-.22-.09-.48.03-.69s.33-.35.57-.37c.02,0,.7-.11,1.17-.92.51-.9,1.03-3.24-1.27-8.75-.08-.21-.07-.45.04-.66.12-.2.32-.34.55-.37.01,0,.62-.13,1.01-.92ZM299.22,335.35l18.56-8.9c-4.09-3.66-9.19-4.71-13.26-2.65-3.91,1.98-5.86,6.25-5.3,11.55ZM317.34,324.18c-2.82-7.48-10.52-28.36-13.99-43.3-3.38-14.53-10.27-24.79-14.99-30.47l-32.35,13.84c.35,1.61.67,3.22.95,4.84,3.37,19.93,14.84,32.46,21.01,39.2,1.01,1.1,1.89,2.06,2.57,2.88,6.4,7.61,13.88,18.05,17.1,22.61-.03-5.1,2.22-9.31,6.2-11.32,4.16-2.1,9.22-1.39,13.5,1.72ZM255.95,262.64l31.39-13.42c-.31-.36-.62-.71-.91-1.02-2.25-2.23-6.8-5.8-12.87-5.8-2.41,0-5.05.56-7.87,1.98-7.88,3.97-9.43,14.68-9.74,18.26ZM283.15,243.8c-.06-19.41-2.22-30.59-4.03-36.56-1.67-5.51-3.36-7.7-3.87-8.27-11.91-.3-21.66-6.34-29.01-14.05,0,.08-.04.16-.08.24l-11.14,18.13.94,3.61c3.01,11.59,7.32,23.57,12.81,35.62,2.5,5.5,4.53,11.15,6.07,16.89.84-4.99,3.26-12.89,10.17-16.37,7.49-3.77,13.83-1.86,18.14.76ZM274.87,197.44c1.41-22.37-5.34-39.93-19.57-50.8-9.6-7.35-19.7-9.43-22.04-9.83-.79,1.51-3.33,7.46-1.36,19,1.06,6.28,5.52,16.59,13.08,25.48,6.07,7.14,16.15,15.64,29.89,16.15ZM270.49,685.93c0-2.49-7.46-14.22-12.91-22.78-3.67-5.77-6.84-10.75-7.61-12.63-.12-.29-.25-.65-.38-1.09-1.77-.21-6.84-.95-12.58-3.12-8.28-3.13-14.34-7.95-17.68-14.01-.49,6.89-1.23,13.89-1.98,20.82-1.37,12.89,1.26,14.46,4.9,16.65,2.52,1.51,5.65,3.38,8.11,9.13,5.79,13.53,10.68,14.6,23.14,14.21,7.75-.24,16.99-1.68,16.99-7.18ZM251.13,573.61c5.19-34.33-.44-58.36-4.25-69.64-.82-2.42-1.32-4.93-1.52-7.49-2.63,8.27-9.27,14.17-17.03,14.17-10.06,0-18.24-9.88-18.24-22.02s8.18-22.02,18.24-22.02c7.32,0,13.64,5.24,16.54,12.77.8-5.84,6.6-49.06,6.23-80.91-.36-31.17-13.37-71.22-15.64-77.96-9.56,11.1-46.09,46-50.73,50.43-.42.36-.86.68-1.31.96.03.1.04.2.04.31-.82,11.01,1.22,35.51,9.68,68.31,1.92,7.49,3.02,16.62,4.19,26.28,2.39,19.85,5.1,42.34,16.15,52.16.31.28.34.75.06,1.06-.14.17-.35.25-.56.25-.17,0-.35-.06-.5-.19-2.16-1.92-4.01-4.26-5.6-6.93-3.42,33.24,5.21,63.29,9.88,79.56l.19.66c3.08,10.72,3.35,23.11,2.58,35.95.02.03.05.05.06.09,5.96,14.26,24.64,17.76,29.58,18.45-2.02-8.5-4.37-32.45,1.96-74.25ZM244.88,184.37c.12-.19.32-.31.53-.34-8.71-9.52-13.86-21.27-14.99-27.96-.48-2.8-.69-5.27-.74-7.45-.03,0-.06.02-.09.02h-37.72c-4.52,0-8.88,1.33-12.59,3.86l-1.02.69s-.05.03-.08.04v45.92s.04.01.06.02l18.84,9.07c2.06,1.03,4.27,1.56,6.57,1.56h17.43c5.05,0,9.84-2.68,12.48-7l11.32-18.43ZM245.06,488.63c0-11.31-7.51-20.52-16.73-20.52s-16.74,9.21-16.74,20.52,7.51,20.52,16.74,20.52,16.73-9.21,16.73-20.52ZM230.91,269.13c1.52-10.99,9.58-38.64,10.27-40.99-2.66-7.04-4.9-14.02-6.68-20.86l-.6-2.34c-3.03,3.95-7.8,6.36-12.82,6.36h-17.43c-2.54,0-4.97-.58-7.23-1.71l-18.24-8.79v171c0,.1-.02.19-.06.28,2-.1,3.98-.85,5.6-2.25,20.53-19.58,51.5-49.85,52.58-52.87,1.48-4.16-.51-14.08-2.43-23.69-1.83-9.1-3.72-18.52-2.96-24.14ZM231.87,136.22l-31.35-21.96-21.38,36.56c3.8-2.4,8.18-3.68,12.73-3.68h37.72s.05.01.08.01c.07-6.09,1.5-9.58,2.2-10.93ZM214.14,64.43c.92-6.13-.19-8.65-1.29-9.68-1.12-1.06-2.46-.79-2.52-.78-.04,0-.08-.01-.13,0-.01,0-.03,0-.05,0s-.04-.02-.06-.02c-.07,0-.14-.02-.2-.05-.06-.02-.11-.05-.16-.08-.01-.01-.03-.01-.04-.02-.02-.02-.04-.05-.06-.08-.04-.04-.1-.08-.13-.13l-4.01-7.15c-6.42-11.48-16.6-18.34-27.23-18.34s-20.82,6.86-27.24,18.34l-3.95,7.04c-.05.13-.11.24-.22.32-.13.12-.3.16-.47.16h-.13s-.02,0-.04,0c-.01-.01-.18-.04-.44-.04-.51,0-1.34.12-2.08.82-1.09,1.03-2.2,3.55-1.28,9.68,1.08,7.24,6.3,7.49,6.36,7.49.41,0,.73.36.72.77,0,.05-.09,5.19,2.73,11.45,1.29,2.89,3.36,6.31,6.63,9.66,3.28,3.35,7.76,6.61,13.89,9.15,1.71.7,3.62,1.08,5.53,1.08s3.79-.37,5.53-1.09c12.27-5.08,17.93-13.03,20.53-18.8,2.81-6.26,2.73-11.4,2.73-11.45-.01-.41.31-.76.72-.77.21-.01,5.28-.31,6.36-7.49ZM206.8,45.72l2.65,4.72c-.17-38.43-27.22-40.01-28.39-40.06h-5.58c-1.15.05-28.17,1.62-28.38,40l2.61-4.66c6.69-11.96,17.36-19.11,28.55-19.11s21.85,7.15,28.54,19.11ZM177.45,150.73l21.88-37.41c-.05-.1-.08-.21-.08-.33v-18.68c-3.49,3.73-8.27,7.28-14.87,10.02-1.93.8-3.99,1.2-6.11,1.2s-4.22-.42-6.1-1.2c-7-2.9-11.96-6.72-15.5-10.7v18.96s.04.04.05.06l20.73,38.08ZM176.73,372.05c-.03-.08-.05-.16-.05-.25v-170.74l-17.04,8.53c-2.26,1.13-4.69,1.71-7.22,1.71h-17.43c-5.08,0-9.89-2.46-12.91-6.48l-.64,2.46c-1.78,6.82-4.01,13.77-6.64,20.78.06.08.11.17.14.27.09.3,8.63,29.36,10.19,40.78.78,5.62-1.12,15.06-2.95,24.18-1.92,9.59-3.92,19.5-2.44,23.65.16.45.51,1.06,1.01,1.8.07.07.12.15.16.23,4.75,6.81,22.67,24.84,50.4,50.64,1.55,1.43,3.46,2.25,5.42,2.44ZM176.68,199.38v-46.4l-.71-.48c-3.71-2.53-8.06-3.86-12.59-3.86h-36.98c-.05,2.18-.26,4.64-.74,7.43-1.13,6.69-6.28,18.44-14.99,27.96.21.04.4.15.51.34l11.33,18.43c2.64,4.32,7.42,7,12.48,7h17.43c2.3,0,4.5-.53,6.55-1.55l17.71-8.87ZM175.62,150.51l-19.86-36.49-31.59,22.12c.68,1.28,2.17,4.78,2.24,11h36.97c4.35,0,8.55,1.16,12.24,3.37ZM163.26,441.48c8.45-32.8,10.49-57.3,9.67-68.31-.01-.22.07-.42.21-.57-1.02-.47-1.99-1.09-2.85-1.89-16.12-15-42.04-39.61-49.82-49.83-2.58,7.75-15.16,46.96-15.51,77.59-.36,31.21,5.2,73.36,6.18,80.51,2.97-7.31,9.2-12.37,16.39-12.37,10.05,0,18.23,9.88,18.23,22.02s-8.18,22.02-18.23,22.02c-7.61,0-14.13-5.65-16.87-13.66-.22,2.38-.71,4.72-1.48,6.98-3.81,11.28-9.44,35.32-4.24,69.64,6.33,41.84,3.97,65.79,1.95,74.27,4.7-.64,23.57-4.05,29.65-18.33-.78-12.93-.52-25.4,2.57-36.18l.41-1.4c4.87-16.92,12.94-44.99,9.73-77.43-1.53,2.49-3.3,4.68-5.34,6.5-.15.13-.32.19-.5.19-.21,0-.41-.08-.56-.25-.28-.31-.25-.78.06-1.06,11.05-9.82,13.76-32.31,16.15-52.16,1.17-9.66,2.27-18.79,4.2-26.28ZM144.26,488.63c0-11.31-7.5-20.52-16.73-20.52s-16.54,8.99-16.72,20.12c0,.16-.01.31-.01.47.03,11.28,7.52,20.45,16.73,20.45s16.73-9.21,16.73-20.52ZM133.81,669.77c3.64-2.19,6.27-3.76,4.9-16.65-.73-6.85-1.47-13.77-1.96-20.6-3.37,5.96-9.38,10.7-17.56,13.79-5.91,2.24-11.1,2.96-12.72,3.14-.13.42-.26.79-.38,1.07-.77,1.88-3.94,6.86-7.61,12.63-5.44,8.56-12.9,20.29-12.9,22.78,0,5.5,9.23,6.94,16.98,7.18,12.47.4,17.35-.68,23.14-14.21,2.46-5.75,5.59-7.62,8.11-9.13ZM124.18,155.81c1.99-11.66-.59-17.57-1.37-19.02-2.03.33-12.37,2.34-22.19,9.85-14.22,10.87-20.97,28.44-19.57,50.81,13.83-.45,23.96-9,30.05-16.16,7.56-8.89,12.02-19.2,13.08-25.48ZM120.96,203.15l-11.05-17.99c-.05-.07-.07-.15-.08-.23-7.38,7.75-17.18,13.8-29.16,14.04-1.11,1.27-7.8,10.35-7.89,44.9,4.31-2.66,10.69-4.63,18.24-.83,6.82,3.44,9.26,11.16,10.14,16.17,1.53-5.67,3.54-11.25,6.01-16.69,5.49-12.04,9.8-24.02,12.82-35.62l.97-3.75ZM68.65,249.2l31.43,13.44c-.31-3.58-1.86-14.29-9.74-18.26-11.18-5.63-19.42,2.26-21.69,4.82ZM99.94,264.21l-32.35-13.83c-4.72,5.67-11.62,15.94-15.01,30.5-3.48,14.94-11.17,35.83-14,43.3,4.28-3.11,9.34-3.82,13.5-1.72,3.99,2.02,6.24,6.23,6.21,11.34,3.2-4.54,10.7-15.01,17.11-22.63.69-.82,1.56-1.78,2.57-2.88,6.17-6.74,17.65-19.27,21.01-39.2.28-1.63.61-3.26.96-4.88ZM56.71,335.35c.55-5.3-1.39-9.57-5.3-11.55-4.08-2.06-9.18-1.01-13.26,2.65l18.56,8.9ZM56.24,336.79l-18.93-9.08c-2.22,5.39-6.71,12.28-11.06,18.96-3.66,5.63-7.45,11.44-9.88,16.33-3.05,6.17-2.67,8.78-2.18,9.78.39.79,1.01.92,1.03.92.23.05.42.19.53.39s.13.43.04.64c-2.3,5.51-1.78,7.85-1.26,8.75.46.81,1.15.92,1.17.92.24.03.45.18.57.39.11.2.13.45.03.67-1.04,2.26-1.78,5.17-1.16,5.74.32.29.56.3.74.28,1.56-.22,3.95-4.05,6.48-8.11,4.76-7.61,11.26-18.05,20.26-18.39.55-.04,1.03.19,1.36.6.89,1.11.72,3.63-.61,8.71-.55,2.13.45,3.29,1.09,3.79.75.59,1.57.7,1.84.55.03-.01.06-.03.05-.17-.23-3.09,1.19-8.13,3.8-13.49.03-.07.07-.12.12-.18,1.21-2.62,1.7-6.98,2.22-11.59.68-6.04,1.38-12.28,3.75-16.41Z" />
      </g>
    </svg>
  )
}
export default BodyFront;