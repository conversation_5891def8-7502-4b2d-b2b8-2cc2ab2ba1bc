import React from 'react';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import { FormControlLabel, Grid, Stack } from "@mui/material";
import BodyImage from './BodyImage';
import ISelfAssessmentBodyPartConfig from '../models/ISelfAssessmentBodyPartConfig';
import IDiscomfort from '../models/IDiscomfort';
import { useList } from '@refinedev/core';
import MLCheckbox from '../../../components/ui/MLCheckbox/MLCheckbox';

interface DiscomfortProps {
  bodyPartConfigs: ISelfAssessmentBodyPartConfig[];
  discomfort: IDiscomfort;
  setDiscomfort: (discomfort: IDiscomfort) => void;
}

const Discomfort: React.FC<DiscomfortProps> = ({
  bodyPartConfigs,
  discomfort,
  setDiscomfort,
}) => {
  const { data: conditionsData, isLoading: conditionsDataLoading } =
    useList({
      resource: "self-assessment-conditions",
      filters: [{
        field: "showAsOption",
        operator: "eq",
        value: "true",
      }],
      meta: {
        populate: {
          canAffectBodyParts: {
            fields: ["bodyPart", "bodyPartText"]
          }
        }
      }
    });
  const conditions = conditionsData?.data ?? [];

  const handleDiscomfortCheckedChange = () => {
    const newDiscomfort = discomfort;
    newDiscomfort.isDiscomfortChecked = !newDiscomfort.isDiscomfortChecked;
    newDiscomfort.isDiscomfortChecked ? setDiscomfort({ isDiscomfortChecked: true }) : setDiscomfort({ ...newDiscomfort });
  };

  const handleConditionCheckedChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    condition: string,
    affectedBodyPart: string,
    affectsBodyPartText: string,
  ) => {
    const { checked } = event.target

    const newDiscomfort: IDiscomfort = { ...discomfort };

    if (!newDiscomfort.selectedCondition) {
      newDiscomfort.selectedCondition = {};
    }

    if (!newDiscomfort.selectedCondition[condition]) {
      newDiscomfort.selectedCondition[condition] = {
        isSelected: checked,
        affectsBodyPart: affectedBodyPart,
        affectsBodyPartText: affectsBodyPartText
      };
    } else {
      newDiscomfort.selectedCondition[condition].isSelected = checked;
    }

    setDiscomfort(newDiscomfort);
  }

  return (
    <Stack>
      <Stack
        marginBottom="60px"
        direction="column"
        gap={"20px"}
      >
        <Stack>
          <MLTypography
            variant="h2" marginTop="20px" display={"inline-block"}>
            Do you experience any discomfort in your body?
          </MLTypography>
          <FormControlLabel
            control={
              <MLCheckbox
                name="noDiscomfort"
                checked={discomfort.isDiscomfortChecked as boolean}
                onChange={handleDiscomfortCheckedChange}
              />
            }
            label="I have no discomfort"
          />
        </Stack>
        {!discomfort.isDiscomfortChecked ? (
          <Stack>
            <MLTypography
              variant="h4" display={"inline-block"}>
              Do you have any of the following conditions?
            </MLTypography>
            <Grid
              container
              sx={{
                maxWidth: "800px"
              }}
            >
              {conditions.map((data, index) => {
                return (
                  <Grid key={data.condition + index} item sm={4} xs={12}>
                    <FormControlLabel
                      control={
                        <MLCheckbox
                          name={data.condition}
                          checked={discomfort.selectedCondition?.[data.condition]?.isSelected ?? false}
                          onChange={(event) => handleConditionCheckedChange(
                            event,
                            data.condition,
                            data.canAffectBodyParts[0]?.bodyPart ?? "",
                            data.canAffectBodyParts[0]?.bodyPartText ?? ""
                          )}
                        />
                      }
                      label={data.condition}
                    />
                  </Grid>
                );
              })}
            </Grid>
          </Stack>
        ) : (<></>)}
      </Stack>

      {!discomfort.isDiscomfortChecked ? (
        <Grid container>
          <Grid item xs={12} sm={12} md={12} lg={6}>
            <MLTypography variant="h4" marginLeft="50px">
              Front
            </MLTypography>
            <Stack
              direction="row"
              justifyContent="center"
            >
              <BodyImage
                key="front-img"
                discomfort={discomfort}
                setDiscomfort={setDiscomfort}
                bodyPartConfigs={bodyPartConfigs}
                isFront={true}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={12} md={12} lg={6}>
            <MLTypography variant="h4" marginLeft="50px">
              Back
            </MLTypography>
            <Stack
              direction="row"
              justifyContent="center"
            >
              <BodyImage
                key="back-img"
                discomfort={discomfort}
                setDiscomfort={setDiscomfort}
                bodyPartConfigs={bodyPartConfigs}
                isFront={false}
              />
            </Stack>
          </Grid>
        </Grid>
      ) : (<></>)}
    </Stack>
  );
};

export default Discomfort;