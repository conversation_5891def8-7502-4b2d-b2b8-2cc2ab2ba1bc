import MLButton from "../../../../components/ui/MLButton/MLButton";
import { useNavigate } from "react-router-dom";

interface RetakeAssessmentProps {
  caseId: number | string | undefined;
}

const RetakeAssessment = ({
  caseId,
}: RetakeAssessmentProps) => {
  const navigate = useNavigate();

  const navigateToCreateNewCase = () => {
    navigate('/self-assessment/getting-started', { replace: true });
  }

  return (
    <>
      <MLButton
        onClick={navigateToCreateNewCase}
        variant="outlined"
        color="secondary"
      >
        Retake Self Assessment
      </MLButton>
    </>
  )
}
export default RetakeAssessment;