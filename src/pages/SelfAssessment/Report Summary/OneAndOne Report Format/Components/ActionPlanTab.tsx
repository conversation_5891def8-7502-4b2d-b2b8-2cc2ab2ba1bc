import React, { useState } from "react";
import { Box, Divider, Stack } from "@mui/material";
import MLTypography from "../../../../../components/ui/MLTypography/MLTypography";
import MLButton from "../../../../../components/ui/MLButton/MLButton";
import { ConsultScheduleSection } from "../../../Report/ReportStyleA";
import ScoreProgressBar from "./ScoreProgressBar";
import AskAQuestion from "../../../../Dashboard/AskAQuestion";
import AchievementBadge from "../../../../../assets/icons/AchievementBadge";

interface ActionPlanTabProps {
    ergoScore: number;
    achievedActionScore: number;
    actionPlans: Array<{
        title: string;
        outcome: string;
        text: string | string[];
        image: string;
        points: number;
        order?: number;
        isCompleted?: boolean;
    }>;
    isActionCompleted: (outcome: string) => boolean;
    onActionComplete: (outcome: string, points: number, isCurrentlyCompleted: boolean) => void;
}

const ActionPlanTab: React.FC<ActionPlanTabProps> = ({
    ergoScore,
    achievedActionScore,
    actionPlans,
    isActionCompleted,
    onActionComplete
}) => {
    const [isNeedHelpModalOpened, setIsNeedHelpModalOpened] = useState<boolean>(false)

    const handleNeedHelp = () => {
        setIsNeedHelpModalOpened(true);
    }

    return (
        <Stack mt={"55px"} gap={"60px"}>
            <Stack direction={{ lg: "row", xs: "column" }} gap={"20px"}>
                <Stack width={{ lg: "50%", xs: "100%" }}
                    gap={"10px"}>
                    <MLTypography
                        variant="h1"
                        fontSize={{ xs: "28px", sm: "40px" }}
                        fontWeight={600}
                        lineHeight={1}
                    >
                        Your quick Action Plan
                    </MLTypography>
                    <MLTypography
                        variant="body1"
                        fontSize={{ xs: "16px", sm: "20px" }}
                        lineHeight={"130%"}
                    >
                        Complete the following actions to improve your ergo score and health!
                    </MLTypography>
                </Stack>
                <Stack width={{ lg: "50%", xs: "100%" }}>
                    <ScoreProgressBar score={(ergoScore ?? 0) + (achievedActionScore ?? 0)} />
                </Stack>
            </Stack>

            {/* Action Plan Items */}
            <Stack gap="30px">
                {actionPlans?.sort((a, b) => (a.order || 0) - (b.order || 0)).map((action, index) => (
                    <Stack key={action.title} gap={"40px"}>
                        <Stack
                            key={`action-plan-${index}`}
                        >
                            {/* Header with number and title */}
                            <Stack
                                sx={{
                                    display: "flex",
                                    gap: "22px",
                                    flexDirection: "row",
                                    alignItems: { md: "baseline", xs: "center" },
                                    mb: 3
                                }}
                            >
                                <MLTypography
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: { md: "40px", xs: "32px" },
                                        fontWeight: 700,
                                        color: "#7856FF",
                                        lineHeight: 1
                                    }}
                                >
                                    0{index + 1}
                                </MLTypography>
                                <MLTypography
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: { sm: "24px", xs: "18px" },
                                        fontWeight: 600,
                                        lineHeight: 1
                                    }}
                                >
                                    {action.title}
                                </MLTypography>
                                {action.isCompleted &&
                                    <Box><AchievementBadge /></Box>
                                }
                            </Stack>

                            {/* Content section */}
                            <Stack
                                direction={{ xs: "column", sm: "row" }}
                                spacing={3}
                                alignItems={"flex-start"}
                            >
                                {/* Image */}
                                <Box
                                    sx={{
                                        position: "relative",
                                        width: { xs: "100%", sm: "240px", md: "280px" },
                                        maxWidth: "300px"
                                    }}
                                >
                                    <Box
                                        component="img"
                                        sx={{
                                            borderStyle: "solid",
                                            border: "0.5px solid #E0E0E0",
                                            backgroundColor: "#fcfcfc",
                                            borderRadius: "8px",
                                            overflow: "hidden",
                                            width: "100%",
                                            height: { xs: "auto", md: "182px" },
                                            objectFit: "contain",
                                        }}
                                        src={action.image}
                                        alt={action.title}
                                    />
                                </Box>

                                {/* Text and buttons */}
                                <Stack
                                    direction={{ sm: "column", md: "row" }}
                                    justifyContent="space-between"
                                    gap={"20px"}
                                    flex={1}
                                >
                                    {/* Action description */}
                                    <Stack width={{ md: "70%", xs: "100%" }}>
                                        {
                                            typeof action.text === "string" ? (
                                                <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                                                    <MLTypography
                                                        component="div"
                                                        variant='body1'
                                                        fontSize={{ md: "16px", xs: "14px" }}
                                                        fontWeight={400}
                                                        lineHeight={1.2}
                                                        sx={{
                                                            "& > div > ul > li": {
                                                                marginBottom: "8px"
                                                            },
                                                            "& > div > ul > li:last-child": {
                                                                marginBottom: 0,
                                                            }
                                                        }}
                                                    >
                                                        <div dangerouslySetInnerHTML={{ __html: action.text }} />
                                                    </MLTypography>
                                                </ul>
                                            ) : (
                                                <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                                                    {action.text.map((textPoint, key) => (
                                                        <li key={textPoint + key}>
                                                            <MLTypography
                                                                component="div"
                                                                variant='body1'
                                                                fontSize={{ md: "16px", xs: "14px" }}
                                                                fontWeight={400}
                                                                lineHeight={1.2}
                                                                sx={{
                                                                    "& > div > ul > li": {
                                                                        marginBottom: "8px"
                                                                    },
                                                                    "& > div > ul > li:last-child": {
                                                                        marginBottom: 0,
                                                                    }
                                                                }}
                                                            >
                                                                {textPoint}
                                                            </MLTypography>
                                                        </li>
                                                    ))}
                                                </ul>
                                            )
                                        }
                                    </Stack>

                                    {/* Action buttons section */}
                                    <Stack
                                        width={{ xs: "100%", md: "25%" }}
                                        spacing={2}
                                        alignItems={"center"}
                                        justifyContent="space-between"
                                    >
                                        <MLTypography
                                            variant="body1"
                                            fontSize="14px"
                                            fontWeight={400}
                                            lineHeight={1.2}
                                        >
                                            Add{" "}
                                            <span
                                                style={{
                                                    fontWeight: 600,
                                                    fontSize: "16px",
                                                    lineHeight: 1.2,
                                                    color: "#31C100",
                                                }}
                                            >
                                                {action.points} points
                                            </span>
                                            {" "}to your ergo score
                                        </MLTypography>
                                        <Stack width={{ sm: "auto", md: "100%" }} gap="25px">
                                            <Stack>
                                                {!isActionCompleted(action.outcome) ? (
                                                    <MLButton
                                                        variant="contained"
                                                        color="secondary"
                                                        onClick={() => onActionComplete(action.outcome, action.points, false)}
                                                    >
                                                        Mark As Done
                                                    </MLButton>
                                                ) : (
                                                    <Stack gap="7px">
                                                        <MLButton
                                                            variant="outlined"
                                                            color="secondary"
                                                            sx={{
                                                                backgroundColor: "#98E080",
                                                                border: "1px solid black",
                                                                color: "black",
                                                                '&:hover': {
                                                                    backgroundColor: "#98E080",
                                                                    border: "1px solid black",
                                                                    color: "black"
                                                                }
                                                            }}
                                                        >
                                                            Completed
                                                        </MLButton>
                                                        {isActionCompleted(action.outcome) && (
                                                            <MLButton
                                                                variant="text"
                                                                color="primary"
                                                                onClick={() => onActionComplete(action.outcome, action.points, true)}
                                                                sx={{
                                                                    textTransform: "none",
                                                                    fontSize: "14px",
                                                                    padding: { xs: "8px 16px", sm: 0 },
                                                                    fontWeight: 600,
                                                                    '&:hover': {
                                                                        backgroundColor: "transparent",
                                                                        color: "#5E3DCF"
                                                                    }
                                                                }}
                                                            >
                                                                Reset this task
                                                            </MLButton>
                                                        )}
                                                    </Stack>
                                                )}
                                            </Stack>
                                            <MLButton
                                                variant="outlined"
                                                disabled={isActionCompleted(action.outcome)}
                                                onClick={() => setIsNeedHelpModalOpened(true)}
                                            >
                                                Need help?
                                            </MLButton>
                                        </Stack>
                                    </Stack>
                                </Stack>
                            </Stack>
                        </Stack>
                        {index < actionPlans.length - 1 &&
                            <Divider sx={{ borderColor: "#E0E0E0" }} />
                        }
                    </Stack>
                ))}
            </Stack>

            <ConsultScheduleSection
                isLeftBorder={true}
                hasContainer={false}
                handleNeedHelp={handleNeedHelp}
            />
            <AskAQuestion
                openAskAQuestionModal={isNeedHelpModalOpened}
                handleClose={() => setIsNeedHelpModalOpened(false)}
                isActionPlan={false}
            />
        </Stack>
    );
};

export default ActionPlanTab;
