import React, { useState } from "react";
import { Box, Stack, Grid, useTheme, useMediaQuery } from "@mui/material";
import MLTypography from "../../../../../components/ui/MLTypography/MLTypography";
import MLContainer from "../../../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";
import { IReportData } from "../../../Report/Report";
import IDiscomfort from "../../../models/IDiscomfort";
import BodyFront from "../../../Discomfort/BodyFront";
import BodyBack from "../../../Discomfort/BodyBack";
import ErgoIssueAndRecommendations from "./ErgoIssueAndRecommendations";
import MLButton from "../../../../../components/ui/MLButton/MLButton";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

interface ErgoRiskAnalysisTabProps {
    report: IReportData;
}

const ErgoRiskAnalysisTab: React.FC<ErgoRiskAnalysisTabProps> = ({
    report,
}) => {
    const [showAllMobile, setShowAllMobile] = useState(false);
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

    const goodHabits = report?.goodHabits || [];
    const habitsToShow = isMobile && !showAllMobile ? goodHabits.slice(0, 2) : goodHabits;
    // Counter for the risk parts
    let bodyPartCounter = 1;

    const lowRiskParts = Object.entries(report?.potentialRiskPart ?? {}).filter(
        ([, value]) => value.riskLevel === "low",
    );

    const mediumRiskParts = Object.entries(
        report?.potentialRiskPart ?? {},
    ).filter(([, value]) => value.riskLevel === "medium");

    const highRiskParts = Object.entries(
        report?.potentialRiskPart ?? {},
    ).filter(([, value]) => value.riskLevel === "high");

    return (
        <Stack mt={"30px"} gap="60px">
            {/* Good Habits Section start */}
            <MLContainer>
                <Box>
                    <Stack >
                        <Stack direction="row" alignItems="center" spacing={1}>
                            <MLTypography
                                variant="h1"
                                lineHeight={1.2}
                                fontFamily="Syne"
                                fontSize="60px" fontWeight={600} color="#7856FF"
                            >
                                {goodHabits.length}
                            </MLTypography>
                            <MLTypography
                                variant="h1"
                                fontFamily="Syne"
                                fontSize="32px"
                                fontWeight={600}
                                lineHeight={1.2}
                            >
                                Good habits
                            </MLTypography>
                        </Stack>
                        <Stack
                            direction="row"
                            alignItems="center"
                            spacing={3}
                            marginLeft={1}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="8" height="68" viewBox="0 0 8 68" fill="none">
                                <path d="M3.64645 67.3536C3.84171 67.5488 4.15829 67.5488 4.35355 67.3536L7.53553 64.1716C7.7308 63.9763 7.7308 63.6597 7.53553 63.4645C7.34027 63.2692 7.02369 63.2692 6.82843 63.4645L4 66.2929L1.17157 63.4645C0.976311 63.2692 0.659728 63.2692 0.464466 63.4645C0.269204 63.6597 0.269204 63.9763 0.464466 64.1716L3.64645 67.3536ZM4 0L3.5 0L3.5 67H4H4.5L4.5 0L4 0Z" fill="#7856FF" />
                            </svg>
                            <MLTypography maxWidth="300px" variant="body1" fontSize="18px">
                                Great work! Keep up these good habits.
                            </MLTypography>
                        </Stack>
                    </Stack>
                    {/* Good Habits Grid */}
                    <Grid container spacing={2} >
                        {habitsToShow.map((goodhabit, index) => (
                            <Grid key={index + goodhabit.title} item lg={4} md={6} xs={6}>
                                <Stack gap={1} direction="column" alignItems="flex-start">
                                    <Stack direction="row" gap={1} alignItems="center">
                                        <Box
                                            sx={{
                                                backgroundColor: "#DFFF30",
                                                borderRadius: "50px",
                                                width: "40px",
                                                height: "40px",
                                            }}
                                        >
                                            <Stack sx={{ height: "100%", justifyContent: "center", alignItems: "center" }}>
                                                <MLTypography variant="h1" fontSize="32px" fontWeight={600} lineHeight={0.7}>
                                                    {`${index + 1}.`}
                                                </MLTypography>
                                            </Stack>
                                        </Box>
                                        <Box
                                            component="img"
                                            src={goodhabit.image || ""}
                                            alt="good habit icon"
                                            sx={{
                                                height: { sm: "100px", xs: "80px" },
                                                width: { sm: "100px", xs: "80px" },
                                            }}
                                        />
                                    </Stack>
                                    <MLTypography fontSize="16px" fontWeight={600}>
                                        {goodhabit.title}
                                    </MLTypography>
                                    <MLTypography
                                        fontSize="14px"
                                        fontWeight={400}
                                        dangerouslySetInnerHTML={{ __html: goodhabit.text }}
                                    />
                                </Stack>
                            </Grid>
                        ))}
                    </Grid>

                    {/* View All Button for Mobile */}
                    {isMobile && goodHabits.length > 2 && (
                        <Stack>
                            <MLButton
                                onClick={() => setShowAllMobile(!showAllMobile)}
                                sx={{
                                    mt: 4,
                                    display: "flex",
                                    borderRadius: "8px",
                                    px: 4,
                                }}
                                variant="outlined"
                                endIcon={showAllMobile ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                            >
                                {showAllMobile ? "VIEW LESS" : "VIEW ALL"}
                            </MLButton>
                        </Stack>
                    )}
                </Box>
            </MLContainer>
            {/* Good Habits Section end */}
            {/* Good Habits Section end */}

            {/* Body Parts at Risk Section start */}
            <Stack
                sx={{
                    paddingY: "30px",
                    backgroundColor: "#f1f1f1",
                    display: {
                        xs: "none",
                        lg: "flex",
                    },
                }}
            >
                <MLContainer>
                    <Stack
                        direction={{ xs: "column", sm: "row" }}
                        gap={"45px"}
                    >
                        <Stack direction={"column"} gap={"30px"}>
                            <Stack>
                                <MLTypography
                                    variant="body1"
                                    fontSize={"25px"}
                                    lineHeight={1.2}
                                    fontWeight={500}
                                    marginBottom={"20px"}
                                >
                                    Potential risk of injury by body area
                                </MLTypography>
                                {highRiskParts.length > 0 && (
                                    <Stack gap={"10px"}>
                                        <Stack
                                            direction="row"
                                            sx={{
                                                width: "fit-content",
                                                backgroundColor: "#C40000",
                                                borderRadius: "34px",
                                                paddingX: "35px",
                                                paddingY: "8px",
                                            }}
                                        >
                                            <MLTypography
                                                variant="body1"
                                                fontSize={"14px"}
                                                fontWeight={600}
                                                lineHeight={1}
                                                color={"white"}
                                            >
                                                High Risk
                                            </MLTypography>
                                        </Stack>
                                        {highRiskParts.map(([bodyPart], index) => (
                                            <Stack
                                                key={`high-${index}`}
                                                direction={"row"}
                                                spacing={"20px"}
                                            >
                                                <MLTypography
                                                    variant="h1"
                                                    fontSize={"36px"}
                                                    fontWeight={500}
                                                    lineHeight={1}
                                                >
                                                    {(bodyPartCounter++).toString().padStart(2, "0")}.
                                                </MLTypography>
                                                <MLTypography
                                                    variant="h1"
                                                    fontSize={"36px"}
                                                    fontWeight={500}
                                                    lineHeight={1}
                                                >
                                                    <a
                                                        href={`#${bodyPart}`}
                                                        style={{
                                                            color: "#704bf4",
                                                            textDecoration: "none",
                                                        }}
                                                    >
                                                        {bodyPart}
                                                    </a>
                                                </MLTypography>
                                            </Stack>
                                        ))}
                                    </Stack>
                                )}
                            </Stack>
                            <Stack>
                                {mediumRiskParts.length > 0 && (
                                    <Stack gap={"10px"}>
                                        <Stack
                                            direction="row"
                                            sx={{
                                                width: "fit-content",
                                                backgroundColor: "#FF7A00",
                                                borderRadius: "34px",
                                                paddingX: "35px",
                                                paddingY: "8px",
                                            }}
                                        >
                                            <MLTypography
                                                variant="body1"
                                                fontSize={"14px"}
                                                fontWeight={600}
                                                lineHeight={1}
                                                color={"black"}
                                            >
                                                Medium Risk
                                            </MLTypography>
                                        </Stack>
                                        {mediumRiskParts.map(([bodyPart], index) => (
                                            <Stack
                                                key={`medium-${index}`}
                                                direction={"row"}
                                                spacing={"20px"}
                                            >
                                                <MLTypography
                                                    variant="h1"
                                                    fontSize={"36px"}
                                                    fontWeight={500}
                                                    lineHeight={1}
                                                >
                                                    {(bodyPartCounter++).toString().padStart(2, "0")}.
                                                </MLTypography>
                                                <MLTypography
                                                    variant="h1"
                                                    fontSize={"36px"}
                                                    fontWeight={500}
                                                    lineHeight={1}
                                                >
                                                    <a
                                                        href={`#${bodyPart}`}
                                                        style={{
                                                            color: "#704bf4",
                                                            textDecoration: "none",
                                                        }}
                                                    >
                                                        {bodyPart}
                                                    </a>
                                                </MLTypography>
                                            </Stack>
                                        ))}
                                    </Stack>
                                )}
                            </Stack>
                            <Stack>
                                {lowRiskParts.length > 0 && (
                                    <Stack gap={"6px"}>
                                        <Stack
                                            direction="row"
                                            sx={{
                                                width: "fit-content",
                                                backgroundColor: "#FFC700",
                                                borderRadius: "34px",
                                                paddingX: "35px",
                                                paddingY: "8px",
                                            }}
                                        >
                                            <MLTypography
                                                variant="body1"
                                                fontSize={"14px"}
                                                fontWeight={600}
                                                lineHeight={1}
                                                color={"black"}
                                            >
                                                Low Risk
                                            </MLTypography>
                                        </Stack>
                                        {lowRiskParts.map(([bodyPart], index) => (
                                            <Stack
                                                key={`low-${index}`}
                                                direction={"row"}
                                                spacing={"20px"}
                                            >
                                                <MLTypography
                                                    variant="h1"
                                                    fontSize={"36px"}
                                                    fontWeight={500}
                                                    lineHeight={1}
                                                >
                                                    {(bodyPartCounter++).toString().padStart(2, "0")}.
                                                </MLTypography>
                                                <MLTypography
                                                    variant="h1"
                                                    fontSize={"36px"}
                                                    fontWeight={500}
                                                    lineHeight={1}
                                                >
                                                    <a
                                                        href={`#${bodyPart}`}
                                                        style={{
                                                            color: "#704bf4",
                                                            textDecoration: "none",
                                                        }}
                                                    >
                                                        {bodyPart}
                                                    </a>
                                                </MLTypography>
                                            </Stack>
                                        ))}
                                    </Stack>
                                )}
                            </Stack>
                        </Stack>
                        <Stack direction={{ xs: "column", sm: "row" }} gap={"20px"}>
                            <BodyFront
                                selectedParts={{ ...report.reportGeneratedBodyParts }}
                            />
                            <BodyBack
                                selectedParts={{ ...report.reportGeneratedBodyParts }}
                            />
                        </Stack>
                    </Stack>
                </MLContainer>
            </Stack>
            {/* Body Parts at Risk Section end */}

            {/*  Ergo issues and recommendations Section start */}
            <MLContainer>
                <ErgoIssueAndRecommendations report={report} />
            </MLContainer>
            {/*  Ergo issues and recommendations Section end */}
        </Stack>
    );
};

export default ErgoRiskAnalysisTab;