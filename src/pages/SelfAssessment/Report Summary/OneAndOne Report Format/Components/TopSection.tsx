import React, { useState } from "react";
import { <PERSON>, <PERSON>ack, Grid, Di<PERSON>r, <PERSON>con<PERSON><PERSON>on, useMedia<PERSON><PERSON>y, Toolt<PERSON> } from "@mui/material";
import MLTypography from "../../../../../components/ui/MLTypography/MLTypography";
import Markdown from "react-markdown";
import HorizontalScoreMeter from "./HorizontalScoreMeter";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { theme } from "../../../../../theme";
import ConditionToolTipIcon from "../../../../../assets/reportIcons/ConditionToolTipIcon";
import { DownloadViewPdfReport } from "../../../Report/components/DownloadViewPdfReport";
import RetakeAssessment from "../../../Report/components/RetakeAssessment";
import { User } from "../../../../../contexts/authContext/AuthContext";
import { IGeneratedReport } from "../../../Report/Report";
import MLContainer from "../../../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";

interface TopSectionProps {
    ergoScore: number;
    ergonomicScoreText: string;
    isAiAssessment?: boolean;
    aiCaseImages?: {
        chairImage?: { url: string };
        chairDeskImage?: { url: string };
    };
    reportPdfUrl: string;
    isPdfGenerating: boolean;
    caseId: number | string | undefined;
    isLoggedUserIsEmployee: boolean;
    loggedUserDetails: User | undefined
    isEmployeeView: boolean
    generatedReport: IGeneratedReport[];
}

const TopSection: React.FC<TopSectionProps> = ({
    ergoScore,
    ergonomicScoreText,
    isAiAssessment = false,
    aiCaseImages = {},
    reportPdfUrl,
    isPdfGenerating,
    caseId,
    isLoggedUserIsEmployee,
    loggedUserDetails,
    isEmployeeView,
    generatedReport,
}) => {
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
    const [showExplanation, setShowExplanation] = useState(false);

    return (
        <Stack direction={{ xs: "column", lg: "row" }}
            sx={{
                marginBottom: "30px",
                gap: "28px"
            }}
        >
            {/* Ergo Score Section */}
            <Stack flex={8}>
                <Stack
                    sx={{
                        backgroundColor: "#6C4AF4",
                        borderRadius: "10px",
                        padding: { xs: 2, sm: 3 },
                    }}
                >
                    {/* Top Row - desktop */}
                    <Stack direction="row"
                        sx={{
                            display: { xs: "none", sm: "flex" },
                            gap: { xs: "20px", md: "80px" },
                        }}
                    >
                        {/* Horizontal Score Meter */}
                        <Stack width={"200px"}>
                            <Stack
                                sx={{
                                    position: "absolute",
                                    width: "200px",
                                    height: "200px",
                                }}
                            >
                                <HorizontalScoreMeter isHorizontal={true} percentage={ergoScore} />
                            </Stack>
                        </Stack>

                        <Stack gap="10px">
                            {
                                !isLoggedUserIsEmployee &&
                                <Stack gap={"6px"} direction={"row"} alignItems={"center"}>
                                    <MLTypography
                                        variant="body1"
                                        fontSize={"20px"}
                                        color={"#DFFF30"}
                                        lineHeight={1}
                                    >
                                        Assessment report for:
                                    </MLTypography>
                                    <MLTypography
                                        variant="body1"
                                        fontSize={"20px"}
                                        fontWeight={600}
                                        lineHeight={1}
                                        color={"#DFFF30"}
                                    >
                                        {generatedReport[0].generatedReport.username.toUpperCase()}
                                    </MLTypography>
                                </Stack>
                            }
                            <Stack spacing={2}>
                                <Stack direction={"row"}
                                    sx={{
                                        alignItems: "center",
                                        gap: "24px",
                                    }}
                                >
                                    <MLTypography
                                        variant="h1"
                                        fontSize={{ xs: "24px", md: "28px" }}
                                        fontWeight={600}
                                        color="white"
                                    >
                                        Your Ergo Score
                                    </MLTypography>
                                    <Tooltip
                                        title={
                                            <div>
                                                <strong>What it means?</strong><br></br>
                                                Ergo score calculated based on workspace factors, their risk levels, and importance to overall ergonomic health.
                                            </div>
                                        }
                                        placement={"right-end"}
                                        slotProps={{
                                            tooltip: {
                                                sx: {
                                                    bgcolor: "#E4E4E4",
                                                    color: "rgba(0, 0, 0, 0.87)",
                                                    fontSize: "12px",
                                                    p: "15px",
                                                    borderRadius: "10px",
                                                },
                                            },
                                        }}
                                        sx={{
                                            display: { xs: "none", sm: "flex" }
                                        }}
                                    >
                                        <Stack>
                                            <ConditionToolTipIcon color="white" />
                                        </Stack>
                                    </Tooltip>
                                </Stack>

                                <Stack direction={{ xs: "column", sm: "row" }} gap={{ xs: "20px", md: "30px" }}>
                                    <Stack direction="row" alignItems="baseline">
                                        <MLTypography
                                            variant="h1"
                                            fontSize={{ xs: "56px", md: "64px" }}
                                            fontWeight={600}
                                            lineHeight={0.8}
                                            color="white"
                                        >
                                            {ergoScore}
                                        </MLTypography>
                                        <MLTypography
                                            variant="h1"
                                            fontSize={{ xs: "24px", md: "32px" }}
                                            fontWeight={600}
                                            lineHeight={1}
                                            color="white"
                                            marginLeft={0.5}
                                        >
                                            /100
                                        </MLTypography>
                                    </Stack>
                                    <Divider
                                        orientation="vertical"
                                        flexItem
                                        sx={{
                                            backgroundColor: "white",
                                            height: "60px",
                                            display: { xs: "none", sm: "block" },
                                        }}
                                    />
                                    <Stack>
                                        <MLTypography
                                            variant="body1"
                                            fontSize={{ xs: "14px", md: "20px" }}
                                            fontWeight={500}
                                            color="white"
                                        >
                                            Higher score =
                                        </MLTypography>
                                        <MLTypography
                                            variant="body1"
                                            fontSize={{ xs: "14px", md: "20px" }}
                                            fontWeight={500}
                                            color="#DFFF30"
                                        >
                                            Better Ergonomics
                                        </MLTypography>
                                    </Stack>
                                </Stack>
                            </Stack>
                        </Stack>
                    </Stack>

                    {/* Top Row - mobile */}
                    <Stack direction="column"
                        marginTop={"0px"}
                        sx={{
                            display: { xs: "flex", sm: "none" },
                            gap: { xs: "20px", lg: "30px", xl: "80px" },
                        }}
                    >
                        {
                            !isLoggedUserIsEmployee &&
                            <Stack gap={"2px"} direction={"row"} alignItems={"center"}>
                                <MLTypography
                                    variant="body1"
                                    fontSize={"20px"}
                                    color={"#DFFF30"}
                                    lineHeight={1}
                                >
                                    Assessment report for:
                                </MLTypography>
                                <MLTypography
                                    variant="body1"
                                    fontSize={"20px"}
                                    fontWeight={600}
                                    lineHeight={1}
                                    color={"#DFFF30"}
                                >
                                    {generatedReport[0].generatedReport.username.toUpperCase()}
                                </MLTypography>
                            </Stack>
                        }
                        <MLTypography
                            variant="h1"
                            fontSize={{ xs: "24px", md: "28px" }}
                            fontWeight={600}
                            color="white"
                        >
                            Your Ergo Score
                        </MLTypography>

                        <Stack direction={"row"} height={"170px"}>
                            {/* vertical Score Meter */}
                            <Stack width={"128px"} >
                                <Stack
                                    sx={{
                                        position: "absolute",
                                        width: "170px",
                                        height: "170px",
                                        marginLeft: "-72px",
                                    }}
                                >
                                    <HorizontalScoreMeter isHorizontal={false} percentage={ergoScore} />
                                </Stack>
                            </Stack>

                            <Stack direction={{ xs: "column", sm: "row" }} gap={"15px"}>
                                <Stack direction="row" alignItems="baseline">
                                    <MLTypography
                                        variant="h1"
                                        fontSize={"64px"}
                                        fontWeight={600}
                                        lineHeight={1}
                                        color="white"
                                    >
                                        {ergoScore}
                                    </MLTypography>
                                    <MLTypography
                                        variant="h1"
                                        fontSize={"32px"}
                                        fontWeight={600}
                                        lineHeight={1}
                                        color="white"
                                        marginLeft={0.5}
                                    >
                                        /100
                                    </MLTypography>
                                </Stack>
                                <Divider
                                    flexItem
                                    sx={{
                                        backgroundColor: "white",
                                    }}
                                />
                                <Stack>
                                    <MLTypography
                                        variant="body1"
                                        fontSize="16px"
                                        fontWeight={500}
                                        color="white"
                                    >
                                        Higher score =
                                    </MLTypography>
                                    <MLTypography
                                        variant="body1"
                                        fontSize="16px"
                                        fontWeight={500}
                                        color="#DFFF30"
                                    >
                                        Better Ergonomics
                                    </MLTypography>
                                </Stack>
                            </Stack>
                        </Stack>
                    </Stack>

                    <Stack
                        direction={{ xs: "column", sm: "row" }}
                        marginTop={"30px"}
                        gap={"12px"}
                        sx={{
                            alignItems: { xs: "stretch", sm: "center" },
                        }}
                    >
                        <DownloadViewPdfReport reportPdfUrl={reportPdfUrl} isPdfGenerating={isPdfGenerating} />
                        {
                            (isLoggedUserIsEmployee || loggedUserDetails?.employeeView || isEmployeeView) &&
                            <RetakeAssessment caseId={caseId} />
                        }
                    </Stack>

                    {/* Know More / Less Toggle */}
                    {isMobile &&
                        <Stack spacing={2} marginTop={"20px"}>
                            {showExplanation && (
                                <>
                                    <MLTypography
                                        variant="body1"
                                        fontSize={"18px"}
                                        fontWeight={600}
                                        lineHeight={"27px"}
                                        color={"white"}
                                    >
                                        What it Means?
                                    </MLTypography>
                                    <MLTypography
                                        variant="body1"
                                        fontSize={{ xs: "16px", sm: "18px" }}
                                        fontWeight={300}
                                        lineHeight={"27px"}
                                        color={"white"}
                                    >
                                        Ergo Score is calculated after comparing the current furniture,
                                        equipment and posture against ergonomics standards and best
                                        practices.
                                    </MLTypography>
                                    <Stack>
                                        <Markdown
                                            components={{
                                                p: ({ children }) => (
                                                    <MLTypography
                                                        variant="body1"
                                                        fontSize={{ xs: "16px", sm: "18px" }}
                                                        fontWeight={300}
                                                        color={"white"}
                                                    >
                                                        {children}
                                                    </MLTypography>
                                                ),
                                            }}
                                        >
                                            {ergonomicScoreText}
                                        </Markdown>
                                    </Stack>
                                </>
                            )}

                            <Stack direction="row" justifyContent="center" alignItems="center" spacing={1}>
                                <MLTypography
                                    variant="body1"
                                    fontSize="14px"
                                    fontWeight={500}
                                    color="white"
                                    sx={{ cursor: "pointer" }}
                                    onClick={() => setShowExplanation((prev) => !prev)}
                                >
                                    {showExplanation ? "Know Less" : "Know More"}
                                </MLTypography>
                                <IconButton
                                    onClick={() => setShowExplanation((prev) => !prev)}
                                    sx={{ color: "white", padding: 0 }}
                                >
                                    {showExplanation ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                                </IconButton>
                            </Stack>
                        </Stack>
                    }
                </Stack>
            </Stack>
            {/* Uploaded Images Section */}
            {generatedReport[0].generatedReport.uploadedImages && (
                <Stack flex={4} gap={"20px"}
                    sx={{
                        justifyContent: "center",
                    }}
                >
                    <MLTypography variant="h1" fontSize={"24px"} fontWeight={700} lineHeight={1.2}>
                        Uploaded Images
                    </MLTypography>
                    <Stack
                        direction={"row"}
                        gap={"10px"}
                    >
                        {(generatedReport[0].generatedReport.uploadedImages.map((img) => (
                            <Box
                                key={img}
                                component="img"
                                sx={{
                                    border: "0.5px solid #9C9C9C",
                                    borderRadius: "10px",
                                    maxWidth: "30%",
                                    width: { xs: "150px", md: "150px" },
                                    maxHeight: { xs: "150px", sm: "180px" },
                                    height: "auto",
                                    objectFit: "cover",
                                }}
                                src={img}
                                alt="AI analysis chair photo"
                            />
                        )))}
                    </Stack>
                </Stack>
            )}
        </Stack>
    );
};

export default TopSection;
