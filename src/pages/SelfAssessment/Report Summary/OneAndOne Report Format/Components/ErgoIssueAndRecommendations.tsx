import React, { useState } from 'react'
import MLTypography from '../../../../../components/ui/MLTypography/MLTypography';
import { Box, Divider, Grid, Stack, useMediaQuery, useTheme } from '@mui/material';
import { IProduct, IReportData } from '../../../Report/Report';
import MLButton from '../../../../../components/ui/MLButton/MLButton';
import cross from "../../../../../assets/reportIcons/cross.png";
import tick from "../../../../../assets/reportIcons/tick.png";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ReadMoreRightArrow from '../../../../../assets/icons/ReadMoreRightArrow';
import { transformToIssueBasedReport } from '../../../Report/reportUtils';

interface ErgoIssueAndRecommendationsProps {
    report: IReportData
}

const ErgoIssueAndRecommendations: React.FC<ErgoIssueAndRecommendationsProps> = ({ report }) => {
    const [expanded, setExpanded] = useState(false);
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

    const imageContainerWidth = {
        xs: "160px",
        sm: "242px",
        md: "280px",
    };

    const imageHeight = {
        xs: "auto",
        sm: "220px",
        md: "180px",
        lg: "250px",
    };

    const issueData = transformToIssueBasedReport(report)
    return (
        <Stack gap="30px">
            {/* Title */}
            <MLTypography
                fontSize={{ xs: "24px", md: "32px" }}
                fontWeight={700}
                variant="h1"
            >
                Ergo issues and recommendations
            </MLTypography>
            {issueData.issueBasedData.map((data, index) => (
                <Stack key={index} gap="50px">
                    <Box>
                        {/* Title & Risk Level */}
                        <Stack
                            direction={{ xs: "column", sm: "row" }}
                            justifyContent="space-between"
                            alignItems={{ xs: "flex-start", sm: "center" }}
                            mb="36px"
                        >
                            <Stack direction="row" gap="22px" alignItems="baseline" >
                                <MLTypography
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: { md: "40px", xs: "32px" },
                                        fontWeight: 700,
                                        color: "#7856FF",
                                        lineHeight: 1,
                                    }}
                                >
                                    0{index + 1}
                                </MLTypography>
                                <MLTypography
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: "24px",
                                        fontWeight: 600,
                                        lineHeight: 1,
                                    }}
                                >
                                    {data.title}
                                </MLTypography>
                            </Stack>

                            {/* Risk Level Bar */}
                            <Box display="flex" alignItems="center" gap={2}>
                                <MLTypography fontSize="14px">Risk Level</MLTypography>
                                <Box display="flex" width="120px" height="12px">
                                    {[0, 1, 2].map((segment) => (
                                        <Box
                                            key={segment}
                                            sx={{
                                                flex: 1,
                                                backgroundColor:
                                                    data.riskLevel === "high" && segment < 3
                                                        ? "#FF6B6B"
                                                        : data.riskLevel === "medium" && segment < 2
                                                            ? "#FF6B6B"
                                                            : data.riskLevel === "low" && segment < 1
                                                                ? "#FF6B6B"
                                                                : "#E0E0E0",
                                                // backgroundColor: "#FF6B6B",
                                                mx: segment === 1 ? "2px" : 0,
                                                borderTopLeftRadius: segment === 0 ? "6px" : 0,
                                                borderBottomLeftRadius: segment === 0 ? "6px" : 0,
                                                borderTopRightRadius: segment === 2 ? "6px" : 0,
                                                borderBottomRightRadius: segment === 2 ? "6px" : 0,
                                                transition: "background-color 0.3s",
                                            }}
                                        />
                                    ))}
                                </Box>
                            </Box>
                        </Stack>
                        {/* Risks & Recommendations */}
                        <Stack direction={{ md: "row", sm: "column" }} gap={{ md: "30px", xs: "20px" }}  >
                            {/* Left Side: Bad/Good Postures */}
                            <Stack
                                sx={{
                                    flex: 1,
                                    borderRadius: "8px",
                                    width: { xs: "100%", md: "66.66%" }
                                }}
                            >
                                <Stack gap="30px">
                                    {/* Bad Posture */}
                                    <Stack direction="row" gap="30px">
                                        <Stack
                                            flex={1}
                                            gap={{ lg: "20px", xs: "10px" }}
                                            sx={{
                                                width: imageContainerWidth,
                                                // maxWidth: "300px",
                                                display: "flex",
                                                flexDirection: "column",
                                            }}
                                        >
                                            <Box sx={{ position: "relative", width: "100%" }}>
                                                <Stack
                                                    sx={{
                                                        position: "absolute",
                                                        top: -10,
                                                        right: -10,
                                                        width: { md: "35px", xs: "30px" },
                                                        zIndex: 10,
                                                    }}
                                                >
                                                    <img src={cross} />
                                                </Stack>
                                                <Box
                                                    component="img"
                                                    sx={{
                                                        border: "0.5px solid #E0E0E0",
                                                        backgroundColor: "#fcfcfc",
                                                        borderRadius: "8px",
                                                        overflow: "hidden",
                                                        width: "100%",
                                                        height: imageHeight,
                                                        objectFit: "contain",
                                                    }}
                                                    src={data.optionConfig.optionImage}
                                                    alt="Bad Posture"
                                                />
                                            </Box>
                                        </Stack>

                                        {/* Bad points */}
                                        <Box sx={{ flex: 1 }} >
                                            {typeof data.optionConfig.overallRisk === "string" && (
                                                <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                                                    {data.optionConfig.overallRisk.split('\n').filter((line: any) => line.trim())
                                                        .map((line: any, index: number) => (
                                                            <li key={index}>
                                                                <MLTypography
                                                                    component="div"
                                                                    variant='body1'
                                                                    fontSize={{ md: "16px", xs: "14px" }}
                                                                    fontWeight={400}
                                                                    lineHeight={1.2}
                                                                >
                                                                    {line}
                                                                </MLTypography>
                                                            </li>
                                                        ))}
                                                </ul>
                                            )}
                                        </Box>
                                    </Stack>

                                    {/* Good Posture (conditionally rendered) */}
                                    <Stack direction="row" gap="30px">
                                        <Stack
                                            flex={1}
                                            gap={{ lg: "20px", xs: "10px" }}
                                            sx={{
                                                width: imageContainerWidth,
                                                // maxWidth: "300px",
                                                display: "flex",
                                                flexDirection: "column",
                                            }}
                                        >
                                            <Box sx={{ position: "relative", width: "100%" }}>
                                                <Stack
                                                    sx={{
                                                        position: "absolute",
                                                        top: -10,
                                                        right: -10,
                                                        width: { md: "35px", xs: "30px" },
                                                        zIndex: 10,
                                                    }}
                                                >
                                                    <img src={tick} />
                                                </Stack>
                                                <Box
                                                    component="img"
                                                    sx={{
                                                        border: "0.5px solid #E0E0E0",
                                                        backgroundColor: "#fcfcfc",
                                                        borderRadius: "8px",
                                                        overflow: "hidden",
                                                        width: "100%",
                                                        height: imageHeight,
                                                        objectFit: "contain",
                                                    }}
                                                    src={data.image}
                                                    alt="Good Posture"
                                                />
                                            </Box>
                                        </Stack>

                                        {/* Good points */}
                                        <Box sx={{ flex: 1 }}>
                                            {
                                                typeof data.description === "string" && (
                                                    <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                                                        <MLTypography
                                                            component="div"
                                                            variant='body1'
                                                            fontSize={{ md: "16px", xs: "14px" }}
                                                            fontWeight={400}
                                                            lineHeight={1.2}
                                                            sx={{
                                                                "& > div > ul > li": {
                                                                    marginBottom: "8px"
                                                                },
                                                                "& > div > ul > li:last-child": {
                                                                    marginBottom: 0,
                                                                }
                                                            }}
                                                        >
                                                            <div dangerouslySetInnerHTML={{ __html: data.description }} />
                                                        </MLTypography>
                                                    </ul>
                                                )
                                            }
                                        </Box>
                                    </Stack>
                                </Stack>
                            </Stack>

                            {/* Right Side: Risk & Products (conditionally rendered) */}
                            {(expanded || !isMobile) && (
                                <Stack
                                    sx={{
                                        borderRadius: "8px",
                                        width: { xs: "100%", md: "33.33%" }
                                    }}
                                >
                                    <Stack gap={"30px"}>
                                        {/* Body Parts at Risk */}
                                        {data.affectedBodyParts.length > 0 &&
                                            <Box>
                                                <MLTypography
                                                    fontFamily="Syne"
                                                    fontWeight={600}
                                                    fontSize="20px"
                                                    mb={"25px"}
                                                >
                                                    Body Parts at Risk
                                                </MLTypography>
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        gap: "20px",
                                                        flexWrap: "wrap",
                                                        justifyContent: "flex-start",
                                                    }}
                                                >
                                                    {data.affectedBodyParts && data?.affectedBodyParts.map((item: any, index: number) => (
                                                        <Stack key={index} alignItems="center" spacing={1}>
                                                            <Box
                                                                sx={{
                                                                    width: "100px",
                                                                    height: "100px",
                                                                    border: `2px solid #C1C1C1`,
                                                                    borderRadius: "50%",
                                                                    display: "flex",
                                                                    justifyContent: "center",
                                                                    alignItems: "center",
                                                                    background: "#FFF",
                                                                }}
                                                            >
                                                                {item.conditionImage ? (
                                                                    <img
                                                                        src={item.conditionImage}
                                                                        alt={item.name}
                                                                        style={{
                                                                            width: '69px',
                                                                            height: '62px',
                                                                            objectFit: 'contain',
                                                                        }}
                                                                    />
                                                                ) : (
                                                                    <Box
                                                                        sx={{
                                                                            width: "69px",
                                                                            height: "62px",
                                                                            backgroundColor: "#f5f5f5",
                                                                            borderRadius: "4px",
                                                                        }}
                                                                    />
                                                                )}
                                                            </Box>
                                                            <MLTypography variant="body2" fontWeight={500}>
                                                                {item.name}
                                                            </MLTypography>
                                                        </Stack>
                                                    ))}
                                                </Box>
                                            </Box>
                                        }
                                        {/* Product Suggestions */}
                                        {data.productRecommendations.length > 0 &&
                                            <Box>
                                                <MLTypography
                                                    fontFamily="Syne"
                                                    fontWeight={600}
                                                    fontSize="20px"
                                                    mb={"25px"}
                                                >
                                                    Product suggestions
                                                </MLTypography>
                                                <Stack spacing={2}>
                                                    {data.productRecommendations &&
                                                        data.productRecommendations.map((product: IProduct) => (
                                                            <Stack
                                                                key={product.name}
                                                                direction="row"
                                                                spacing={2}
                                                                sx={{ backgroundColor: "#F7F6F6" }}
                                                                borderRadius="10px"
                                                                padding="20px"
                                                            >
                                                                <Box
                                                                    component="img"
                                                                    src={product.image.url}
                                                                    alt={product.name}
                                                                    sx={{
                                                                        width: "105px",
                                                                        height: "115px",
                                                                        border: "1px solid #eee",
                                                                        objectFit: "contain",
                                                                        backgroundColor: "white",
                                                                        borderRadius: "10px",
                                                                    }}
                                                                />
                                                                <Stack spacing={0.5}>
                                                                    <MLTypography
                                                                        color="#7856FF"
                                                                        fontWeight={600}
                                                                        fontSize="16px"
                                                                        lineHeight={1.2}
                                                                        maxWidth="140px"
                                                                    >
                                                                        {product.name}
                                                                    </MLTypography>
                                                                    <MLTypography
                                                                        // maxWidth="180px"
                                                                        fontSize="14px"
                                                                    >
                                                                        {product.description}
                                                                    </MLTypography>
                                                                    <MLTypography fontWeight={700} fontSize="14px">
                                                                        ${product.price}
                                                                    </MLTypography>
                                                                    <Box>
                                                                        <MLButton
                                                                            endIcon={<ReadMoreRightArrow />}
                                                                            variant="outlined">
                                                                            BUY ITEM
                                                                        </MLButton>
                                                                    </Box>
                                                                </Stack>
                                                            </Stack>
                                                        ))}
                                                </Stack>
                                            </Box>
                                        }
                                    </Stack>
                                </Stack>
                            )}
                        </Stack>

                        {/* Toggle Button (only on mobile) */}
                        {isMobile && (
                            <Stack>
                                <MLButton
                                    onClick={() => setExpanded((prev) => !prev)}
                                    sx={{
                                        mt: 4,
                                        // mx: "auto",
                                        display: "flex",
                                        borderRadius: "8px",
                                        // width: "fit-content",
                                        px: 4,
                                    }}
                                    variant="outlined"
                                    endIcon={expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                                >
                                    {expanded ? "VIEW LESS DETAILS" : "VIEW ALL DETAILS"}
                                </MLButton>
                            </Stack>
                        )}
                    </Box>
                    {index < issueData.issueBasedData.length - 1 &&
                        <Divider sx={{ borderColor: "#E0E0E0" }} />
                    }
                </Stack>
            ))}
        </Stack>
    )
}

export default ErgoIssueAndRecommendations
