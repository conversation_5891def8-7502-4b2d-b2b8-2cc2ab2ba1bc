import { Grid, Typography, Stack } from "@mui/material";

export default function ErrorPage({ message }: { message?: string }) {
  return (
    <Grid display="flex" justifyContent="center" alignItems="center" mt={20}>
      <Grid container direction="column" display="flex" alignItems="center">
        <Typography variant="h1">500</Typography>
        <Stack direction="row" spacing="2">
          <Typography>
            {message ?? "Sorry, an internal error occured."}
          </Typography>
        </Stack>
      </Grid>
    </Grid>
  );
}
