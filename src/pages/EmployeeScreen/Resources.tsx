import React, { useEffect, useState } from 'react';
import MLTypography from '../../components/ui/MLTypography/MLTypography';
import { TabContext, TabPanel } from '@mui/lab';
import MLTabs from '../../components/ui/MLTabs/MLTabs';
import { formatEnumString } from '../../utils/enumUtils';
import MLTab from '../../components/ui/MLTab/MLTab';
import {
  ButtonBase,
  Stack,
  styled,
} from '@mui/material';
import ErgoReelsTab from '../../components/EmployeeScreen/ErgoReelsTab';
import ErgoArticlesTab from '../../components/EmployeeScreen/ErgoArticlesTab';
import ErgoVideosTab from '../../components/EmployeeScreen/ErgoVideosTab';
import PostersTab from '../../components/EmployeeScreen/PostersTab';
import ResourceFilterBox from '../../components/EmployeeScreen/ResourceFilterBox';
import { desktop, tablet } from '../../responsiveStyles';
import { useLocation } from 'react-router-dom';
import MLBanner from '../../components/ui/MLBanner/MLBanner';
import MLContainer from '../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer';
import ErgoArticlesPDFTab from '../../components/EmployeeScreen/ErgoArticlesPDFTab';
import { StatusMessage } from '../MyAssessments/myAssessmentUtils';

export enum TabTitle {
  ERGO_VIDEOS = "Ergo Videos",
  ERGO_ARTICLES = "Ergo Articles",
  // ERGO_REELS = "Ergo Reels",
  POSTER = "Posters",
}

interface TabItem {
  title: TabTitle;
  count: number;
}

// Custom styled container for the grid
export const CustomGridContainer = styled('div')({
  display: 'grid',
  gridTemplateColumns: 'repeat(3, 1fr)',
  columnGap: '40px',
  rowGap: '30px',
  width: '100%',
  '@media (max-width: 1200px)': {
    gridTemplateColumns: 'repeat(2, 1fr)',
  },
  '@media (max-width: 600px)': {
    gridTemplateColumns: '1fr',
    width: '100%',
  },
});

const Resources = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const activeTab = queryParams.get("activeTab");
  const initialTab = location.state?.activeTab || activeTab || TabTitle.ERGO_VIDEOS;
  const [currentTab, setCurrentTab] = useState<TabTitle>(initialTab);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);

  const tabs: TabItem[] = Object.values(TabTitle).map((tabs) => ({
    title: tabs,
    count: 0,
  }) as TabItem);

  const handleTopicToggle = (topics: string[]) => {
    setSelectedTopics(topics);
  };

  const handleClearFilters = () => {
    setSelectedTopics([]);
  };

  const handleTabChange = (event: React.SyntheticEvent, newTab: TabTitle) => {
    setCurrentTab(newTab);
    handleClearFilters();
  };

  useEffect(() => {
    if (location.state?.activeTab) {
      setCurrentTab(location.state?.activeTab)
    }
  }, [location])

  // Validate that tabs exist before rendering
  if (tabs.length === 0) {
    return (
      <MLContainer>
        <div style={{
          marginTop: '50px'
        }}>

          <StatusMessage
            title="Resources Unavailable"
            message="unable to load the resources at this time. Please try again later."
            type="error"
          />
        </div>
      </MLContainer>
    );
  }

  return (
    <>
      <MLBanner backgroundColor='#E3DDFF' title={'Training'}
        subtitle='Browser our collection of ergonomic resources'
      />

      <Stack
        direction={"column"}
        sx={{
          paddingX: {
            // lg: desktop.contentContainer.paddingX,
            // md: tablet.contentContainer.paddingX,
            // xs: tablet.contentContainer.paddingX,
          },
          paddingY: {
            // lg: desktop.contentContainer.paddingY,
            md: tablet.contentContainer.paddingY,
            xs: tablet.contentContainer.paddingY,
          },
          mt: {
            md: '-40px',
            sm: '-34px'
          },
        }}
        fontFamily="syne"
      >

        <MLContainer>
          <Stack gap="15px">
            <TabContext value={currentTab}>
              {/* tab headings */}
              <Stack
                direction="row"
                display={{ xs: "none", sm: "flex" }}
                justifyContent="space-between"
                alignItems="center"
              >
                <Stack
                  sx={{
                    overflowX: "auto", // Allow horizontal scrolling
                    Width: "100%", // Ensure that the tabs container doesn't stretch beyond its width
                  }}>
                  <MLTabs
                    value={currentTab}
                    onChange={handleTabChange}
                    defaultValue={formatEnumString(TabTitle.ERGO_VIDEOS)}
                  >
                    {tabs.map((tab) => (
                      <MLTab
                        key={tab.title}
                        value={tab.title}
                        label={`${formatEnumString(tab.title)}`}
                      />
                    ))}
                  </MLTabs>
                </Stack>
                {/* Filter box*/}
                <ResourceFilterBox
                  selectedTopics={selectedTopics}
                  handleTopicToggle={handleTopicToggle}
                  handleClearFilters={handleClearFilters}
                />
              </Stack>

              {/** Tabs for mobile view */}
              <Stack direction={"row"} display={{ xs: "flex", sm: "none" }}
                sx={{
                  gap: "12px",
                  alignItems: "center",
                }}
              >
                {/* videos Tab */}
                <ButtonBase
                  component={Stack}
                  sx={{
                    border: currentTab === TabTitle.ERGO_VIDEOS ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                    backgroundColor: currentTab === TabTitle.ERGO_VIDEOS ? "#E3DDFF" : "",
                    borderRadius: "5px",
                    padding: "8px",
                    justifyContent: "center",
                    alignItems: "center",
                    flex: 1,
                    minWidth: 0,
                    transition: "background-color 0.3s",
                    "&:hover": {
                      backgroundColor: currentTab === TabTitle.ERGO_VIDEOS ? "#D9D0FF" : "#F5F5F5",
                    },
                    gap: "4px",
                  }}
                  onClick={() => setCurrentTab(TabTitle.ERGO_VIDEOS)}
                  disableRipple={false}
                  centerRipple
                >
                  <svg width="24" height="24" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 26.5917C20.9542 26.5917 26.5917 20.9542 26.5917 14C26.5917 7.04579 20.9542 1.40829 14 1.40829C7.04582 1.40829 1.40833 7.04579 1.40833 14C1.40833 20.9542 7.04582 26.5917 14 26.5917Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M10.5659 13.4327V9.0768C10.5659 8.46262 11.1538 8.07523 11.6201 8.38704L14.9246 10.5603L18.2291 12.7335C18.6954 13.0453 18.6954 13.8106 18.2291 14.1224L14.9246 16.2957L11.6201 18.4689C11.1538 18.7807 10.5659 18.3933 10.5659 17.7791V13.4232V13.4327Z" fill="#FF6E6E" />
                  </svg>

                  <MLTypography
                    variant="body1"
                    fontSize={"14px"}
                    fontWeight={400}
                    textAlign={"center"}
                  >
                    Videos
                  </MLTypography>
                </ButtonBase>

                {/* Articles Tab */}
                <ButtonBase
                  component={Stack}
                  sx={{
                    border: currentTab === TabTitle.ERGO_ARTICLES ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                    backgroundColor: currentTab === TabTitle.ERGO_ARTICLES ? "#E3DDFF" : "",
                    borderRadius: "5px",
                    padding: "8px",
                    justifyContent: "center",
                    alignItems: "center",
                    flex: 1,
                    minWidth: 0,
                    transition: "background-color 0.3s",
                    "&:hover": {
                      backgroundColor: currentTab === TabTitle.ERGO_ARTICLES ? "#D9D0FF" : "#F5F5F5",
                    },
                    gap: "4px",
                  }}
                  onClick={() => setCurrentTab(TabTitle.ERGO_ARTICLES)}
                  disableRipple={false}
                  centerRipple
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clipPath="url(#clip0_17028_10667)">
                      <rect width="24" height="24" fill="#E3DDFF" />
                      <path d="M0.75 2.25C0.75 1.42 1.42 0.75 2.25 0.75H21.75C22.58 0.75 23.25 1.42 23.25 2.25V21.75C23.25 22.58 22.58 23.25 21.75 23.25H2.25C1.42 23.25 0.75 22.58 0.75 21.75V2.25Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M13.5 13.5H19.5" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M13.5 16.5H19.5" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M13.5 19.5H17.25" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M3.75 13.5H9.75" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M3.75 16.5H9.75" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M3.75 19.5H7.5" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M3.75 4.5C3.75 4.09 4.09 3.75 4.5 3.75H19.5C19.91 3.75 20.25 4.09 20.25 4.5V9C20.25 9.41 19.91 9.75 19.5 9.75H4.5C4.09 9.75 3.75 9.41 3.75 9V4.5Z" fill="#FF6E6E" />
                    </g>
                    <defs>
                      <clipPath id="clip0_17028_10667">
                        <rect width="24" height="24" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>

                  <MLTypography
                    variant="body1"
                    fontSize={"14px"}
                    fontWeight={400}
                    textAlign={"center"}
                  >
                    Articles
                  </MLTypography>
                </ButtonBase>

                {/* Reels Tab */}
                {/* <ButtonBase
                  component={Stack}
                  sx={{
                    border: currentTab === TabTitle.ERGO_REELS ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                    backgroundColor: currentTab === TabTitle.ERGO_REELS ? "#E3DDFF" : "",
                    borderRadius: "5px",
                    padding: "8px",
                    justifyContent: "center",
                    alignItems: "center",
                    flex: 1,
                    minWidth: 0,
                    transition: "background-color 0.3s",
                    "&:hover": {
                      backgroundColor: currentTab === TabTitle.ERGO_REELS ? "#D9D0FF" : "#F5F5F5",
                    },
                    gap: "4px",
                  }}
                  onClick={() => setCurrentTab(TabTitle.ERGO_REELS)}
                  disableRipple={false}
                  centerRipple
                >
                  <svg width="19" height="24" viewBox="0 0 19 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clipPath="url(#clip0_17028_10682)">
                      <path d="M6 10.5041V6.69886C6 6.16233 6.51361 5.8239 6.92094 6.09629L9.80772 7.9948L12.6945 9.8933C13.1018 10.1657 13.1018 10.8343 12.6945 11.1067L9.80772 13.0052L6.92094 14.9037C6.51361 15.1761 6 14.8377 6 14.3011V10.4959V10.5041Z" fill="#FF6E6E" />
                      <path d="M1 4C1 2.34 2.34 1 4 1H15.25C16.91 1 18.25 2.34 18.25 4V20.5C18.25 22.16 16.91 23.5 15.25 23.5H4C2.34 23.5 1 22.16 1 20.5V4Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M1 19H18.25" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                    </g>
                    <defs>
                      <clipPath id="clip0_17028_10682">
                        <rect width="18.75" height="24" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>

                  <MLTypography
                    variant="body1"
                    fontSize={"14px"}
                    fontWeight={400}
                    textAlign={"center"}
                  >
                    Reels
                  </MLTypography>
                </ButtonBase> */}

                {/** Posters tab */}
                <ButtonBase
                  component={Stack}
                  sx={{
                    border: currentTab === TabTitle.POSTER ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                    backgroundColor: currentTab === TabTitle.POSTER ? "#E3DDFF" : "",
                    borderRadius: "5px",
                    padding: "8px",
                    justifyContent: "center",
                    alignItems: "center",
                    flex: 1,
                    minWidth: 0,
                    transition: "background-color 0.3s",
                    "&:hover": {
                      backgroundColor: currentTab === TabTitle.POSTER ? "#D9D0FF" : "#F5F5F5",
                    },
                    gap: "4px",
                  }}
                  onClick={() => setCurrentTab(TabTitle.POSTER)}
                  disableRipple={false}
                  centerRipple
                >
                  <svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clipPath="url(#clip0_17028_10692)">
                      <path d="M0.75 3.20221C0.75 2.33636 1.42 1.63742 2.25 1.63742H21.75C22.58 1.63742 23.25 2.33636 23.25 3.20221V23.5445C23.25 24.4104 22.58 25.1093 21.75 25.1093H2.25C1.42 25.1093 0.75 24.4104 0.75 23.5445V3.20221Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M16.5 11.8086C18.16 11.8086 19.5 10.4107 19.5 8.67899C19.5 6.94729 18.16 5.54941 16.5 5.54941C14.84 5.54941 13.5 6.94729 13.5 8.67899C13.5 10.4107 14.84 11.8086 16.5 11.8086Z" fill="#FF6E6E" />
                      <path d="M3.96002 16.4612C6.03002 15.2302 8.51002 15.0007 10.76 15.8353C13.01 16.6699 14.79 18.4746 15.66 20.7905" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M14.38 18.5059C16.02 16.9411 18.52 16.8889 20.23 18.3807" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
                    </g>
                    <defs>
                      <clipPath id="clip0_17028_10692">
                        <rect width="24" height="25.0367" fill="white" transform="translate(0 0.855026)" />
                      </clipPath>
                    </defs>
                  </svg>

                  <MLTypography
                    variant="body1"
                    fontSize={"14px"}
                    fontWeight={400}
                    textAlign={"center"}
                  >
                    Posters
                  </MLTypography>
                </ButtonBase>

                <ResourceFilterBox
                  selectedTopics={selectedTopics}
                  handleTopicToggle={handleTopicToggle}
                  handleClearFilters={handleClearFilters}
                />
              </Stack>

              {/* All Tabs */}
              <Stack>
                {/* <TabPanel sx={{ px: 0 }} value={TabTitle.ERGO_REELS}>
                  <ErgoReelsTab selectedTopics={selectedTopics} />
                </TabPanel> */}
                <TabPanel sx={{ px: 0 }} value={TabTitle.ERGO_ARTICLES}>
                  {/* <ErgoArticlesTab selectedTopics={selectedTopics} />   */}
                  {/* for temporary commited , for demo purpose to show pdf modal to article show  */}
                  <ErgoArticlesPDFTab selectedTopics={selectedTopics} />
                </TabPanel>
                <TabPanel sx={{ px: 0 }} value={TabTitle.ERGO_VIDEOS}>
                  <ErgoVideosTab selectedTopics={selectedTopics} />
                </TabPanel>
                <TabPanel sx={{ px: 0 }} value={TabTitle.POSTER}>
                  <PostersTab selectedTopics={selectedTopics} />
                </TabPanel>
              </Stack>
            </TabContext>
          </Stack >
        </MLContainer>
      </Stack>
    </>

  );
};

export default Resources;
