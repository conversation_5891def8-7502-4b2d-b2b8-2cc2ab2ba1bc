import MLTypography from "../../components/ui/MLTypography/MLTypography";
import { useTypeCountsContext } from "../../contexts/case-type-context/TypeCountContext";
import { Box, Paper } from "@mui/material";

const ErgoAnalytics = () => {
  const { typeCounts, prevMonthsTypeCounts, prevMonthName } =
    useTypeCountsContext();

  const defaultCounts = {
    accomodation: 0,
    body_discomfort: 0,
    new_office_assessment: 0,
    general_assessment: 0,
    product_requisite: 0,
  };

  const counts = { ...defaultCounts, ...typeCounts };
  const prevCounts = { ...defaultCounts, ...prevMonthsTypeCounts };

  const calculatePercentageChange = (current: number, previous: number) => {
    if (previous === 0) return current === 0 ? 0 : 100;
    return ((current - previous) / previous) * 100;
  };

  const percentageChanges = {
    accomodation: calculatePercentageChange(
      counts.accomodation,
      prevCounts.accomodation,
    ),
    body_discomfort: calculatePercentageChange(
      counts.body_discomfort,
      prevCounts.body_discomfort,
    ),
    new_office_assessment: calculatePercentageChange(
      counts.new_office_assessment,
      prevCounts.new_office_assessment,
    ),
    general_assessment: calculatePercentageChange(
      counts.general_assessment,
      prevCounts.general_assessment,
    ),
  };

  const getPercentageChangeStyle = (change: number) => ({
    backgroundColor: change > 0 ? "#31C100" : "#C40000",
    color: "white",
    padding: "3px 5px",
    borderRadius: "11px",
    fontSize: "16px",
    fontWeight: 500,
  });

  const formatPercentageChange = (change: number) =>
    `${change > 0 ? "↑" : "↓"} ${Math.abs(change).toFixed(1)}%`;

  return (
    <Box style={{ marginTop: "18px" }}>
      <MLTypography
        variant="h6"
        fontSize={32}
        fontWeight={600}
        style={{ marginBottom: "15px" }}
      >
        Ergo Analytics
      </MLTypography>
      <Paper
        style={{
          padding: "24px",
          minHeight: "45vh",
          borderRadius: "8px",
          border: "1px solid  #9C9C9C",
          boxShadow: "none",
        }}
      >
        <MLTypography
          fontSize={24}
          fontWeight={700}
          variant="h6"
          style={{ marginBottom: "30px" }}
        >
          Case Distribution
        </MLTypography>
        <Box textAlign="center">
          <Box display="flex" justifyContent="space-between">
            <MLTypography paragraph fontSize={22} fontWeight={600}>
              Accommodation
            </MLTypography>
            <MLTypography fontSize={22} fontWeight={600}>
              Body Discomfort
            </MLTypography>
            <MLTypography fontSize={22} fontWeight={600}>
              New Office
            </MLTypography>
            <MLTypography fontSize={22} fontWeight={600}>
              General Assessment
            </MLTypography>
          </Box>
          <hr
            style={{
              border: "1px thin #ADADAD",
              width: "100%",
              marginTop: "15px",
              marginBottom: "16px",
            }}
          />
          <Box display="flex" justifyContent="space-between" textAlign="center">
            <Box>
              <Box display="flex" justifyContent="center" alignItems="center">
                <MLTypography
                  variant="h4"
                  style={{
                    fontSize: "40px",
                    fontWeight: 700,
                    marginRight: "8px",
                  }}
                >
                  {counts.accomodation}
                </MLTypography>
                <Box position="relative" bottom={10}>
                  <Box
                    style={{
                      ...getPercentageChangeStyle(
                        percentageChanges.accomodation,
                      ),
                      display: "flex",
                      justifyContent: "center",
                    }}
                  >
                    <MLTypography fontSize={16} fontWeight={500}>
                      {formatPercentageChange(percentageChanges.accomodation)}
                    </MLTypography>
                  </Box>
                  <MLTypography
                    variant="body2"
                    style={{
                      position: "absolute",
                      top: "100%",
                      left: "50%",
                      transform: "translateX(-50%)",
                      fontSize: "12px",
                      fontFamily: "Inter",
                      fontWeight: 400,
                      marginTop: "6px",
                    }}
                  >
                    Compared to {prevMonthName} month
                  </MLTypography>
                </Box>
              </Box>
            </Box>
            <Box>
              <Box display="flex" justifyContent="center" alignItems="center">
                <MLTypography
                  variant="h4"
                  style={{
                    fontSize: "40px",
                    fontWeight: 700,
                    marginRight: "8px",
                  }}
                >
                  {counts.body_discomfort}
                </MLTypography>
                <Box position="relative" bottom={10}>
                  <Box
                    style={{
                      ...getPercentageChangeStyle(
                        percentageChanges.body_discomfort,
                      ),
                      display: "flex",
                      justifyContent: "center",
                    }}
                  >
                    <MLTypography fontSize={16} fontWeight={500}>
                      {formatPercentageChange(
                        percentageChanges.body_discomfort,
                      )}
                    </MLTypography>
                  </Box>
                  <MLTypography
                    variant="body2"
                    style={{
                      position: "absolute",
                      top: "100%",
                      left: "50%",
                      transform: "translateX(-50%)",
                      fontSize: "12px",
                      marginTop: "6px",
                      fontFamily: "Inter",
                      fontWeight: 400,
                    }}
                  >
                    Compared to {prevMonthName} month
                  </MLTypography>
                </Box>
              </Box>
            </Box>
            <Box>
              <Box display="flex" justifyContent="center" alignItems="center">
                <MLTypography
                  variant="h4"
                  style={{
                    fontSize: "40px",
                    fontWeight: 700,
                    marginRight: "8px",
                  }}
                >
                  {counts.new_office_assessment}
                </MLTypography>
                <Box position="relative" bottom={10}>
                  <Box
                    style={{
                      ...getPercentageChangeStyle(
                        percentageChanges.new_office_assessment,
                      ),
                      display: "flex",
                      justifyContent: "center",
                    }}
                  >
                    <MLTypography fontSize={16} fontWeight={500}>
                      {formatPercentageChange(
                        percentageChanges.new_office_assessment,
                      )}
                    </MLTypography>
                  </Box>
                  <MLTypography
                    variant="body2"
                    style={{
                      position: "absolute",
                      top: "100%",
                      left: "50%",
                      transform: "translateX(-50%)",
                      fontSize: "12px",
                      marginTop: "6px",
                      fontFamily: "Inter",
                      fontWeight: 400,
                    }}
                  >
                    Compared to {prevMonthName} month
                  </MLTypography>
                </Box>
              </Box>
            </Box>
            <Box
              sx={{
                transform: "translateX(-28px)",
              }}
            >
              <Box display="flex" justifyContent="center" alignItems="center">
                <MLTypography
                  variant="h4"
                  style={{
                    fontSize: "40px",
                    fontWeight: 700,
                    marginRight: "8px",
                  }}
                >
                  {counts.general_assessment}
                </MLTypography>
                <Box position="relative" bottom={10}>
                  <Box
                    style={{
                      ...getPercentageChangeStyle(
                        percentageChanges.general_assessment,
                      ),
                      display: "flex",
                      justifyContent: "center",
                    }}
                  >
                    <MLTypography fontSize={16} fontWeight={500}>
                      {formatPercentageChange(
                        percentageChanges.general_assessment,
                      )}
                    </MLTypography>
                  </Box>
                  <MLTypography
                    variant="body2"
                    style={{
                      position: "absolute",
                      top: "100%",
                      left: "50%",
                      transform: "translateX(-50%)",
                      fontSize: "12px",
                      marginTop: "6px",
                      fontFamily: "Inter",
                      fontWeight: 400,
                    }}
                  >
                    Compared to {prevMonthName} month
                  </MLTypography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default ErgoAnalytics;
