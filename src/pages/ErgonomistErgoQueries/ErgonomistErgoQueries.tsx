import React, { useState } from 'react';
import MLTypography from '../../components/ui/MLTypography/MLTypography';
import { TabContext, TabPanel } from '@mui/lab';
import MLTabs from '../../components/ui/MLTabs/MLTabs';
import { formatEnumString } from '../../utils/enumUtils';
import MLTab from '../../components/ui/MLTab/MLTab';
import { Stack } from '@mui/material';
import { desktop, tablet } from '../../responsiveStyles';
import { useGetIdentity, useList, useOne } from '@refinedev/core';
import { IIdentity } from '../AuthScreens/Profile/Profile';
import Loading from '../Loading/Loading';
import AllQueries from '../MyErgoQueris/Tabs/AllQueries';
import { ErgoRequest } from '../MyErgoQueris/MyErgoQueris';
import CompanyFilterBox from '../MyErgoQueris/components/CompanyFilterBox';
import User from '../../models/User';
import { Organization } from '../../models/Organization';


export enum TabTitle {
    ALL = "All Queries",
    ACTIVE = "Active",
    RESOLVED = "Resolved",
}

interface TabItem {
    title: TabTitle;
    count: number;
}

const ErgonomistErgoQueries = () => {
    const { data: userIdentity } = useGetIdentity<IIdentity>();
    const [selectedCompanies, setSelectedCompanies] = useState<string[]>([]);
    const [currentTab, setCurrentTab] = useState<TabTitle>(TabTitle.ALL);
    const { data: organizationsData, isLoading: isLoadingOrganizations } = useList<Organization>({
        resource: "organizations",
        // queryOptions: {
        //     enabled: !!userIdentity?.id
        // }
    });

    const { data: userDetails, isLoading: isLoadingUserDetails } = useOne<User>({
        resource: "users",
        id: userIdentity?.id!,
        meta: {
            populate: "*"
        }
    });

    const { data: ergoRequests, isLoading, refetch } = useList<ErgoRequest>({
        resource: "ep-ergo-requests",
        meta: {
            populate: {
                sender: {
                    populate: {
                        user: {
                            populate: '*'
                        },
                        employee: {
                            populate: '*'
                        },
                        organization: {
                            populate: '*'
                        }
                    }
                },
                attachments: {
                    fields: ['name', 'url', 'mime', 'size']
                },
                ergoRequestThread: {
                    populate: {
                        sender: {
                            fields: ['username', 'department', 'avatar']
                        },
                        attachments: {
                            fields: ['name', 'url', 'mime', 'size']
                        }
                    },
                    sort: ['threadIndex:asc']
                },
                assignedTo: "*"
            },
            pagination: {
                pageSize: 1000, // Fetch up to 1000 records
                withCount: true // Include total count in response
            }
        },
        filters: [
            {
                field: "organization.id",
                operator: "eq",
                value: userDetails?.data.organization.id
            },
        ],
        pagination: {
            pageSize: 1000 // Match the meta pagination size
        },
        queryOptions: {
            enabled: !!userDetails?.data.organization.id,
        }
    });

    const getFilteredRequests = (tab: TabTitle) => {
        if (!ergoRequests?.data) return [];

        const sortedData = [...ergoRequests.data].sort((a, b) => {
            const dateA = new Date(a.createdAt).getTime();
            const dateB = new Date(b.createdAt).getTime();
            return dateB - dateA;
        });

        const filteredByCompany = selectedCompanies.length > 0
            ? sortedData.filter(req =>
                selectedCompanies.includes(req.sender.organization.id.toString()))
            : sortedData;

        switch (tab) {
            case TabTitle.ACTIVE:
                return filteredByCompany.filter(req =>
                    req.status === 'pending' || req.status === 'processing');
            case TabTitle.RESOLVED:
                return filteredByCompany.filter(req =>
                    req.status === 'completed');
            default:
                return filteredByCompany;
        }
    };

    const getTabCounts = () => {
        if (!ergoRequests?.data) return {
            [TabTitle.ALL]: 0,
            [TabTitle.ACTIVE]: 0,
            [TabTitle.RESOLVED]: 0
        };
        if (!ergoRequests?.data) return {
            [TabTitle.ALL]: 0,
            [TabTitle.ACTIVE]: 0,
            [TabTitle.RESOLVED]: 0
        };

        return {
            [TabTitle.ALL]: getFilteredRequests(TabTitle.ALL).length,
            [TabTitle.ACTIVE]: getFilteredRequests(TabTitle.ACTIVE).length,
            [TabTitle.RESOLVED]: getFilteredRequests(TabTitle.RESOLVED).length
        };
    };

    // console.log('ergoRequests: ', ergoRequests?.data);
    // console.log('userDetails: ', userDetails?.data);
    // console.log('userIdentity: ', userIdentity);
    // console.log('organizationsData: ', organizationsData);
    // console.log("isLoading:", isLoading);

    const tabCounts = getTabCounts();

    const tabs: TabItem[] = Object.values(TabTitle).map((tab) => ({
        title: tab,
        count: tabCounts[tab],
    }));

    const handleTabChange = (event: React.SyntheticEvent, newTab: TabTitle) => {
        setCurrentTab(newTab);
    };

    if (isLoading || isLoadingUserDetails) return <Loading />

    return (
        <Stack
            direction={"column"}
            sx={{
                paddingX: {
                    lg: desktop.contentContainer.paddingX,
                    md: tablet.contentContainer.paddingX,
                    xs: tablet.contentContainer.paddingX,
                },
                paddingY: {
                    lg: desktop.contentContainer.paddingY,
                    md: tablet.contentContainer.paddingY,
                    xs: tablet.contentContainer.paddingY,
                },
            }}
            fontFamily="syne"
        >
            <Stack
                direction="column"
                marginBottom={{
                    lg: desktop.contentContainer.header.marginBottom,
                    md: tablet.contentContainer.header.marginBottom,
                    xs: tablet.contentContainer.header.marginBottom,
                }}
                gap={1}
            >
                <MLTypography marginLeft={{ xs: "46px", md: "0px" }}
                    sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        maxWidth: { xs: "calc(100vw)" },
                    }}
                    variant="h1" fontWeight={700}
                >
                    Ergo Queries
                </MLTypography>
                <MLTypography marginLeft={{ xs: "46px", md: "0px" }} fontWeight={400}>
                    Track and manage your ergonomic support requests
                </MLTypography>

            </Stack>
            <Stack gap="15px">
                <TabContext value={currentTab}>
                    {/* tab headings */}
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Stack sx={{
                            overflowX: "auto", // Allow horizontal scrolling
                            Width: "100%", // Ensure that the tabs container doesn't stretch beyond its width
                        }}>
                            <MLTabs
                                value={currentTab}
                                onChange={handleTabChange}
                                defaultValue={formatEnumString(TabTitle.ALL)}
                            >
                                {tabs.map((tab) => (
                                    <MLTab
                                        key={tab.title}
                                        value={tab.title}
                                        label={`${formatEnumString(tab.title)} ${tab.count}`} />
                                ))}
                            </MLTabs>
                        </Stack>
                        <CompanyFilterBox
                            companies={organizationsData?.data.map(org => ({
                                id: org.id.toString(),
                                name: org.name
                            })) || []}
                            selectedCompanies={selectedCompanies}
                            onCompaniesChange={setSelectedCompanies}
                            onClearFilters={() => setSelectedCompanies([])}
                        />
                    </Stack>
                    {/* All Tabs */}
                    <Stack>
                        {Object.values(TabTitle).map((tab) => (
                            <TabPanel key={tab} sx={{ pl: 0 }} value={tab}>
                                <AllQueries
                                    loggedUser={userDetails?.data}
                                    ergoRequests={getFilteredRequests(tab)}
                                />
                            </TabPanel>
                        ))}
                    </Stack>
                </TabContext>
            </Stack >
        </Stack>
    )
}

export default ErgonomistErgoQueries
