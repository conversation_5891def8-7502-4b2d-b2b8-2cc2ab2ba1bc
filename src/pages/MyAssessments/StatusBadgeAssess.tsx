import React from 'react';
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import MLTypography from '../../components/ui/MLTypography/MLTypography';
import { ExpertAssessmentStatus } from './myAssessTypes';

export const BadgeWrapper = styled(Box)({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '4px 7px',
    borderRadius: "5px",
    border: "0.5px solid #9c9c9c",
    fontSize: '15px',
    fontWeight: 500,
});

const StatusIcons = {
    pending: (
        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="16" viewBox="0 0 13 16" fill="none">
            <g clipPath="url(#clip0_13991_39801)">
                <path d="M2.10564 3.66541C2.49352 5.79874 3.84416 7.26021 6.23378 8.00134C8.62339 7.26021 9.98096 5.79874 10.3619 3.66541C10.4104 3.3745 10.3204 3.08359 10.1195 2.86195C9.91862 2.6403 9.63464 2.51562 9.32988 2.51562H3.13767C2.83291 2.51562 2.542 2.6403 2.34806 2.86195C2.1472 3.08359 2.06408 3.3745 2.10564 3.66541Z" stroke="#FF7A00" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M0.519531 0.519531H11.9481" stroke="#FF7A00" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M3.13767 13.4857C2.83291 13.4857 2.542 13.361 2.34806 13.1394C2.1472 12.9177 2.06408 12.6268 2.10564 12.3359C2.49352 10.2026 3.84416 8.74113 6.23378 8C8.62339 8.74113 9.98096 10.2026 10.3619 12.3359C10.4104 12.6268 10.3204 12.9177 10.1195 13.1394C9.91862 13.361 9.63464 13.4857 9.32988 13.4857H3.13767Z" stroke="#FF7A00" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M0.519531 15.4805H11.9481" stroke="#FF7A00" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6.23361 6.54059C6.98361 6.51412 7.6612 6.07471 8.00775 5.39706C8.0543 5.31765 8.0543 5.21176 8.00775 5.13235C7.9612 5.05294 7.87844 5 7.78534 5H4.68189C4.58878 5 4.50603 5.05294 4.45947 5.13235C4.41292 5.21706 4.41292 5.31765 4.45947 5.39706C4.80603 6.08 5.48361 6.51412 6.23361 6.54059Z" fill="#FF7A00" />
                <path d="M6.62151 9.69676C6.51806 9.58558 6.37841 9.52734 6.23358 9.52734C6.08875 9.52734 5.9491 9.59087 5.84565 9.69676L4.12841 11.682C3.99393 11.8356 3.96289 12.0579 4.04565 12.2485C4.12841 12.4391 4.31462 12.5609 4.51634 12.5609H7.96117C8.16289 12.5609 8.3491 12.4391 8.43186 12.2485C8.51462 12.0579 8.48358 11.8356 8.3491 11.682L6.62669 9.69676H6.62151Z" fill="#FF7A00" />
            </g>
        </svg>
    ),
    processing: (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 12.5H7.5V15" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M15.1133 12.9867C14.5667 14.58 13.0267 15.6067 11.3467 15.4933C9.66667 15.38 8.28001 14.1533 7.95334 12.5" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M13 10.5H15.5V8" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M7.88666 10.0152C8.43332 8.42186 9.97332 7.39519 11.6533 7.50852C13.3333 7.62186 14.72 8.84852 15.0467 10.5019" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M2.5 4.5H9.5" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M2.5 7.5H6.5" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M2.5 10.5H5" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M5 13.5H1.5C0.946667 13.5 0.5 13.0533 0.5 12.5V1.5C0.5 0.946667 0.946667 0.5 1.5 0.5H8.58667C8.85333 0.5 9.10667 0.606667 9.29333 0.793333L11.2067 2.70667C11.3933 2.89333 11.5 3.14667 11.5 3.41333V5" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
        </svg>


    ),
    completed: (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M9 17C13.416 17 17 13.416 17 9C17 4.584 13.416 1 9 1C4.584 1 1 4.584 1 9C1 13.416 4.584 17 9 17Z" fill="#31C100" stroke="#31C100" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M4.73364 9.86501L6.47586 12.3397C6.61098 12.5388 6.83142 12.6597 7.0732 12.6739C7.31498 12.681 7.54253 12.5743 7.69187 12.3823L13.267 5.32812" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    ),
    'Scheduled': (
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 4.19951C1 3.60927 1.47646 3.13281 2.0667 3.13281H15.9338C16.5241 3.13281 17.0005 3.60927 17.0005 4.19951V15.9332C17.0005 16.5235 16.5241 16.9999 15.9338 16.9999H2.0667C1.47646 16.9999 1 16.5235 1 15.9332V4.19951Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M1 7.39844H17.0005" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M5.26682 4.73345V1" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M12.7336 4.73345V1" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M4.73328 9.53125C4.24203 9.53125 3.84436 9.92892 3.84436 10.4202C3.84436 10.9114 4.24203 11.3091 4.73328 11.3091C5.22452 11.3091 5.6222 10.9114 5.6222 10.4202C5.6222 9.92892 5.22452 9.53125 4.73328 9.53125Z" fill="#FF6E6E" />
            <path d="M4.73328 13.0859C4.24203 13.0859 3.84436 13.4836 3.84436 13.9749C3.84436 14.4661 4.24203 14.8638 4.73328 14.8638C5.22452 14.8638 5.6222 14.4661 5.6222 13.9749C5.6222 13.4836 5.22452 13.0859 4.73328 13.0859Z" fill="#FF6E6E" />
            <path d="M9.00012 9.53125C8.50888 9.53125 8.11121 9.92892 8.11121 10.4202C8.11121 10.9114 8.50888 11.3091 9.00012 11.3091C9.49137 11.3091 9.88904 10.9114 9.88904 10.4202C9.88904 9.92892 9.49137 9.53125 9.00012 9.53125Z" fill="#FF6E6E" />
            <path d="M9.00012 13.0859C8.50888 13.0859 8.11121 13.4836 8.11121 13.9749C8.11121 14.4661 8.50888 14.8638 9.00012 14.8638C9.49137 14.8638 9.88904 14.4661 9.88904 13.9749C9.88904 13.4836 9.49137 13.0859 9.00012 13.0859Z" fill="#FF6E6E" />
            <path d="M13.2669 9.53125C12.7757 9.53125 12.378 9.92892 12.378 10.4202C12.378 10.9114 12.7757 11.3091 13.2669 11.3091C13.7582 11.3091 14.1559 10.9114 14.1559 10.4202C14.1559 9.92892 13.7582 9.53125 13.2669 9.53125Z" fill="#FF6E6E" />
            <path d="M13.2669 13.0859C12.7757 13.0859 12.378 13.4836 12.378 13.9749C12.378 14.4661 12.7757 14.8638 13.2669 14.8638C13.7582 14.8638 14.1559 14.4661 14.1559 13.9749C14.1559 13.4836 13.7582 13.0859 13.2669 13.0859Z" fill="#FF6E6E" />
        </svg>

    ),
    'report_created': (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.06725 2.5H1.83652C1.37365 2.5 1 2.94662 1 3.49989V14.4987C1 15.052 1.37365 15.4986 1.83652 15.4986H5.5M11.8748 5.5V3.49989C11.8748 2.94662 11.5012 2.5 11.0383 2.5H8.80756" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8.62355 3.15972C8.56778 3.36636 8.40605 3.49968 8.2276 3.49968H4.64727C4.46882 3.49968 4.30709 3.3597 4.25132 3.15972L3.69364 1.15993C3.64902 1.00661 3.67133 0.839964 3.7494 0.706645C3.82748 0.573326 3.95575 0.5 4.08959 0.5H8.78528C8.91912 0.5 9.04739 0.579992 9.12547 0.706645C9.20354 0.839964 9.22585 1.00661 9.18123 1.15993L8.62355 3.15972Z" fill="#FF6E6E" />
            <path d="M11 15.5C13.2067 15.5 15 13.7067 15 11.5C15 9.29333 13.2067 7.5 11 7.5C8.79333 7.5 7 9.29333 7 11.5C7 13.7067 8.79333 15.5 11 15.5Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M12.78 10.3438L10.84 12.9237C10.7533 13.0371 10.62 13.1104 10.4733 13.1238C10.3267 13.1371 10.1867 13.0837 10.0867 12.9771L9.08667 11.9771" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
    ),
    'Assigned': (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_15191_50832)">
                <path d="M0.5 11.5003C0.5 9.98698 1.26 8.58031 2.52 7.74698C3.78 6.91365 5.37333 6.76698 6.76667 7.36031" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M4.99999 6C6.51999 6 7.74666 4.76667 7.74666 3.25333C7.74666 1.74 6.51999 0.5 4.99999 0.5C3.47999 0.5 2.25333 1.73333 2.25333 3.25333C2.25333 4.77333 3.48666 6 4.99999 6Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M11.5 15.5C13.7067 15.5 15.5 13.7067 15.5 11.5C15.5 9.29333 13.7067 7.5 11.5 7.5C9.29333 7.5 7.5 9.29333 7.5 11.5C7.5 13.7067 9.29333 15.5 11.5 15.5Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M13.28 10.3359L11.3467 12.9159C11.26 13.0293 11.1267 13.1026 10.98 13.1159C10.8333 13.1293 10.6933 13.0759 10.5933 12.9693L9.59332 11.9693" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
            </g>
            <defs>
                <clipPath id="clip0_15191_50832">
                    <rect width="16" height="16" fill="white" />
                </clipPath>
            </defs>
        </svg>

    ),
    'follow_Up': (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="Icons" clipPath="url(#clip0_15191_50862)">
                <g id="Users / Geometric-Close-Up-Single-User-Actions-Neutral / single-neutral-actions-refresh">
                    <g id="Group 374">
                        <g id="Regular 374">
                            <path id="Shape 1471" d="M0.5 11.5003C0.5 9.98698 1.26 8.58031 2.52 7.74698C3.78 6.91365 5.37333 6.76698 6.76667 7.36031" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                            <path id="Oval 546" d="M4.99999 6C6.51999 6 7.74666 4.76667 7.74666 3.25333C7.74666 1.74 6.51999 0.5 4.99999 0.5C3.47999 0.5 2.25333 1.73333 2.25333 3.25333C2.25333 4.77333 3.48666 6 4.99999 6Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
                            <path id="Shape 1472" d="M10 12.5H7.5V15" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
                            <path id="Shape 1473" d="M15.1133 12.9867C14.5667 14.58 13.0267 15.6067 11.3467 15.4933C9.66667 15.38 8.28001 14.1533 7.95334 12.5" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
                            <path id="Shape 1474" d="M13 10.5H15.5V8" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
                            <path id="Shape 1475" d="M7.88666 10.0152C8.43332 8.42186 9.97332 7.39519 11.6533 7.50852C13.3333 7.62186 14.72 8.84852 15.0467 10.5019" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
                        </g>
                    </g>
                </g>
            </g>
            <defs>
                <clipPath id="clip0_15191_50862">
                    <rect width="16" height="16" fill="white" />
                </clipPath>
            </defs>
        </svg>

    ),
    'Created': (
        <svg width="19" height="17" viewBox="0 0 19 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.5 15.75C16.7067 15.75 18.5 13.9567 18.5 11.75C18.5 9.54333 16.7067 7.75 14.5 7.75C12.2933 7.75 10.5 9.54333 10.5 11.75C10.5 13.9567 12.2933 15.75 14.5 15.75Z" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M16.28 10.5859L14.3467 13.1659C14.26 13.2793 14.1267 13.3526 13.98 13.3659C13.8333 13.3793 13.6933 13.3259 13.5933 13.2193L12.5933 12.2193" stroke="#FF6E6E" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M11.3313 3.20375C11.3313 1.56934 10.0128 0.25 8.3795 0.25C6.74619 0.25 5.42773 1.56934 5.42773 3.20375" fill="#FF6E6E" />
            <path d="M15.7588 5.75V4.18771C15.7588 3.64291 15.3193 3.20312 14.7749 3.20312H1.98392C1.43948 3.20312 1 3.64291 1 4.18771V12.0644C1 12.6092 1.43948 13.049 1.98392 13.049H8" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M1 6.89844H11" stroke="#333333" strokeLinecap="round" strokeLinejoin="round" />
        </svg>

    ),
    'undefined': (
        <></>
    )
    // "AI": (
    // ),
    // "Survey": (
    // )
};


const getStatusIconType = (status: string): keyof typeof StatusIcons => {
    switch (status) {
        case ExpertAssessmentStatus.CREATED:
            return 'Created';
        case ExpertAssessmentStatus.ASSIGNED:
            return 'Assigned'
        case ExpertAssessmentStatus.SCHEDULED:
            return 'Scheduled'
        case ExpertAssessmentStatus.PENDING_APPROVAL:
            return 'pending';
        case ExpertAssessmentStatus.ASSESSMENT_IN_PROGRESS:
            return 'processing'
        case ExpertAssessmentStatus.REPORT_CREATED:
            return 'report_created';
        case ExpertAssessmentStatus.REPORT_SHARED:
            return 'report_created';
        case ExpertAssessmentStatus.FOLLOW_UP:
            return 'follow_Up'
        case 'in_process':
            return 'processing';
        case 'completed':
            return 'completed';
        case 'pending':
            return 'pending';
        // case 'AI':
        //     return '';
        // case 'Survey':
        //     return '';
        case ExpertAssessmentStatus.ASSESSMENT_COMPLETED:
        case ExpertAssessmentStatus.CLOSED:
            return 'completed';
        default:
            return 'undefined';
    }
};

export const formatStatusText = (status: string) => {
    let formattedStatus = status
        .replace(/_/g, ' ') // Replace underscores with spaces
        .replace(/\b\w/g, char => char.toUpperCase()); // Capitalize first letter of each word

    if (formattedStatus === "In Process") {
        formattedStatus = "In-process"; // Add hyphen
    }
    if (status === "assessment_in_progress") {
        formattedStatus = "In-progress"; // Add hyphen
    }
    if (formattedStatus === "report_shared") {
        formattedStatus = "Report Prepared"; // Add hyphen
    }

    return formattedStatus;
};



interface StatusBadgeProps {
    status: string;
}

const StatusBadgeAssess: React.FC<StatusBadgeProps> = ({ status }) => {
    const iconType = getStatusIconType(status);

    return (
        <BadgeWrapper>
            {StatusIcons[iconType]}
            <MLTypography fontSize={{ xs: '12px', sm: '15px' }} fontWeight={status === 'AI' || status === 'Survey' ? 600 : 'normal'} component="span" sx={{ textTransform: 'capitalize' }}>
                {formatStatusText(status)}
            </MLTypography>
        </BadgeWrapper>
    );
};

export default StatusBadgeAssess;
