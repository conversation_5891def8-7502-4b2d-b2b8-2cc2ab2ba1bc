// MyAssessments.tsx - Updated to pass isAssessmentHistory prop
import React, { useMemo, useState } from 'react';
import { TabContext, TabPanel } from '@mui/lab';
import MLTabs from '../../components/ui/MLTabs/MLTabs';
import { formatEnumString } from '../../utils/enumUtils';
import MLTab from '../../components/ui/MLTab/MLTab';
import { Box, ButtonBase, Stack } from '@mui/material';
import { desktop, tablet } from '../../responsiveStyles';
import { useLocation } from 'react-router-dom';
import MLBanner from '../../components/ui/MLBanner/MLBanner';
import { useGetIdentity, useOne } from '@refinedev/core';
import { IIdentity } from '../AuthScreens/Profile/Profile';
import User from '../../models/User';
import MLAssessmentCard from './MLAssessmentCard';
import MLTypography from '../../components/ui/MLTypography/MLTypography';
import MLContainer from '../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer';
import { ExpertAssessmentData, MyAssesTabItem, SelfAssessmentData, TabTitle } from './myAssessTypes';
import Loading from '../Loading/Loading';
import { sortByUpdatedDate, StatusMessage } from './myAssessmentUtils';

interface Employee {
  id: string;
  cases?: ExpertAssessmentData[];
  selfAssessmentCases?: SelfAssessmentData[];
  riskLevel?: string;
}

interface MyAssessmentsProps {
  employeeId?: string; // Optional prop to support both standalone and EmployeeShow usage
  simplifiedView?: boolean; // New prop to determine if we show the simplified view (only self assessments, no tabs)
  isAssessmentHistory?: boolean; // New prop to determine if this is in the Assessment History section
}

const MyAssessments: React.FC<MyAssessmentsProps> = ({
  employeeId: propEmployeeId,
  simplifiedView = false,
  isAssessmentHistory = false
}) => {
  const location = useLocation();
  const initialTab = location.state?.activeTab || TabTitle.ALL;
  const [currentTab, setCurrentTab] = useState<TabTitle>(initialTab);

  const { data: identity } = useGetIdentity<IIdentity>();

  // Only fetch user details if we don't have an employeeId provided as prop
  const { data: userDetails, isLoading: isUserLoading, error: userError } = useOne<User>({
    resource: "users",
    id: identity?.id!,
    meta: { populate: ["employee"] },
    queryOptions: {
      enabled: !propEmployeeId // Only run this query if employeeId prop is not provided
    }
  });

  // Determine which employeeId to use
  const effectiveEmployeeId = propEmployeeId || userDetails?.data?.employee?.id;

  // API-level filtering for self-assessment cases
  // Build the appropriate API query based on whether this is the Assessment History view
  const apiFilters = useMemo(() => {
    // If this is the Assessment History view, add filter to exclude completed assessments without reports
    if (isAssessmentHistory) {
      return {
        populate: {
          cases: {
            fields: ["*"],
            populate: {
              assessment: {
                populate: ["report"]
              },
              assessmentAssignedTo: true
            }
          },
          selfAssessmentCases: {
            filters: {
              // This creates an OR condition:
              // 1. NOT isCompleted, OR
              // 2. Has a selfAssessmentReport that's not null
              $or: [
                { isCompleted: { $ne: true } },
                { selfAssessmentReport: { $null: false } }
              ]
            },
            populate: {
              selfAssessmentReport: true
            }
          },
          riskLevel: true
        }
      };
    } else {
      // Default populate for regular views
      return {
        populate: [
          "cases",
          "selfAssessmentCases",
          'cases.assessmentAssignedTo',
          'cases.assessment.report',
          'riskLevel',
          'selfAssessmentReport',
          'selfAssessmentCases.selfAssessmentReport'
        ]
      };
    }
  }, [isAssessmentHistory]);

  // Fetch employee data using the effective ID and appropriate filters
  const { data: currentEmployee, isLoading: isEmployeeLoading, isSuccess, error: employeeError } = useOne<Employee>({
    resource: "employees",
    meta: apiFilters,
    id: effectiveEmployeeId,
    queryOptions: {
      enabled: !!effectiveEmployeeId // Only fetch employee data when effectiveEmployeeId is available
    }
  });

  // Use useMemo to prevent unnecessary re-sorting on every render
  const selfAssessment = useMemo(() =>
    sortByUpdatedDate(currentEmployee?.data?.selfAssessmentCases),
    [currentEmployee?.data?.selfAssessmentCases]
  );


  const expertAssessment = useMemo(() =>
    sortByUpdatedDate(currentEmployee?.data?.cases),
    [currentEmployee?.data?.cases]
  );

  const riskLevel = currentEmployee?.data?.riskLevel;

  // Create tabs with accurate counts (only used in full view)
  const tabs: MyAssesTabItem[] = useMemo(() =>
    Object.values(TabTitle).map((tab) => ({
      title: tab,
      count: tab === TabTitle.SELF_ASSESSMENT
        ? selfAssessment.length
        : tab === TabTitle.EXPERT_ASSESSMENTS
          ? expertAssessment.length
          : selfAssessment.length + expertAssessment.length
    })),
    [selfAssessment.length, expertAssessment.length]
  );

  const handleTabChange = (event: React.SyntheticEvent, newTab: TabTitle) => {
    setCurrentTab(newTab);
  };

  // Handle loading state - different logic for standalone vs employee profile view
  const isLoading = propEmployeeId
    ? isEmployeeLoading
    : isUserLoading || isEmployeeLoading;

  if (isLoading) {
    return simplifiedView
      ? <Box><Loading /></Box>
      : <Box height={650}><Loading /></Box>;
  }

  // Handle error state
  const error = userError || employeeError;
  if (error) {
    return (
      <Box height={simplifiedView ? 'auto' : 650} display="flex" justifyContent="center" alignItems="center">
        <MLContainer>
          <StatusMessage
            title="Unable to Load Assessments"
            message="There is an issue connecting to servers. Please try again in a few moments or contact support if the issue persists."
            type="error"
          />
        </MLContainer>
      </Box>
    );
  }

  // Handle case where data loaded but no employee ID was found
  if (!effectiveEmployeeId && !isLoading) {
    return (
      <Box height={simplifiedView ? 'auto' : 650} display="flex" justifyContent="center" alignItems="center">
        <MLContainer>
          <StatusMessage
            title="Profile Not Found"
            message="Unable to find the employee profile. This is needed to view assessments."
            type="warning"
          />
        </MLContainer>
      </Box>
    );
  }

  // Handle case where the data loaded successfully but no employee data was found
  if (!isSuccess && !isLoading) {
    return (
      <Box height={simplifiedView ? 'auto' : 650} display="flex" justifyContent="center" alignItems="center">
        <MLContainer>
          <StatusMessage
            title="Assessment Data Unavailable"
            message="Unable to retrieve assessment data at this moment. Please try refreshing the page or come back later."
            type="warning"
          />
        </MLContainer>
      </Box>
    );
  }

  // For simplified view, just render self-assessment cards
  if (simplifiedView) {
    return (
      <Stack>
        {selfAssessment.length > 0 ? (
          selfAssessment.map(caseItem => (
            <MLAssessmentCard
              key={caseItem.id}
              data={caseItem}
              type="self"
              riskLevel={riskLevel}
              isAssessmentHistory={isAssessmentHistory} // Pass the new prop
            />
          ))
        ) : (
          <StatusMessage
            title="No Assessments Found"
            message={propEmployeeId
              ? "This employee doesn't have any assessments yet."
              : "You don't have any assessments yet. They will appear here once you've started or completed them."}
            type="info"
          />
        )}
      </Stack>
    );
  }

  // Full view with tabs and all assessments
  return (
    <>
      {/* Only show banner when used as standalone component (not in EmployeeShow) */}
      {!propEmployeeId && (
        <MLBanner
          backgroundColor="#E3DDFF"
          title="My Assessments"
          subtitle="Browse our collection of ergonomic resources"
        />
      )}

      <Stack
        direction="column"
        sx={{
          paddingX: {
            lg: propEmployeeId ? 0 : desktop.contentContainer.paddingX,
            md: tablet.contentContainer.paddingX,
            // xs: tablet.contentContainer.paddingX,
          },
          paddingY: {
            md: tablet.contentContainer.paddingY,
            xs: tablet.contentContainer.paddingY,
          },
          ...(propEmployeeId ? { padding: 0 } : {}), // Remove padding when used within EmployeeShow
          mt: {
            md: '-40px',
            sm: '-34px'
          },
        }}
        fontFamily="syne"
      >
        <MLContainer>
          {selfAssessment.length === 0 && expertAssessment.length === 0 ? (
            <StatusMessage
              title="No Assessments Found"
              message={propEmployeeId
                ? "This employee doesn't have any assessments yet."
                : "You don't have any assessments yet. Your assessments will appear here once they've been assigned or completed."}
              type="info"
            />
          ) : (
            <Stack gap="0px">
              <TabContext value={currentTab}>
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Stack display={{ xs: "none", sm: "flex" }}
                    sx={{ overflowX: "auto", width: "100%" }}
                  >
                    <MLTabs allowScrollButtonsMobile value={currentTab} onChange={handleTabChange}>
                      {tabs.map((tab) => (
                        <MLTab
                          key={tab.title}
                          value={tab.title}
                          label={`${formatEnumString(tab.title)}`}
                        />
                      ))}
                    </MLTabs>
                  </Stack>

                  {/** Tabs for mobile view */}
                  <Stack direction={"row"} display={{ xs: "flex", sm: "none" }}
                    sx={{
                      gap: "15px",
                      width: "100%"
                    }}
                  >
                    {/* All Tab */}
                    <ButtonBase
                      component={Stack}
                      sx={{
                        border: currentTab === TabTitle.ALL ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                        backgroundColor: currentTab === TabTitle.ALL ? "#E3DDFF" : "",
                        borderRadius: "5px",
                        padding: "15px",
                        justifyContent: "center",
                        alignItems: "center",
                        flex: 1,
                        minWidth: 0,
                        transition: "background-color 0.3s",
                        "&:hover": {
                          backgroundColor: currentTab === TabTitle.ALL ? "#D9D0FF" : "#F5F5F5",
                        },
                      }}
                      onClick={() => setCurrentTab(TabTitle.ALL)}
                      disableRipple={false}
                      centerRipple
                    >
                      <MLTypography
                        variant="body1"
                        fontSize={"14px"}
                        fontWeight={600}
                        textAlign={"center"}
                      >
                        All Assessment
                      </MLTypography>
                    </ButtonBase>

                    {/* Self Assessment Tab */}
                    <ButtonBase
                      component={Stack}
                      sx={{
                        border: currentTab === TabTitle.SELF_ASSESSMENT ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                        backgroundColor: currentTab === TabTitle.SELF_ASSESSMENT ? "#E3DDFF" : "",
                        borderRadius: "5px",
                        padding: "15px",
                        justifyContent: "center",
                        alignItems: "center",
                        flex: 1,
                        minWidth: 0,
                        transition: "background-color 0.3s",
                        "&:hover": {
                          backgroundColor: currentTab === TabTitle.SELF_ASSESSMENT ? "#D9D0FF" : "#F5F5F5",
                        },
                      }}
                      onClick={() => setCurrentTab(TabTitle.SELF_ASSESSMENT)}
                      disableRipple={false}
                      centerRipple
                    >
                      <MLTypography
                        variant="body1"
                        fontSize={"14px"}
                        fontWeight={600}
                        textAlign={"center"}
                      >
                        Self Assessment
                      </MLTypography>
                    </ButtonBase>

                    {/* Expert Assessment Tab */}
                    <ButtonBase
                      component={Stack}
                      sx={{
                        border: currentTab === TabTitle.EXPERT_ASSESSMENTS ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                        backgroundColor: currentTab === TabTitle.EXPERT_ASSESSMENTS ? "#E3DDFF" : "",
                        borderRadius: "5px",
                        padding: "15px",
                        justifyContent: "center",
                        alignItems: "center",
                        flex: 1,
                        minWidth: 0,
                        transition: "background-color 0.3s",
                        "&:hover": {
                          backgroundColor: currentTab === TabTitle.EXPERT_ASSESSMENTS ? "#D9D0FF" : "#F5F5F5",
                        },
                      }}
                      onClick={() => setCurrentTab(TabTitle.EXPERT_ASSESSMENTS)}
                      disableRipple={false}
                      centerRipple
                    >
                      <MLTypography
                        variant="body1"
                        fontSize={"14px"}
                        fontWeight={600}
                        textAlign={"center"}
                      >
                        Expert Assessment
                      </MLTypography>
                    </ButtonBase>
                  </Stack>
                </Stack>

                <Stack>
                  {/* All Assessments Tab Panel */}
                  <TabPanel sx={{ p: 0, my: 0 }} value={TabTitle.ALL}>
                    <MLTypography fontSize={'24px'} my={'20px'} variant="h3" fontWeight={700}>
                      Self Assessments
                    </MLTypography>

                    {selfAssessment.length > 0 ? (selfAssessment.map(caseItem => (
                      <MLAssessmentCard
                        key={caseItem.id}
                        data={caseItem}
                        type="self"
                        riskLevel={riskLevel}
                        isAssessmentHistory={isAssessmentHistory} // Pass the new prop
                      />
                    ))) :
                      <StatusMessage
                        title="No Self-Assessments Found"
                        message={propEmployeeId
                          ? "This employee doesn't have any self-assessments yet."
                          : "You don't have any self-assessments yet. They will appear here once you've started or completed them."}
                        type="info"
                      />
                    }

                    {expertAssessment.length > 0 && (
                      <Stack mt={2}>
                        <Box sx={{
                          display: { sm: 'flex', md: 'none' },
                        }} borderBottom="0.5px solid #9C9C9C" />

                        <MLTypography fontSize={'24px'} my={'20px'} variant="h3" mt={2.5} mb={2.2} fontWeight={700}>
                          Expert Assessments
                        </MLTypography>

                        {expertAssessment.map((caseItem) => (
                          <MLAssessmentCard
                            key={caseItem.id}
                            data={caseItem}
                            type="expert"
                            riskLevel={riskLevel}
                            isAssessmentHistory={isAssessmentHistory} // Pass the new prop
                          />
                        ))}

                      </Stack>
                    )}
                  </TabPanel>

                  {/* Self Assessments Tab Panel */}
                  <TabPanel sx={{ p: 0, my: 0 }} value={TabTitle.SELF_ASSESSMENT}>
                    <MLTypography fontSize={'24px'} my={'20px'} variant="h3" fontWeight="bold">
                      Self Assessments
                    </MLTypography>
                    {selfAssessment.length > 0 ? (
                      selfAssessment.map((caseItem) => (
                        <MLAssessmentCard
                          key={caseItem.id}
                          data={caseItem}
                          type="self"
                          riskLevel={riskLevel}
                          isAssessmentHistory={isAssessmentHistory} // Pass the new prop
                        />
                      ))
                    ) : (
                      <StatusMessage
                        title="No Self-Assessments Found"
                        message={propEmployeeId
                          ? "This employee doesn't have any self-assessments yet."
                          : "You don't have any self-assessments yet. They will appear here once you've started or completed them."}
                        type="info"
                      />
                    )}
                  </TabPanel>

                  {/* Expert Assessments Tab Panel */}
                  <TabPanel sx={{ p: 0, my: 0 }} value={TabTitle.EXPERT_ASSESSMENTS}>
                    <MLTypography fontSize={'24px'} variant="h3" my={'20px'} fontWeight={700}>
                      Expert Assessments
                    </MLTypography>
                    {expertAssessment.length > 0 ? (
                      expertAssessment.map((caseItem) => (
                        <MLAssessmentCard
                          key={caseItem.id}
                          data={caseItem}
                          type="expert"
                          riskLevel={riskLevel}
                          isAssessmentHistory={isAssessmentHistory} // Pass the new prop
                        />
                      ))
                    ) : (
                      <StatusMessage
                        title="No Expert Assessments Found"
                        message={propEmployeeId
                          ? "This employee doesn't have any expert assessments yet."
                          : "You don't have any expert assessments yet. They will appear here once they've been assigned to you."}
                        type="info"
                      />
                    )}
                  </TabPanel>
                </Stack>
              </TabContext>
            </Stack>
          )
          }
        </MLContainer>
      </Stack>
    </>
  );
};

export default MyAssessments;