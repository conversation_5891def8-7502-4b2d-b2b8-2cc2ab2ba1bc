import { Box } from "@mui/material";
import MLTypography from "../../components/ui/MLTypography/MLTypography";

// Helper function for consistent date sorting
export const sortByUpdatedDate = <T extends { updatedAt?: string; createdAt?: string }>(items: T[] | undefined): T[] => {
    if (!items || items.length === 0) return [];

    return [...items].sort((a, b) => {
        const dateA = new Date(a.updatedAt || a.createdAt || 0).getTime();
        const dateB = new Date(b.updatedAt || b.createdAt || 0).getTime();
        return dateB - dateA; // Descending order - newest first
    });
};

// User-friendly message component that matches the app's design system
export const StatusMessage: React.FC<{
    title: string;
    message: string;
    type?: 'error' | 'warning' | 'info';
}> = ({ title, message, type = 'info' }) => {
    // Background colors based on message type
    const backgroundColors = {
        error: '#FEE7E6',
        warning: '#FFF8E6',
        info: '#E3DDFF'
    };

    return (
        <Box
            sx={{
                backgroundColor: backgroundColors[type],
                borderRadius: '8px',
                padding: '24px',
                width: '100%',
                textAlign: 'center',
                maxWidth: '650px',
                mt: '15px'
                // margin: '10px auto'
            }}
        >
            <MLTypography variant="h4" fontWeight={700} mb={1} fontSize={{ xs: "20px", sm: "24px" }}>
                {title}
            </MLTypography>
            <MLTypography variant="body1" fontSize={{ xs: "14px", sm: "16px" }}>
                {message}
            </MLTypography>
        </Box>
    );
};