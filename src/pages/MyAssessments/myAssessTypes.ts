// types.ts
export enum ExpertAssessmentStatus {
    CREATED = "created",
    ASSIGNED = "assigned",
    SCHEDULED = "scheduled",
    ASSESSMENT_IN_PROGRESS = "assessment_in_progress",
    ASSESSMENT_COMPLETED = "assessment_completed",
    REPORT_CREATED = "report_created",
    PENDING_APPROVAL = "pending_approval",
    REPORT_SHARED = "report_shared",
    FOLLOW_UP = "follow_up",
    CLOSED = "closed",
    DELETED = "deleted",
  }
  
  export enum TabTitle {
    ALL = "All Assessments",
    SELF_ASSESSMENT = "Self Assessments",
    EXPERT_ASSESSMENTS = "Expert Assessments",
  }
  
  export interface AssessmentAssignedTo {
    id: number;
    username: string;
    email: string;
    provider: string;
    confirmed: boolean;
    archive: boolean;
    blocked: boolean;
    createdAt: string;
    phone: string | null;
    resetRequired: boolean | null;
    timezone: string | null;
    updatedAt: string;
    vendor: string | null;
  }
  
  export interface SelfAssessmentData {
    id: number;
    fullName: string;
    countryCode: string | null;
    contactNo: string | null;
    email: string;
    assessmentType: string;
    assessmentProgress: number;
    currentPage: string;
    isCompleted: boolean;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    workSetup: any;
    workHabit: any;
    discomfort: any;
    isAiAssessment?: boolean;
    selfAssessmentReport?: any
  }
  
  export interface ExpertAssessmentData {
    id: number;
    mode: string;
    type: string;
    status: string;
    notes: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    closeTime: string | null;
    closeReason: string | null;
    deleteReason: string | null;
    followUpMode: string | null;
    followUpNotes: string | null;
    followUpStatus: string | null;
    followUpUpdatedAt: string | null;
    assessmentAssignedTo?: AssessmentAssignedTo;
    assessment: any;
  }
  

 export interface MyAssesTabItem {
    title: TabTitle;
    count: number;
  }
  