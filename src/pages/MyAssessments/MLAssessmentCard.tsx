import calendarshortIcon from "../../assets/icons/calendarshortIcon.svg";
import scheduleShortIcon from "../../assets/icons/scheduleShortIcon.svg";
import usershortProfileIcon from "../../assets/icons/usershortProfileIcon.svg";
import MLTypography from "../../components/ui/MLTypography/MLTypography";
import StatusBadgeAssess, { formatStatusText } from "./StatusBadgeAssess";
import { ExpertAssessmentData, SelfAssessmentData } from "./myAssessTypes";
import { Box, LinearProgress, Stack, useMediaQuery } from "@mui/material";
import dayjs from "dayjs";
import React from "react";
import { useGo } from "@refinedev/core";
import { capitalizeFirstLetter, formatRelativeTime } from "../../utils/colorCodeUtils";
import MLButton from "../../components/ui/MLButton/MLButton";

interface MLAssessmentCardProps {
    data: SelfAssessmentData | ExpertAssessmentData;
    type: "self" | "expert";
    riskLevel?: string;
    isAssessmentHistory?: boolean; // New prop to identify if card is in Assessment History
}

const MLAssessmentCard: React.FC<MLAssessmentCardProps> = ({ data, type, riskLevel, isAssessmentHistory = false }) => {
    const go = useGo();
    const isSelfAssessment = type === "self";
    const selfData = data as SelfAssessmentData;
    const expertData = data as ExpertAssessmentData;
    const isMobile = useMediaQuery("(max-width:600px)");

    const isSelfAssessCompleted = isSelfAssessment && selfData.isCompleted;
    // to show the flag or case type ai or survey based
    const isAiAssessment = selfData?.isAiAssessment;
    const showProgress = isSelfAssessment && !isSelfAssessCompleted;

    // Check if report exists
    const hasReport = isSelfAssessment &&
        selfData.selfAssessmentReport?.generatedReport;

    // Determine if card should be clickable based on the specified logic
    // 1. Expert assessments are always clickable
    // 2. Survey-based assessments (isAiAssessment = false) are always clickable
    // 3. Only disable the card if: it's a self assessment AND it's AI-based AND isCompleted is false AND selfAssessmentReport is null
    const disableCardClick =
        isSelfAssessment &&
        isAiAssessment &&
        !selfData.isCompleted &&
        !selfData.selfAssessmentReport;

    const isCardClickable = !disableCardClick;

    const getRiskLevelColor = (riskLevel: string) => {
        switch (riskLevel?.toLowerCase()) {
            case 'high':
                return '#C40000';
            case 'medium':
                return '#FF7A00';
            case 'low':
                return '#FFC700';
            default:
                return '#C40000';
        }
    };

    const handleViewCase = () => {
        if (type === "expert") {
            go({ to: `/cases/show/${data.id}` });
        }
    };

    const handlescheduleAssessment = () => {
        if (type === "self" && isCardClickable) {
            go({ to: `/self-assessment/${data.id}` });
        }
    };

    const handleViewReport = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent card click event
        if (type === "self" && hasReport) {
            go({ to: `/assessments-list/report/${data.id}` });
        } else if (type === "expert" && expertData.assessment?.report) {
            go({ to: `/cases/report/${data.id}` });
        }
    };

    // Card click handler that checks if the card is clickable
    const handleCardClick = () => {
        if (!isCardClickable) {
            return; // Do nothing if card shouldn't be clickable
        }

        if (type === "expert") {
            handleViewCase();
        } else {
            handlescheduleAssessment();
        }
    };

    if (isMobile) {
        return (
            <Box
                onClick={handleCardClick}
                sx={{
                    border: "1px solid #E5E7EB",
                    borderRadius: "8px",
                    cursor: isCardClickable ? "pointer" : "default",
                    mb: 2,
                    p: '20px',
                    backgroundColor: "white",
                    position: "relative",
                    opacity: isCardClickable ? 1 : 0.7, // Visual indication of non-clickable state
                }}
            >
                <Stack direction="row" justifyContent="space-between" alignItems="center" mb={1}>
                    <MLTypography
                        variant="body2"
                        sx={{
                            backgroundColor: "#DDDDDD",
                            px: 1.5,
                            py: 0.5,
                            borderRadius: "4px",
                            fontSize: "14px",
                            fontWeight: 500,
                            color: "#374151"
                        }}
                    >
                        Case no: {isSelfAssessment ? "SA-" : "EA-"}{data.id}
                    </MLTypography>
                    <StatusBadgeAssess
                        status={isSelfAssessment
                            ? (isSelfAssessCompleted ? "completed" : "in_process")
                            : expertData.status
                        }
                    />
                </Stack>

                {
                    isSelfAssessment && <Stack display={'inline-block'} width={'auto'} mt={1}>
                        <StatusBadgeAssess
                            status={
                                isSelfAssessment
                                    ? isAiAssessment
                                        ? "AI"
                                        : "Survey"
                                    : expertData.status
                            }
                        />
                    </Stack>
                }

                {/* Progress bar for self assessment in-progress */}
                {showProgress && (
                    <Box sx={{ mt: 2 }}>
                        <MLTypography
                            variant="body2"
                            sx={{
                                fontSize: "12px",
                                fontWeight: 500,
                                mb: 0.5
                            }}
                        >
                            Progress {selfData.assessmentProgress}%
                        </MLTypography>
                        <LinearProgress
                            variant="determinate"
                            value={selfData.assessmentProgress}
                            sx={{
                                height: 8,
                                backgroundColor: "#E5E7EB",
                                borderRadius: "8px",
                                "& .MuiLinearProgress-bar": {
                                    backgroundColor: "#22C55E",
                                    borderRadius: "8px",
                                },
                            }}
                        />
                    </Box>
                )}

                {/* For Expert Assessment: Risk Level & Type */}
                {type === "expert" && (
                    <Stack direction="row" alignItems="center" gap={2} sx={{ mt: 2 }}>
                        <MLTypography
                            sx={{
                                backgroundColor: getRiskLevelColor(riskLevel || ''),
                                color: "white",
                                px: 2,
                                py: 0.5,
                                borderRadius: "16px",
                                fontSize: "14px",
                                width: "fit-content"
                            }}
                        >
                            {capitalizeFirstLetter(riskLevel || '')} Risk
                        </MLTypography>
                        <Stack direction="row" alignItems="center" spacing={1}>
                            <Box
                                sx={{
                                    width: "10px",
                                    height: "10px",
                                    borderRadius: "50%",
                                    backgroundColor: "#FFC700"
                                }}
                            />
                            <MLTypography sx={{ fontSize: "14px", color: "#374151" }}>
                                {formatStatusText(expertData.type)}
                            </MLTypography>
                        </Stack>
                    </Stack>
                )}

                {/* For Self Assessment: Risk Level (when completed) */}
                {(isSelfAssessment && isSelfAssessCompleted) && (
                    <Box sx={{ mt: 2 }}>
                        <MLTypography
                            sx={{
                                backgroundColor: getRiskLevelColor(riskLevel || ''),
                                color: "white",
                                px: 2,
                                py: 0.5,
                                borderRadius: "16px",
                                fontSize: "14px",
                                width: "fit-content"
                            }}
                        >
                            {capitalizeFirstLetter(riskLevel || '')} Risk
                        </MLTypography>
                    </Box>
                )}

                {/* "View Report" button for completed assessments with reports - only in Assessment History */}
                {isAssessmentHistory && ((isSelfAssessment && isSelfAssessCompleted && hasReport) ||
                    (type === "expert" && expertData.assessment?.report)) && (
                        <Box sx={{ mt: 2, textAlign: "left" }}>
                            <MLButton
                                variant="contained"
                                color="primary"
                                size="medium"
                                onClick={handleViewReport}
                                sx={{ fontSize: "12px" }}
                            >
                                View Report
                            </MLButton>
                        </Box>
                    )}

                {/* Footer information */}
                <Stack spacing={1} sx={{ mt: 2 }}>
                    {type === "expert" && expertData.assessmentAssignedTo && (
                        <MLTypography sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: 1,
                            color: "#6B7280",
                            fontSize: "12px",
                        }}>
                            <img src={usershortProfileIcon} width={16} height={16} alt="user" />
                            Assigned to: {expertData.assessmentAssignedTo.username}
                        </MLTypography>
                    )}
                    <MLTypography sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                        color: "#6B7280",
                        fontSize: "12px",
                    }}>
                        <img src={calendarshortIcon} width={16} height={16} alt="calendar" />
                        Created on {dayjs(data.createdAt).format("MMM DD, YYYY")}
                    </MLTypography>
                    <MLTypography sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                        color: "#6B7280",
                        fontSize: "12px",
                    }}>
                        <img src={scheduleShortIcon} width={16} height={16} alt="schedule" />
                        updated {formatRelativeTime(data.updatedAt)}
                    </MLTypography>
                </Stack>
            </Box>
        );
    }

    // Desktop layout
    return (
        <Box
            onClick={handleCardClick}
            sx={{
                border: "0.5px solid #9C9C9C",
                borderRadius: "10px",
                cursor: isCardClickable ? "pointer" : "default",
                mb: '20px',
                position: "relative",
                p: isSelfAssessment ? 2.5 : 3,
                px: 3,
                opacity: isCardClickable ? 1 : 0.7, // Visual indication of non-clickable state
            }}
        >
            <Box>
                <Stack
                    direction="row"
                    spacing={2}
                    alignItems="center"
                    mb={1.5}
                    gap={0.5}
                >
                    <MLTypography
                        variant="body1"
                        sx={{
                            backgroundColor: "#DDDDDD",
                            px: 2,
                            py: 0.5,
                            borderRadius: "5px",
                            fontSize: "16px",
                            fontWeight: 600,
                        }}
                    >
                        Case no: {isSelfAssessment ? "SA-" : "EA-"}
                        {data.id}
                    </MLTypography>
                    {
                        isSelfAssessment && <StatusBadgeAssess
                            status={
                                isSelfAssessment
                                    ? isAiAssessment
                                        ? "AI"
                                        : "Survey"
                                    : expertData.status
                            }
                        />
                    }
                    <StatusBadgeAssess
                        status={
                            isSelfAssessment
                                ? isSelfAssessCompleted
                                    ? "completed"
                                    : "in_process"
                                : expertData.status
                        }
                    />

                </Stack>

                {/* Show risk level only for completed self assessments or expert assessments */}
                {((isSelfAssessment && isSelfAssessCompleted) || type === "expert") && (
                    <MLTypography
                        sx={{
                            backgroundColor: getRiskLevelColor(riskLevel || ''),
                            color: "white",
                            px: 2,
                            py: 0.5,
                            borderRadius: "16px",
                            fontSize: "14px",
                            width: "fit-content",
                            mt: "10px",
                        }}
                    >
                        {capitalizeFirstLetter(riskLevel || '')} Risk
                    </MLTypography>
                )}

                {/* "View Report" button for completed assessments with reports - only in Assessment History */}
                {isAssessmentHistory && ((isSelfAssessment && isSelfAssessCompleted && hasReport) ||
                    (type === "expert" && expertData.assessment?.report)) && (
                        <Box sx={{ position: "absolute", top: 24, right: showProgress ? 140 : 24 }}>
                            <MLButton
                                variant="contained"
                                color="primary"
                                size="medium"
                                onClick={handleViewReport}
                            >
                                View Report
                            </MLButton>
                        </Box>
                    )}

                {showProgress ? (
                    <Box
                        sx={{
                            position: "absolute",
                            top: 24,
                            right: 24,
                            display: "flex",
                            alignItems: "center",
                            flexDirection: "column",
                            gap: "6px",
                        }}
                    >
                        <MLTypography
                            sx={{
                                fontSize: "14px",
                                fontWeight: 500,
                                textAlign: "right",
                                mr: 1,
                            }}
                        >
                            Progress {selfData.assessmentProgress}%
                        </MLTypography>
                        <Box sx={{ width: 100 }}>
                            <LinearProgress
                                variant="determinate"
                                value={selfData.assessmentProgress}
                                sx={{
                                    height: 9,
                                    backgroundColor: "#E5E7EB",
                                    borderRadius: "14px",
                                    "& .MuiLinearProgress-bar": {
                                        backgroundColor: "#22C55E",
                                        borderRadius: "4px",
                                    },
                                }}
                            />
                        </Box>
                    </Box>
                ) : (
                    type === "expert" && (
                        <Box
                            sx={{
                                position: "absolute",
                                top: 24,
                                right: 24,
                                display: "flex",
                                alignItems: "center",
                                gap: 3,
                            }}
                        >
                            <MLTypography
                                component={'div'}
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                    color: "#374151",
                                    fontSize: "14px",
                                }}
                            >
                                <Box
                                    sx={{
                                        width: "12px",
                                        height: "12px",
                                        borderRadius: "50%",
                                        backgroundColor: "#FFC700",
                                    }}
                                />
                                {formatStatusText(expertData.type)}
                            </MLTypography>
                        </Box>
                    )
                )}

                <Box sx={{ mt: "7px" }}>
                    <Stack
                        direction="row"
                        justifyContent="flex-end"
                        alignItems="center"
                        spacing={2.5}
                        sx={{ mt: isSelfAssessment ? 0 : 2 }}
                    >
                        {type === "expert" && expertData.assessmentAssignedTo && (
                            <MLTypography
                                component={'div'}
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                    color: "#6B7280",
                                    fontSize: "14px",
                                    mr: "auto",
                                }}
                            >
                                <img src={usershortProfileIcon} width={16} height={16} alt="user" />
                                Assigned to: {expertData.assessmentAssignedTo.username}
                            </MLTypography>
                        )}
                        <Stack direction="row" spacing={2} alignItems="center">
                            <MLTypography sx={{ display: "flex", alignItems: "center", gap: 1, color: "#6B7280", fontSize: "14px" }}>
                                <img src={calendarshortIcon} width={16} height={16} alt="calendar" />
                                Created on {dayjs(data.createdAt).format("MMM DD, YYYY")}
                            </MLTypography>
                            <MLTypography sx={{ display: "flex", alignItems: "center", gap: 1, color: "#6B7280", fontSize: "14px" }}>
                                <img src={scheduleShortIcon} width={16} height={16} alt="schedule" />
                                updated {formatRelativeTime(data.updatedAt)}
                            </MLTypography>
                        </Stack>
                    </Stack>
                </Box>
            </Box>
        </Box>
    );
};

export default MLAssessmentCard;